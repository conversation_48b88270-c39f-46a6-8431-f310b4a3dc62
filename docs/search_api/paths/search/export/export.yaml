post:
  tags:
  - Search Results
  summary: Export search results
  description: Exports search results into a file (PDF, Excel or CSV)
  parameters:
    - $ref: '#/components/parameters/search_hash'
    - $ref: '#/components/parameters/sort_by'
    - $ref: '#/components/parameters/sort_order'
    - name: format
      in: query
      schema:
        type: string
        enum: [CSV, EXCEL, PDF]
      description: Desired output format. Alternatively, can be specified as 'Accept' header
      required: true
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            search_filters:
              $ref: '#/components/schemas/SemanticSearchFilters'
            patent_documents_ids:
              type: array
              items:
                type: integer
              description: IDs of documents to include in the exported file. If not given, all search results will be included. If search hash is omitted, this field becomes mandatory.
              example: [37996214, 35311927, 9306480]
            charts:
              type: array
              description: Chart data to include in PDF report (optional)
              items:
                type: object
                required:
                  - key
                  - title
                  - img_base64
                properties:
                  key:
                    description: Chart identifier
                    type: string
                    example: topapplicants
                  title:
                    description: Chart title
                    type: string
                    example: Top applicants
                  img_base64:
                    description: Base64-encoded jpeg/png data of the chart
                    type: string
                    example: PHN2ZyB2ZXJzaW9uPSIxLjEiI...
            title:
              type: string
              description: Title of the exported PDF report, if different from default
            subtitle:
              type: string
              description: Subtitle of the PDF report
            layout:
              type: string
              enum: [patentfit, default, monitor, landscape]
              description: Layout to apply to the PDF report, if different from default
  responses:
    200:
      description: Binary data with export file
      content:
        text/csv:
          schema:
            type: string
            format: binary
        application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
          schema:
            type: string
            format: binary
        application/pdf:
          schema:
            type: string
            format: binary
    401:
      $ref: '#/components/responses/Unauthorized'
    404:
      $ref: '#/components/responses/NotFound'
    410:
      $ref: '#/components/responses/Gone'
