from typing import Callable
from .profile import LandscapeProfileModel, LandscapeProfileSchema, LandscapeProfileDocumentModel, SearchType, \
    LandscapeProfileStatus


def from_collection(user_id: int, collection, load_patent_numbers: Callable,
                    profile_name: str = None, profile_category: str = None):
    patent_numbers = load_patent_numbers([r.document_id for r in collection.get_results()])
    profile = LandscapeProfileModel(
        name=collection.name if profile_name is None else profile_name,
        category=collection.description if profile_category is None else profile_category,
        status=LandscapeProfileStatus.DRAFT,
        processed_input={
            "invalid": [],
            "not_found": [],
            "valid": patent_numbers
        },
        user_id=user_id
    )
    documents = collection.get_results()
    for r in documents:
        profile.add_document(LandscapeProfileDocumentModel(document_id=r.document_id,
                                                           source=SearchType.PATENT_NUMBER))
    profile.refresh_portfolio_stats()
    profile.save()
    return LandscapeProfileSchema().dump(profile)
