from typing import Dict
from octimine_common.app_request import AppRequest
from octimine_common.util import Page, Sort
from datetime import datetime
from app.extensions import db
from app.models.aliases.applicants import ApplicantAliasSchema
from app.models.read_document.read_document import ReadDocumentModel
from app.services import search


class DocumentHelper:

    @staticmethod
    def read_document(req: AppRequest, document_id: int) -> Dict:
        family = search.get_family(payload=dict(family_id=document_id))
        DocumentHelper.mark_document_as_read(req, document_id)
        return family.as_dict()

    @staticmethod
    def get_read_documents(req: AppRequest, page: Page, sort: Sort, show: search.Show, user_id: int) -> Dict:
        query = ReadDocumentModel.filter_by_user_id(user_id=user_id)
        if sort.sort_column == "created_at":
            if sort.sort_order == "asc":
                query = query.order_by(ReadDocumentModel.created_at.asc())
            else:
                query = query.order_by(ReadDocumentModel.created_at.desc())
            sort = None
        page.total_hits = query.count()
        results = query.all()
        document_ids = [o.document_id for o in results]
        if not document_ids:
            return {'data': {'documents': []}}

        return {
            'data': search.search_by_document_ids(
                req=req.replace(
                    payload=dict(
                        query=dict(
                            documents_ids=document_ids,
                            applicant_aliases=ApplicantAliasSchema.load_all(user_id),
                        ),
                        show=show,
                        sort=sort,
                        page=page
                    )
                ),
            ).as_dict()
        }

    @staticmethod
    def unread_documents(user_id: int):
        ReadDocumentModel.query.filter_by(user_id=user_id).delete()
        db.session.commit()

    @staticmethod
    def unread_document(user_id: int, document_id: int):
        ReadDocumentModel.query.filter_by(user_id=user_id).filter_by(document_id=document_id) \
            .delete()
        db.session.commit()

    @staticmethod
    def unread_multiple_documents(user_id: int, document_ids: list[int]):
        ReadDocumentModel.query.filter_by(user_id=user_id).filter(ReadDocumentModel.document_id.in_(document_ids)) \
            .delete(synchronize_session=False)
        db.session.commit()

    @staticmethod
    def apply_read_document_status(user_id: int, response_dict: Dict) -> None:
        documents = response_dict.get('data', {}).get('documents', [])
        document_ids = list(set([o.get('general', {}).get('docdb_family_id') for o in documents]))

        if len(document_ids) > 0:
            read_documents = ReadDocumentModel.get_user_read_documents_ids(user_id, document_ids)

            for doc in response_dict['data']['documents']:
                doc_id = doc.get('general', {}).get('docdb_family_id')
                doc['last_read'] = read_documents.get(doc_id, None)

    @staticmethod
    def mark_document_as_read(req: AppRequest, document_id: int) -> None:
        document = ReadDocumentModel.find_one(req.requester.user_id, document_id)
        if not document:
            rd = ReadDocumentModel(
                last_read=datetime.now(),
                user_id=req.requester.user_id,
                company_id=req.requester.company_id,
                document_id=document_id
            )
            db.session.add(rd)
        else:
            document.last_read = datetime.now()
        db.session.commit()
