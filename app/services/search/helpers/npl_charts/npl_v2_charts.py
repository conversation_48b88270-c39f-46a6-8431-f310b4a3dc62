from collections import defaultdict
from typing import Dict, List
import numpy as np  # needed for histogram calculation in the recency_indication chart.

from app.services.search.responses import Paper


def technology_timeline(papers, parameters: Dict):
    """
    Calculate data for "Technology Timeline" chart.
    This method will count the number of patents per year.
    """
    results = defaultdict(lambda: {'count': 0})
    for paper in papers:
        year = paper.year or None
        if not year:
            continue
        results[year] = {'count': results[year]['count'] + 1}
    sorted_tuples = sorted([(k, v) for k, v in results.items()], key=lambda x: x[0])
    return dict(total=len(papers), datasource=dict(xAxis=[k for k, v in sorted_tuples],
                                                   yAxis=[v['count'] for k, v in sorted_tuples]))


def top_technological_fields(papers: List, parameters: Dict) -> Dict:
    """
    Calculate data for "Technological Fields" chart
    Algorithm:
        - Step 1: get all TechnologyField of papers which will be called all_fields
        - Step 2: get the distinct fields of all_fields which will be known as category of all_fields.
        - Step 3: count the number of fields for each category from all_fields. Then storing in results variable.
    """
    all_fields = []
    for paper in papers:
        fields = paper.fields_of_study or None
        if fields:
            all_fields.extend(fields)

    all_fields = [i['category'] for i in all_fields]

    results = defaultdict(lambda: 0)

    for field in all_fields:
        results[field] += 1
    return dict(datasource=[{'name': k, 'y': v} for k, v in results.items()])


def top_authors(papers, parameters: Dict):
    """
    Calculate data for "Top authors" chart
    """
    quantity = parameters.get('npl_top_authors_quantity', 5)
    results = defaultdict(lambda: 0)
    papers_without_authors_count = 0
    for paper in papers:
        authors = _get_authors(paper)
        for author in authors:
            if author:
                results[author] += 1
        if not authors:
            papers_without_authors_count += 1

    sorted_tuples = sorted([(k, v) for k, v in results.items()], key=lambda x: x[1], reverse=True)
    top_tuples = sorted_tuples[:quantity]
    others_count = 0
    for paper in papers:
        authors = _get_authors(paper)
        for author in authors:
            if not any([author_top for author_top in top_tuples if author in author_top]):
                others_count += 1
                break

    if others_count > 0:
        top_tuples += [("Others", others_count)]
    if papers_without_authors_count > 0:
        top_tuples += [("N/A", papers_without_authors_count)]
    total_count = len(papers)
    return dict(datasource=[{"name": item[0], "absolute": item[1], "y": round(item[1] * 100 / total_count, 2)}
                            for item in top_tuples],
                top=[item[0] for item in sorted_tuples[:quantity]], total=total_count)


def _get_authors(paper: Paper) -> List[str]:
    authors = [a['name'] for a in (paper.authors or []) if a]
    return list(set(authors))


def citations_references(papers, parameters: Dict) -> Dict:
    """Calculate data for "citations_references" bar chart
    note:
        - citing=citation, cited=reference
    Returns:
        citation and reference counts, both are list of integers.
        Example:
            "referenceCount": [
                    -16,
                    -3,
                    0,
                ],
            "citationCount": [
                16,
                2,
                7,
                ]
    """
    bins = [0, 1, 2, 3, 4, 5, 6, 11, 21, 31, 51, 100, 99999]

    citing_list = []
    cited_list = []
    for paper in papers:
        # papers that have no citation counts will be default to 0
        citing_list.append(paper.citation_count or 0)
        cited_list.append(paper.reference_count or 0)
    citation_count = []
    reference_count = []
    if len(citing_list) > 0 and len(cited_list) > 0:
        citation_count = list(map(int, np.histogram(citing_list, bins)[0]))
        reference_count = list(map(int, -np.histogram(cited_list, bins)[0]))
    return dict(referenceCount=reference_count, citationCount=citation_count)
