import re
import logging
import math
from enum import Enum
from collections import Counter
from dataclasses import dataclass
from typing import Iterator
from octimine_common.exceptions import APIException
from octimine_common.app_request import AppRequest
from octimine_common.util import Page
from app.services.search import SearchResultsPaginator
from app.models.monitor.machine_learning import MachineLearningDatasetEntryModel, \
    MachineLearningDatasetEntrySource, MachineLearningDatasetEntryMatch
from app.services import search


logger = logging.getLogger('monitor.machine_learning.dataset.preparer')


class DatasetPreparationException(Exception):
    pass


class MLDocumentSource(Enum):
    POSITIVE_USER = "positive_user"
    POSITIVE_CITATIONS = "positive_citations"
    NEGATIVE_USER = "negative_user"
    NEGATIVE_IPCS = "negative_ipcs"


@dataclass
class DocumentParams:
    search_document: search.Family
    source: MLDocumentSource


@dataclass
class Dataset:
    examples: Iterator[dict]
    top_positive_ipcs: list[str]


class DatasetPreparer:
    """
    This class performs dataset augmentation based on an initial set of examples provided
    by the user (original dataset).

    Preparation includes:
    * Augmenting thumbs-up (positive) examples based on initial set + feedback
    * Augmenting thumbs-down (negative) examples based on positive examples + feedback
    """

    _NEGATIVE_POSITIVE_RATIO = 10  # for each positive example, there should be 10 negative

    def __init__(self,  original_dataset: list[MachineLearningDatasetEntryModel]):
        self._original_dataset = original_dataset

    def exec(self) -> Dataset:
        """ Prepares the dataset.
        Training examples from the dataset are returned as a generator to optimize memory footprint
        """
        logger.info("Starting dataset preparation")
        try:
            logger.debug("Gathering positive inputs")
            positive_inputs = self._gather_positive_inputs()
            logger.debug("Gathering negative inputs")
            top_positive_ipcs = self._get_top_ipcs(positive_inputs)
            negative_inputs = self._gather_negative_inputs(positive_inputs, top_positive_ipcs)
        except APIException as e:
            logger.exception(f"Search API returned error {e.code} while gathering inputs")
            raise DatasetPreparationException("Error while gathering dataset from our databases")

        def _generate_examples():
            yield from self._build_training_examples(inputs=positive_inputs, label=1)
            yield from self._build_training_examples(inputs=negative_inputs, label=0)

        logger.info(f"Finishing dataset preparation - "
                    f"prospects: positive={len(positive_inputs)} / negative={len(negative_inputs)}")
        return Dataset(examples=_generate_examples(), top_positive_ipcs=top_positive_ipcs)

    def _gather_positive_inputs(self) -> list[DocumentParams]:
        """
        Initial documents from original dataset might not be enough to train a model. That's why we extend them here.
        In addition to the already provided examples via profile configuration + positive feedback,
        We include as well X citations for such documents
        """
        positive_family_ids = [
            e.document_id for e in self._original_dataset
            if e.match == MachineLearningDatasetEntryMatch.POSITIVE
            or e.source != MachineLearningDatasetEntrySource.FEEDBACK
        ]
        positive_families = self._get_families_with_bibliographic_info(positive_family_ids)
        positive_families_x_citations = self._get_x_citations([
            f.family_id
            for f in positive_families
            if f.citations_forward_count + f.citations_forward_count <= 100
        ])
        positive_families_source_list = [DocumentParams(item, MLDocumentSource.POSITIVE_USER)
                                         for item in positive_families]
        positive_families_x_citations_source_list = [DocumentParams(item, MLDocumentSource.POSITIVE_CITATIONS)
                                                     for item in positive_families_x_citations]
        positive_inputs = (positive_families_source_list + positive_families_x_citations_source_list)
        if not positive_inputs:
            raise DatasetPreparationException("No positive inputs found while preparing dataset")
        return positive_inputs

    def _get_families_with_bibliographic_info(self, family_ids: list[int]) -> list[search.Family]:
        def _fetch_page(page):
            return search.search_by_document_ids(
                req=AppRequest.from_ctx().replace(
                    payload=dict(
                        query=dict(documents_ids=family_ids),
                        page=page,
                        show=search.Show(bibliographic=True)
                    )
                ),
            )

        return SearchResultsPaginator.fetch_all(lambda page: _fetch_page(page), page_size=1000).documents

    def _get_x_citations(self, positive_family_ids: list[int]) -> list[search.Family]:
        chunk_size = 1  # Neo4j queries with big chunks seem to be really slow, causing timeout errors in search API
        chunks = [positive_family_ids[i: i + chunk_size] for i in range(0, len(positive_family_ids), chunk_size)]
        result = []
        for chunk in chunks:
            logger.debug(f'sending chunk {chunk} to citation search endpoint')
            response = search.search_for_citations(
                req=AppRequest.from_ctx().replace(
                    payload=dict(
                        query=dict(
                            docdb_family_ids=chunk,
                            level=1,
                            direction="bidirectional",
                            citation_category='X'
                        )
                    )
                ),
            )
            try:
                result.extend(response.documents)
            except APIException:
                logger.exception("Error when calling citations search")
        return result

    def _gather_negative_inputs(self, positive_inputs: list[DocumentParams],
                                top_positive_ipcs: list[str]) -> list[DocumentParams]:
        """
        There is no way for users to configure profiles with thumbs-down documents from the beginning. Those are
        provided via feedback after generating some results with an already trained model.
        That's why we try to create this set based on
        * Feedback from the user if any
        * Documents that are in the same IPC4 hierarchy, but excluding most common IPCs from the positive set
        """
        negative_families = search.Family.from_list([
            {'general': {'docdb_family_id': e.document_id}}
            for e in self._original_dataset
            if e.match == MachineLearningDatasetEntryMatch.NEGATIVE
        ])
        ipc4_list = self._extract_ipc4(top_positive_ipcs)
        max_size = len(positive_inputs) * self._NEGATIVE_POSITIVE_RATIO
        negative_families_from_ipcs = self._get_patent_families_in_ipc4_list(
            ipc4_list,
            excluding_ipcs=top_positive_ipcs,
            max_results=max_size
        )
        negative_families_source_list = [DocumentParams(item, MLDocumentSource.NEGATIVE_USER)
                                         for item in negative_families]
        negative_families_from_ipcs_source_list = [DocumentParams(item, MLDocumentSource.NEGATIVE_IPCS)
                                                   for item in negative_families_from_ipcs]
        negative_inputs = (negative_families_source_list + negative_families_from_ipcs_source_list)[:max_size]
        if not negative_inputs:
            raise DatasetPreparationException("No negative inputs found while preparing dataset")
        return negative_inputs

    @staticmethod
    def _get_top_ipcs(positive_inputs: list[DocumentParams]) -> list[str]:
        counter = Counter([ipc for r in positive_inputs for ipc in r.search_document.ipc_codes])
        top_positive_ipcs = [ipc for ipc, _ in counter.most_common()]
        top_positive_ipcs = DatasetPreparer._filter_out_ipcs_with_unknown_pattern(top_positive_ipcs)
        if not top_positive_ipcs:
            raise DatasetPreparationException("No top positive ipcs found while preparing dataset")
        logger.info(f'total positive IPCs gathered: {len(top_positive_ipcs)}')
        return top_positive_ipcs

    @staticmethod
    def _filter_out_ipcs_with_unknown_pattern(ipcs):
        """It seems that we have in our database IPCs for which we don't support its format.
        This method will filter those out
        """
        pattern = re.compile(r'^[A-Z]\d{0,2}[A-Z*](\d{0,3}\**)(/[\d*]+)?$')
        return [ipc for ipc in ipcs if pattern.match(ipc)]

    @staticmethod
    def _extract_ipc4(top_ipcs: list[str]) -> list[str]:
        return list(set([ipc[:4] for ipc in top_ipcs]))

    def _get_patent_families_in_ipc4_list(self, ipc4_list: list[str], *, excluding_ipcs: list[str],
                                          max_results: int) -> list[search.Family]:
        results_per_ipc4 = min(10000, math.ceil(max_results / len(ipc4_list)))
        families = []
        for ipc4 in ipc4_list:
            related_ipcs = [ipc for ipc in excluding_ipcs if ipc.startswith(ipc4)]
            logger.debug(f'ipc4={ipc4}, total related ipcs to exclude={len(related_ipcs)}')

            chunk_size = 100  # we split in chunks to avoid max depth recursion in search API boolean query parsing
            chunks = [
                f'NOT(IPC=({" OR ".join(related_ipcs[i: i + chunk_size])}))'
                for i in range(0, len(related_ipcs), chunk_size)
            ]

            response = search.search_by_boolean_query(
                req=AppRequest.from_ctx().replace(
                    payload=dict(
                        # some IPCs are being delivered from DW with lowercase letters, won't work in boolean query
                        # TODO: Lets clean that up once we have this problem solved in data-pipeline
                        query=dict(
                            search_input=f'(IPC4=({ipc4})) AND {" AND ".join(chunks)}'.upper(),
                            max_results=results_per_ipc4,
                        ),
                        show=search.Show(), page=Page(disabled=True)
                    ),
                    internal=True
                ),
            )
            families.extend(response.documents)
        return families

    def _build_training_examples(self, *, inputs: list[DocumentParams], label: int) -> Iterator[dict]:
        chunk_size = 100
        total_examples = 0
        chunks = [inputs[i: i + chunk_size] for i in range(0, len(inputs), chunk_size)]
        for chunk in chunks:
            preprocessed_texts = self._get_preprocessed_text(chunk)
            for document_param in chunk:
                preprocessed_text = preprocessed_texts.get(document_param.search_document.family_id)
                if not preprocessed_text:
                    continue
                yield {
                    "docdb_family_id": document_param.search_document.family_id,
                    "title": preprocessed_text.stemmed_title,
                    "abstract": preprocessed_text.stemmed_abstract,
                    "description": preprocessed_text.stemmed_description,
                    "claims": preprocessed_text.stemmed_claims,
                    "label": label,
                    "source": document_param.source.value
                }
                total_examples += 1
        if not total_examples:
            raise DatasetPreparationException(f'No training examples for label={label}')
        logger.info(f'total training examples for label={label}: {total_examples}')

    @staticmethod
    def _get_preprocessed_text(inputs: list[DocumentParams]) -> dict[int, search.PreprocessedText]:
        try:
            return search.get_preprocessed_text(
                payload=search.PreprocessedTextRequest(
                    documents_ids=[sd.search_document.family_id for sd in inputs],
                )
            )
        except APIException:
            logger.exception("Error while fetching preprocessed info from search API")
            raise DatasetPreparationException("Error while gathering preprocessed data from our databases")
