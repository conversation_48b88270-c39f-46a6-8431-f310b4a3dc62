# Third party imports
# Standard lib imports
from http import HTTPStatus

from flask import request, make_response
from flask_jwt_extended import jwt_required
from flask.views import MethodView

from app.models.company import CompanyFilterSchema, CompanySchema, CompanyModel
# Local imports
from octimine_common.exceptions import BadRequestException
from octimine_common.util import Page, Sort
from octimine_common.utils.models.filter import Filters
from octimine_common.utils.routes.authorization import Authorization
from octimine_common.utils.routes.response import create_response, wrap_many

request_exclude_fields = ['id', 'created_at', 'updated_at']
response_exclude_fields = ['default_feature_ids']


class Companies(MethodView):

    @jwt_required()
    @Authorization.roles_required(any_roles=['is_admin'])
    def get(self):
        is_page_disabled = request.args.get('load_all', type=int, default=0) == 1
        page = None if is_page_disabled else Page.from_request(request)
        sort = Sort.from_request(request)
        if not sort.is_defined():
            sort.sort_column = "id"
            sort.sort_order = "desc"
        filters = Filters(CompanyFilterSchema)
        companies = CompanySchema(many=True).dump(CompanyModel.get_all(page, sort, filters))
        return create_response(wrap_many("companies", companies, page))

    @jwt_required()
    @Authorization.roles_required(any_roles=['is_admin'])
    def post(self):
        company = CompanySchema(exclude=request_exclude_fields).load(request.get_json(force=True))
        company.save()
        return create_response(CompanySchema(exclude=response_exclude_fields).dump(company), HTTPStatus.CREATED)


class CompaniesId(MethodView):
    _exclude = ['id', 'created_at', 'updated_at']

    @jwt_required()
    @Authorization.roles_required(any_roles=['is_admin'])
    def get(self, company_id: int):
        company = CompanyModel.get_one(id=company_id)
        return create_response(CompanySchema().dump(company))

    @jwt_required()
    @Authorization.roles_required(any_roles=['is_admin'])
    def patch(self, company_id: int):
        company = (CompanySchema(exclude=request_exclude_fields, partial=True).
                   load(request.get_json(force=True), instance=CompanyModel.get_one(id=company_id)))
        company.save()
        return create_response(CompanySchema(exclude=response_exclude_fields).dump(company))

    @jwt_required()
    @Authorization.roles_required(any_roles=['is_admin'])
    def delete(self, company_id: int):
        company = CompanyModel.get_one(id=company_id)
        if len(company.users) > 0:
            raise BadRequestException()
        company.delete()
        return make_response('', HTTPStatus.NO_CONTENT)
