# Third party imports
from flask_jwt_extended import jwt_required
from flask.views import MethodView

# Local imports
from app.extensions import statistics


class Statistics(MethodView):
    @jwt_required()
    def get(self):
        search_cache = statistics.get_cached_search()
        if search_cache:
            return search_cache
        else:
            search_results = statistics.perform_statistics_search()
            return search_results
