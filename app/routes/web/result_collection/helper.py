from octimine_common.app_request import AppRequest
from octimine_common.exceptions import ForbiddenException
from app.services.collection.access import can_remove_results_from_collection
from app.models.result_collection.collection import CollectionModel
from app.services.collection.results import _SEARCH_HASH_SOURCE_TYPES, _SEARCH_HISTORY_MODELS


def complete_source_from_search_hash(user_id: int, search_hash: str):
    if not search_hash:
        return None
    source_type = _SEARCH_HASH_SOURCE_TYPES.get(search_hash[0])
    model_class = _SEARCH_HISTORY_MODELS.get(source_type)
    return model_class.get_last_by_search_hash(user_id, search_hash) if model_class else None


def remove_results_from_collection(app_request: AppRequest, collection: CollectionModel, remove_request):
    if not can_remove_results_from_collection(app_request.requester, collection,
                                              remove_request.get('document_ids'),
                                              remove_request.get('publication_numbers')):
        raise ForbiddenException("You cannot remove the documents requested from this collection")
    collection.remove_results(remover_id=app_request.requester.user_id,
                              document_ids=remove_request.get("document_ids", None),
                              publication_numbers=remove_request.get("publication_numbers", None))
