from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Iterable


class MachineLearningClientException(Exception):
    pass


@dataclass
class DatasetParams:
    internal_id: str
    examples: Iterable[dict]


@dataclass
class DatasetInfo:
    details: dict


@dataclass
class TrainingParams:
    internal_id: str
    dataset: DatasetInfo


class TrainingStatus(str, Enum):
    PENDING = 'PENDING'
    RUNNING = 'RUNNING'
    READY = 'READY'
    ERROR = 'ERROR'


class PredictionSetupStatus(str, Enum):
    PENDING = 'PENDING'
    RUNNING = 'RUNNING'
    READY = 'READY'
    ERROR = 'ERROR'


@dataclass
class TrainingInfo:
    status: TrainingStatus
    details: dict


@dataclass
class SetupPredictionParams:
    internal_id: str
    training: TrainingInfo


@dataclass
class PredictionInfo:
    status: PredictionSetupStatus
    details: dict


@dataclass
class ResourceInfo:
    internal_id: str | None = None
    dataset: DatasetInfo | None = None
    training: TrainingInfo | None = None
    prediction: PredictionInfo | None = None

    @staticmethod
    def from_dict(data: dict):
        return ResourceInfo(
            dataset=DatasetInfo(**data.get('dataset')) if data.get('dataset') else None,
            training=TrainingInfo(**data.get('training')) if data.get('training') else None,
            prediction=PredictionInfo(**data.get('prediction')) if data.get('prediction') else None,
        )

    def to_dict(self):
        return {
            'dataset': asdict(self.dataset) if self.dataset else None,
            'training': asdict(self.training) if self.training else None,
            'prediction': asdict(self.prediction) if self.prediction else None
        }


@dataclass
class CloneResourcesParams:
    source_resource_info: ResourceInfo
    destination_internal_id: str


@dataclass
class PredictionOutputEntry:
    family_id: int
    publication_number: str
    value: float


@dataclass
class PredictionParams:
    internal_id: str
    run_id: int
    prediction: PredictionInfo
    prediction_dates: list[str]


@dataclass
class PredictionOutput:
    outputs: Iterable[PredictionOutputEntry]


class MachineLearningClient(ABC):
    """
    Abstraction class that contains all possible interactions required for setting up and using a machine learning
    model for predictions.
    """

    @abstractmethod
    def create_dataset(self, params: DatasetParams) -> DatasetInfo:
        """
        Contains logic about creating a dataset for training.
        :param params: Whatever we need for creating the dataset
        :return: Metadata related to the dataset created (dataset URI, id, etc...)
        """
        pass

    @abstractmethod
    def schedule_training(self, params: TrainingParams) -> TrainingInfo:
        """
        Contains logic for scheduling model training. This method is expected to be async, so caller is not blocked.

        It should return all metadata needed to poll the training status via `check_training_status`
        (resource URI, id, etc...)
        :param params: Whatever we need to start training a model (model template URI, dataset URI,
        :return: Metadata generated from the scheduling process
        """
        pass

    @abstractmethod
    def check_training_status(self, params: TrainingInfo) -> TrainingInfo:
        """
        Allows checking the status of a training job
        :param params: Whatever is needed to figure out the status (training job URI, model id, etc...)
        :return: training status
        """
        pass

    @abstractmethod
    def setup_prediction(self, params: SetupPredictionParams) -> PredictionInfo:
        """
        Should contain all logic for setting up a trained model for prediction
        Should fail if called and model is not ready for predictions (Still not trained, etc...)
        :param params: Whatever is required to set up predictions (model URI, id, etc...)
        :return: Operation details that need to be stored for prediction
        """
        pass

    @abstractmethod
    def predict(self, params: PredictionParams) -> PredictionOutput:
        """
        Executes prediction
        :param params: Whatever is required to execute prediction (model URI or id, input params, etc...)
        :return: Prediction output
        """
        pass

    @abstractmethod
    def clone_resources(self, params: CloneResourcesParams) -> ResourceInfo:
        """
        Makes a copy of the ML resources
        :param params: Whatever is required to clone prediction model (e.g. model URI, destination, etc...)
        :return: Operation details
        """
        pass

    @abstractmethod
    def drop_resources(self, resources: ResourceInfo):
        """
        Allows removing several resources (Dataset, model, etc...) in one go.
        Use cases:
            * Cleaning all existing external assets associated to a profile being deleted/re-trained
        :param resources: Whatever info we need to remove the assets
        """
        pass
