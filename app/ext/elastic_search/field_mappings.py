import numbers
import random
import re
from enum import Enum
from http import HTTPStatus
from typing import List, Dict, Callable

from octimine_common.enums import ESDocumentType

from octimine_common.environmental import get_environmental_classification_codes_and_categories
from octimine_common.exceptions import APIException
from octimine_common.fields import PatentNumberField
from octimine_common.legal_status import format_legal_statuses
from app.ext.elastic_search.format.description import format_description
from app.ext.elastic_search.format.claims import format_claims


class ESFieldSection(Enum):
    GENERAL = 'general'
    BIBLIOGRAPHIC = 'bibliographic'
    ANALYTICS = 'analytics'
    FULL_TEXT = 'fulltext'
    TAGS = 'tags'


class ESFieldType(Enum):
    TEXT = 'text'
    PATENT_NUMBER = 'patent_number'
    KEYWORD = 'keyword'
    INTEGER = 'integer'
    FLOAT = 'float'
    DATE = 'date'
    OBJECT = 'object'


def _format_parties(parties: list[str] | None) -> list[str] | None:
    """Workaround for empty applicants/assignees/inventors after name harmonization"""
    if not parties:
        return parties
    return [p for p in parties if p]


def _normalize_patent_number(value):
    if isinstance(value, str):
        return PatentNumberField.get_presentation_value(value)
    elif isinstance(value, list):
        return [_normalize_patent_number(i) for i in value]


_FIELD_TYPE_FORMATTERS = {
    ESFieldType.PATENT_NUMBER: _normalize_patent_number
}


ESMainAreas = ['Electrical Engineering', 'Instruments', 'Chemistry', 'Mechanical Engineering', 'Other Fields']
ExtraFields = ['rank', 'similarity_index', 'original_numbers', 'original_number', 'original_number_normalized',
               'confidence_score', 'term_weights', 'green', 'owners', 'ultimate_owners']
ExtraFieldsSections = ['general', 'general', 'general', 'general', 'general',
                       'general', 'general', 'general', 'bibliographic', 'bibliographic']


class MappingError(APIException):
    def __init__(self, message):
        APIException.__init__(self, HTTPStatus.BAD_REQUEST, message)


class ESField:
    def __init__(self, name: str, es_name: str, section: ESFieldSection, field_type: ESFieldType, sortable: bool,
                 can_be_obfuscated: bool, formatter: Callable = None):
        self.name = name  # Field name as returned by the API
        self.es_name = es_name  # Field name in the elastic search
        self.section = section  # Which logical section does the field belong to
        self.field_type = field_type  # Field type
        self.sortable = sortable  # If documents can be sorted by this field
        self.can_be_obfuscated = can_be_obfuscated  # If field should be obfuscated for non-paying customers
        self.formatter = formatter or _FIELD_TYPE_FORMATTERS.get(self.field_type)

    def __str__(self):
        return self.name

    def format(self, value):
        return self.formatter(value) if self.formatter else value


class ESFieldMapping:
    """
    Description of fields in elastic search. Must be kept up to date with mappings in elastic search
    """

    def __init__(self, *, index: ESDocumentType, primary_key: str, fields: list[ESField], lexer_fields: list[str]):
        self._index = index
        self._pk = primary_key
        self._fields = fields
        self._lexer_fields = lexer_fields
        self._field_map = {f.name: f for f in fields}
        self._es_field_map = {f.es_name: f for f in fields}

    @property
    def primary_key(self):
        return self._pk

    @property
    def index(self) -> ESDocumentType:
        return self._index

    def _get_field(self, field_name: str) -> ESField:
        if field_name in self._field_map:
            return self._field_map[field_name]
        else:
            raise MappingError('Unknown field %s' % field_name)

    def get_field_by_es_name(self, es_name: str) -> ESField:
        return self._es_field_map.get(es_name)

    def es_field_name(self, field_name) -> str:
        return self._get_field(field_name).es_name

    def es_field_type(self, field_name) -> ESFieldType:
        return self._get_field(field_name).field_type

    def get_fields_of_type(self, field_type: ESFieldType) -> List[ESField]:
        return [f for f in self._fields if f.field_type == field_type]

    def get_fields(self, field_names: List[str] = None) -> List[ESField]:
        if not field_names:
            return self._fields
        return [f for f in self._fields if f.name in field_names]

    def get_fields_of_section(self, section: ESFieldSection) -> List[ESField]:
        return [f for f in self._fields if f.section == section]

    def sortable_field_names(self):
        return [f.name for f in self._fields if f.sortable]

    def obfuscated_field_names(self):
        return [f.name for f in self._fields if f.can_be_obfuscated]

    def get_lexer_mapping(self):
        return {k: self.es_field_name(k) for k in self._lexer_fields}

    def all_field_names(self):
        return self._field_map.keys()


FAMILIES = ESFieldMapping(
    index=ESDocumentType.FAMILY,
    primary_key='docdb_family_id',
    fields=[
        ESField('docdb_family_id', 'docdb_family_id', ESFieldSection.GENERAL, ESFieldType.INTEGER, False, True),
        ESField('rank', 'rank', ESFieldSection.GENERAL, ESFieldType.INTEGER, False, False),
        ESField('raw_publication_number', 'publn_no_rep', ESFieldSection.GENERAL, ESFieldType.PATENT_NUMBER, True,
                True),
        ESField('publication_number', 'publn_no', ESFieldSection.GENERAL, ESFieldType.KEYWORD, True, True),
        ESField('application_numbers', 'application_numbers', ESFieldSection.BIBLIOGRAPHIC,
                ESFieldType.PATENT_NUMBER, True, True),
        ESField('publication_authority', 'publn_auth', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('publication_kind', 'publn_kind', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, True),
        ESField('also_published_as', 'also_published_as', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.PATENT_NUMBER,
                False, True),
        ESField('publication_date', 'publn_date', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.DATE, True, False),

        ESField('priority_date', 'priority_date', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.DATE, True, False),
        ESField('cpc', 'cpc', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('cpc4', 'cpc4', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('ipc', 'ipc', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('ipc4', 'ipc4', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('applicants', 'applicants_ifi', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, True,
                formatter=_format_parties),
        ESField('applicants_original', 'applicants_original', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('inventors', 'inventors_octimine', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, True,
                formatter=_format_parties),
        ESField('inventors_original', 'inventors_harmonized', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('assignees', 'assignees_ifi', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True, formatter=_format_parties),
        ESField('assignees_original', 'assignees_original', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('owner_ids', 'owner_ids', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.INTEGER, True, True),
        ESField('title', 'title', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.TEXT, False, True),
        ESField('title_lang', 'title_lang', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, False, False),
        ESField('abstract', 'abstract', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.TEXT, False, True),
        ESField('abstract_lang', 'abstract_lang', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, False, False),
        ESField('claims_exist', 'claims_exist', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.INTEGER, False, False),
        ESField('description_exists', 'description_exists', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.INTEGER,
                False, False),
        ESField('tech_areas', 'tech_areas', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, False, False),
        ESField('tech_fields', 'tech_fields', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, False, False),
        ESField('authorities', 'authorities', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('legal_status', 'legal_statuses', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False,
                formatter=format_legal_statuses),
        ESField('citation_backward_count', 'citation_backward_count', ESFieldSection.ANALYTICS, ESFieldType.INTEGER,
                True, False),
        ESField('citation_backward_unique_count', 'citation_backward_unique_count', ESFieldSection.ANALYTICS,
                ESFieldType.INTEGER, True, False),
        ESField('citation_forward_count', 'citation_forward_count', ESFieldSection.ANALYTICS, ESFieldType.INTEGER,
                True, False),
        ESField('citation_forward_count_unique', 'citation_forward_count_unique', ESFieldSection.ANALYTICS,
                ESFieldType.INTEGER, True, False),
        ESField('technology_broadness', 'technology_broadness', ESFieldSection.ANALYTICS, ESFieldType.INTEGER,
                True, False),
        ESField('consistency', 'consistency', ESFieldSection.ANALYTICS, ESFieldType.FLOAT, True, False),
        ESField('impact', 'impact', ESFieldSection.ANALYTICS, ESFieldType.INTEGER, True, False),
        ESField('impact_top10', 'impact_top10', ESFieldSection.ANALYTICS, ESFieldType.INTEGER, True, False),
        ESField('impact_weighted', 'impact_weighted', ESFieldSection.ANALYTICS, ESFieldType.FLOAT, True, False),
        ESField('risk', 'risk', ESFieldSection.ANALYTICS, ESFieldType.INTEGER, True, False),
        ESField('risk_top10', 'risk_top10', ESFieldSection.ANALYTICS, ESFieldType.INTEGER, True, False),
        ESField('risk_weighted', 'risk_weighted', ESFieldSection.ANALYTICS, ESFieldType.FLOAT, True, False),
        ESField('recency', 'recency', ESFieldSection.ANALYTICS, ESFieldType.INTEGER, True, False),
        ESField('recency_top10', 'recency_top10', ESFieldSection.ANALYTICS, ESFieldType.INTEGER, True, False),
        ESField('recency_years', 'recency_years', ESFieldSection.ANALYTICS, ESFieldType.FLOAT, True, False),
        ESField('market_coverage', 'market_coverage', ESFieldSection.ANALYTICS, ESFieldType.INTEGER, True, False),
        ESField('market_coverage_gdp', 'market_coverage_GDP', ESFieldSection.ANALYTICS, ESFieldType.FLOAT,
                True, False),
        ESField('recency_octimine', 'recency_octimine', ESFieldSection.ANALYTICS, ESFieldType.FLOAT, True, False),
        ESField('technological_distance', 'technological_distance', ESFieldSection.ANALYTICS, ESFieldType.FLOAT,
                True, False),
        ESField('claims', 'claims', ESFieldSection.FULL_TEXT, ESFieldType.TEXT, False, True,
                formatter=format_claims),
        ESField('claims_lang', 'claims_lang', ESFieldSection.FULL_TEXT, ESFieldType.KEYWORD, False, False),
        ESField('description', 'description', ESFieldSection.FULL_TEXT, ESFieldType.TEXT, False, True,
                formatter=format_description),
        ESField('description_lang', 'description_lang', ESFieldSection.FULL_TEXT, ESFieldType.KEYWORD, False, False),
        ESField('arrived_at', 'arrived_at', ESFieldSection.GENERAL, ESFieldType.DATE, False, False),
        ESField('claims_publication_number', 'claims_publn_no', ESFieldSection.FULL_TEXT, ESFieldType.PATENT_NUMBER,
                False, True),
        ESField('description_publication_number', 'description_publn_no', ESFieldSection.FULL_TEXT,
                ESFieldType.PATENT_NUMBER, False, True),
        ESField('abstract_publication_number', 'abstract_publn_no', ESFieldSection.BIBLIOGRAPHIC,
                ESFieldType.PATENT_NUMBER, False, True),
        ESField('title_publication_number', 'title_publn_no', ESFieldSection.BIBLIOGRAPHIC,
                ESFieldType.PATENT_NUMBER, False, True),
        ESField('sep', 'sep', ESFieldSection.TAGS, ESFieldType.KEYWORD, False, True),
        ESField('tag', 'tag', ESFieldSection.TAGS, ESFieldType.INTEGER, False, True),
    ],
    lexer_fields=[
        'cpc', 'ipc', 'cpc4', 'ipc4', 'tech_areas', 'tech_fields',
        'applicants', 'inventors', 'assignees', 'owner_ids',
        'applicants_original', 'inventors_original', 'assignees_original',
        'title', 'abstract', 'claims', 'description',
        'authorities', 'also_published_as', 'application_numbers',
        'raw_publication_number', 'publication_authority', 'publication_kind',
        'publication_date', 'priority_date',
        'impact', 'risk', 'market_coverage', 'consistency', 'technology_broadness', 'recency', 'recency_years',
        'consistency', 'citation_backward_count', 'citation_forward_count', 'citation_backward_unique_count',
        'citation_forward_count_unique', 'legal_status', 'docdb_family_id', 'tag'
    ]
)

PUBLICATIONS = ESFieldMapping(
    index=ESDocumentType.PUBLICATION,
    primary_key='publication_number',
    fields=[
        ESField('publication_number', 'publication_number', ESFieldSection.GENERAL, ESFieldType.KEYWORD,
                False, True),
        ESField('application_number', 'application_number', ESFieldSection.BIBLIOGRAPHIC,
                ESFieldType.PATENT_NUMBER, True, True),
        ESField('application_date', 'application_date', ESFieldSection.BIBLIOGRAPHIC,
                ESFieldType.DATE, True, True),
        ESField('publication_authority', 'publn_auth', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('publication_kind', 'publn_kind', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, True),
        ESField('docdb_family_id', 'docdb_family_id', ESFieldSection.GENERAL, ESFieldType.INTEGER, False, True),
        ESField('publication_type', 'publication_type', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                False, True),
        ESField('publication_date', 'publn_date', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.DATE, True, False),
        ESField('priority_claims', 'priority_claims', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.OBJECT,
                False, True),
        ESField('cpc', 'cpc', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('cpc4', 'cpc4', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('ipc', 'ipc', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('ipc4', 'ipc4', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('applicants', 'applicants_ifi', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('applicants_original', 'applicants_original', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('inventors', 'inventors_octimine', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('inventors_original', 'inventors_harmonized', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('assignees', 'assignees_ifi', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('assignees_original', 'assignees_original', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, True),
        ESField('owner_ids', 'owner_ids', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.INTEGER, True, True),
        ESField('title', 'title', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.TEXT, False, True),
        ESField('abstract', 'abstract', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.TEXT, False, True),
        ESField('claims', 'claims', ESFieldSection.FULL_TEXT, ESFieldType.TEXT, False, True, formatter=format_claims),
        ESField('description', 'description', ESFieldSection.FULL_TEXT, ESFieldType.TEXT, False, True,
                formatter=format_description),
        ESField('tech_areas', 'tech_areas', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, False, False),
        ESField('tech_fields', 'tech_fields', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, False, False),
        ESField('authorities', 'authorities', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('legal_status', 'legal_status', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD, True, False),
        ESField('grant_legal_status', 'grant_legal_status', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                True, False),
        ESField('arrived_at', 'arrived_at', ESFieldSection.GENERAL, ESFieldType.DATE, False, False),
        ESField('sep', 'sep', ESFieldSection.TAGS, ESFieldType.KEYWORD, False, True),
        ESField('tag', 'tag', ESFieldSection.TAGS, ESFieldType.INTEGER, False, True),
        ESField('authority_legal_status', 'authority_legal_status', ESFieldSection.BIBLIOGRAPHIC, ESFieldType.KEYWORD,
                False, True),
        ESField('anticipated_expiration_date', 'anticipated_expiration_date', ESFieldSection.BIBLIOGRAPHIC,
                ESFieldType.DATE, True, False)
    ],
    lexer_fields=[
        'cpc', 'ipc', 'cpc4', 'ipc4', 'tech_areas', 'tech_fields',
        'applicants', 'inventors', 'assignees', 'owner_ids',
        'applicants_original', 'inventors_original', 'assignees_original',
        'title', 'abstract', 'claims', 'description',
        'application_number', 'publication_number',
        'publication_type', 'publication_date', 'application_date', 'legal_status', 'docdb_family_id', 'tag',
        'publication_kind', 'publication_authority', 'authorities'
    ]
)

PAPERS = ESFieldMapping(
    index=ESDocumentType.PAPER,
    primary_key='corpus_id',
    fields=[
        ESField('corpus_id', 'corpus_id', ESFieldSection.GENERAL, ESFieldType.KEYWORD,
                False, True),
        ESField('abstract', 'abstract', ESFieldSection.GENERAL,
                ESFieldType.KEYWORD, True, True)
    ],
    lexer_fields=[
        'corpus_id', 'abstract'
    ]
)

_FIELD_MAPPINGS = {
    ESDocumentType.FAMILY: FAMILIES,
    ESDocumentType.PUBLICATION: PUBLICATIONS
}


def get_field_mapping(index: ESDocumentType = None) -> ESFieldMapping:
    return _FIELD_MAPPINGS.get(index or ESDocumentType.FAMILY)


class ESFieldFormatter:
    """
    Utility class for converting fields and documents from ES representation in the API representation
    """

    def __init__(self, request=None, show_general=False, show_bibliographic=True, show_analytics=False,
                 show_fulltext=False, show_tags=False, index: ESDocumentType = ESDocumentType.FAMILY):
        self._field_mapping = get_field_mapping(index)
        if request:
            show_analytics = bool(request.args.get('show_analytics', type=int, default=0))
            show_bibliographic = bool(request.args.get('show_bibliographic', type=int, default=1))
            show_general = bool(request.args.get('show_general', type=int, default=0))
            show_fulltext = bool(request.args.get('show_fulltext', type=int, default=0))
            show_tags = bool(request.args.get('show_tags', type=int, default=0))
        self._sections = {
            ESFieldSection.ANALYTICS: show_analytics,
            ESFieldSection.BIBLIOGRAPHIC: show_bibliographic,
            ESFieldSection.GENERAL: show_general,
            ESFieldSection.FULL_TEXT: show_fulltext,
            ESFieldSection.TAGS: show_tags
        }

    @property
    def show_analytics(self):
        return bool(self._sections.get(ESFieldSection.ANALYTICS))

    @property
    def show_bibliographic(self):
        return bool(self._sections.get(ESFieldSection.BIBLIOGRAPHIC))

    @property
    def show_general(self):
        return bool(self._sections.get(ESFieldSection.GENERAL))

    @property
    def show_fulltext(self):
        return bool(self._sections.get(ESFieldSection.FULL_TEXT))

    @property
    def show_tags(self):
        return bool(self._sections.get(ESFieldSection.TAGS))

    def _obfuscate(self, value):
        if isinstance(value, str):
            value = re.sub(r'[A-Z]', 'X', value)
            value = re.sub(r'[0-9]', 'N', value)
            return re.sub(r'[a-z]', 'x', value)
        elif isinstance(value, numbers.Number):
            return random.randint(0, 999999)
        elif isinstance(value, list):
            return [self._obfuscate(i) for i in value]

    def es_source(self):
        """
        Return list of fields to be loaded from elastic search ("source" in the ES jargon)
        """
        fields = []
        for field in self._field_mapping.get_fields():
            if self._sections.get(field.section):
                fields.append(field.es_name)
        if self._field_mapping.primary_key not in fields:  # Check default property
            fields.append(self._field_mapping.primary_key)
        return fields

    def _obfuscate_document(self, doc: Dict) -> None:
        for section in doc:
            for field in doc[section]:
                if field in self._field_mapping.obfuscated_field_names():
                    doc[section][field] = self._obfuscate(doc[section][field])
        doc['general']['obfuscated'] = True

    def format_document(self, document: Dict):
        """
        Format single ES document, converting it into API representation
        """
        doc = {
            'general': {
                self._field_mapping.primary_key: document.get(self._field_mapping.primary_key)
            }
        }
        for section, enabled in self._sections.items():
            if not enabled:
                continue
            if section.value not in doc:
                doc[section.value] = {}
            for field in self._field_mapping.get_fields_of_section(section):
                doc[section.value][field.name] = field.format(document.get(field.es_name))

        for field, section in zip(ExtraFields, ExtraFieldsSections):
            if field in document and section in doc:
                doc[section][field] = document[field]
            elif field == 'green' and self._sections.get(ESFieldSection.TAGS):
                classification_codes = set(document.get(self._field_mapping.es_field_name('cpc')) or []) \
                    .union(set(document.get(self._field_mapping.es_field_name('ipc')) or []))
                green_code = get_environmental_classification_codes_and_categories(classification_codes)
                doc['general']['green_codes'] = green_code
                doc['tags']['green'] = len(green_code) > 0
        return doc

    def format_documents(self, documents: List, citations: Dict = None,
                         perform_obfuscation: bool = False) -> List[Dict]:
        """
        Format list of ES documents, converting them into API representation
        :param documents: list of ES hits to format
        :param citations: additional citation information dictionary to include if needed
        :param perform_obfuscation: whether to obfuscate certain results for free users
        """
        formed_documents = list()
        for i, doc in enumerate(documents):
            if '_source' in doc:
                doc = doc['_source']  # We want to support raw ES docs as well as already extracted sources
            document_id = doc.get(self._field_mapping.primary_key)
            citation_info = citations.get(str(document_id)) if citations else []

            formatted_document = self.format_document(doc)
            formed_documents.append(formatted_document)

            obfuscate = perform_obfuscation and documents and i < 3
            if obfuscate:
                self._obfuscate_document(formatted_document)

            if citations:
                formatted_document['citations'] = citation_info
        return formed_documents


class TechnologicalField:
    def __init__(self, label: str, main_area: str, main_area_label: str, abbreviation:  str):
        self.label = label
        self.main_area = main_area
        self.main_area_label = main_area_label
        self.abbreviation = abbreviation

    def __str__(self):
        return 'TechnologicalField(' + self.label + ', ' + self.main_area + ', ' + self.main_area_label + ')'


TECHNOLOGICAL_FIELDS = [
    TechnologicalField("Electrical Machinery, Apparatus, Energy", "ma1", "Electrical Engineering", "Electr. Mach."),
    TechnologicalField("Audio-Visual Technology", "ma1", "Electrical Engineering", "Audio-visual Tech."),
    TechnologicalField("Telecommunications", "ma1", "Electrical Engineering", "Telecomm."),
    TechnologicalField("Digital Communication", "ma1", "Electrical Engineering", "Digital Communic."),
    TechnologicalField("Basic Communication Processes", "ma1", "Electrical Engineering", "Basic Communic."),
    TechnologicalField("Computer Technology", "ma1", "Electrical Engineering", "Comp. Tech."),
    TechnologicalField("IT Methods for Management", "ma1", "Electrical Engineering", "IT Methods Managem."),
    TechnologicalField("Semiconductors", "ma1", "Electrical Engineering", "Semiconductor"),
    TechnologicalField("Optics", "ma2", "Instruments", "Optics"),
    TechnologicalField("Measurement", "ma2", "Instruments", "Measurement"),
    TechnologicalField("Analysis of Biological Materials", "ma2", "Instruments", "Anal. Biol. Mat."),
    TechnologicalField("Control", "ma2", "Instruments", "Control"),
    TechnologicalField("Medical Technology", "ma2", "Instruments", "Med. Tech."),
    TechnologicalField("Organic Fine Chemistry", "ma3", "Chemistry", "Org. Fine Chem."),
    TechnologicalField("Biotechnology", "ma3", "Chemistry", "Biotech"),
    TechnologicalField("Pharmaceuticals", "ma3", "Chemistry", "Pharmaceut."),
    TechnologicalField("Macromolecular Chemistry, Polymers", "ma3", "Chemistry", "Macromolecular. Chem."),
    TechnologicalField("Food Chemistry", "ma3", "Chemistry", "Food Chem."),
    TechnologicalField("Basic Materials Chemistry", "ma3", "Chemistry", "Basic Mat. Chem."),
    TechnologicalField("Materials, Metallurgy", "ma3", "Chemistry", "Mat., Metallurgy"),
    TechnologicalField("Surface Technology, Coating", "ma3", "Chemistry", "Surface Tech., Coat."),
    TechnologicalField("Micro-Structural and Nano-Technology", "ma3", "Chemistry", "Micro and Nanotech."),
    TechnologicalField("Chemical Engineering", "ma3", "Chemistry", "Chem. Engin."),
    TechnologicalField("Environmental Technology", "ma3", "Chemistry", "Environ. Tech."),
    TechnologicalField("Handling", "ma4", "Mechanical Engineering", "Handling"),
    TechnologicalField("Machine Tools", "ma4", "Mechanical Engineering", "Machine Tools"),
    TechnologicalField("Engines, Pumps, Turbines", "ma4", "Mechanical Engineering", "Engine, Pumps..."),
    TechnologicalField("Textile and Paper Machines", "ma4", "Mechanical Engineering", "Textile and Paper Mach."),
    TechnologicalField("Other Special Machines", "ma4", "Mechanical Engineering", "Other Spec. Machines"),
    TechnologicalField("Thermal Processes and Apparatus", "ma4", "Mechanical Engineering", "Thermal"),
    TechnologicalField("Mechanical Elements", "ma4", "Mechanical Engineering", "Mech. Elem."),
    TechnologicalField("Transport", "ma4", "Mechanical Engineering", "Transport"),
    TechnologicalField("Furniture, Games", "ma5", "Other Fields", "Furniture, Games"),
    TechnologicalField("Other Consumer Goods", "ma5", "Other Fields", "Other Cons. Goods"),
    TechnologicalField("Civil Engineering", "ma5", "Other Fields",  "Civil Engin.")
]


class TechnologicalFieldAbbreviation:
    def __init__(self, full: str, abbr: str):
        self.full = full
        self.abbr = abbr

    def __str__(self):
        return 'TechnologicalFieldAbbreviation(' + self.full + ', ' + self.abbr + ')'


TECHNOLOGICAL_FIELD_ABBREVIATIONS = [
    TechnologicalFieldAbbreviation("Electrical Machinery, Apparatus, Energy", "Electr. Mach."),
    TechnologicalFieldAbbreviation("Audio-Visual Technology", "Audio-visual Tech."),
    TechnologicalFieldAbbreviation("Telecommunications", "Telecomm."),
    TechnologicalFieldAbbreviation("Digital Communication", "Digital Communic."),
    TechnologicalFieldAbbreviation("Basic Communication Processes", "Basic Communic."),
    TechnologicalFieldAbbreviation("Computer Technology", "Comp. Tech."),
    TechnologicalFieldAbbreviation("IT Methods for Management", "IT Methods Managem."),
    TechnologicalFieldAbbreviation("Semiconductors", "Semiconductor"),
    TechnologicalFieldAbbreviation("Optics", "Optics"),
    TechnologicalFieldAbbreviation("Measurement", "Measurement"),
    TechnologicalFieldAbbreviation("Analysis of Biological Materials", "Anal. Biol. Mat."),
    TechnologicalFieldAbbreviation("Control", "Control"),
    TechnologicalFieldAbbreviation("Medical Technology", "Med. Tech."),
    TechnologicalFieldAbbreviation("Organic Fine Chemistry", "Org. Fine Chem."),
    TechnologicalFieldAbbreviation("Biotechnology", "Biotech"),
    TechnologicalFieldAbbreviation("Pharmaceuticals", "Pharmaceut."),
    TechnologicalFieldAbbreviation("Macromolecular Chemistry, Polymers", "Macromolecular. Chem."),
    TechnologicalFieldAbbreviation("Food Chemistry", "Food Chem."),
    TechnologicalFieldAbbreviation("Basic Materials Chemistry", "Basic Mat. Chem."),
    TechnologicalFieldAbbreviation("Materials, Metallurgy", "Mat., Metallurgy"),
    TechnologicalFieldAbbreviation("Surface Technology, Coating", "Surface Tech., Coat."),
    TechnologicalFieldAbbreviation("Micro-Structural and Nano-Technology", "Micro, Nanotech."),
    TechnologicalFieldAbbreviation("Chemical Engineering", "Chem. Engin."),
    TechnologicalFieldAbbreviation("Environmental Technology", "Environ. Tech."),
    TechnologicalFieldAbbreviation("Handling", "Handling"),
    TechnologicalFieldAbbreviation("Machine Tools", "Machine Tools"),
    TechnologicalFieldAbbreviation("Engines, Pumps, Turbines", "Engine, Pumps..."),
    TechnologicalFieldAbbreviation("Textile and Paper Machines", "Textile, Paper Mach."),
    TechnologicalFieldAbbreviation("Other Special Machines", "Other Spec. Machines"),
    TechnologicalFieldAbbreviation("Thermal Processes and Apparatus", "Thermal"),
    TechnologicalFieldAbbreviation("Mechanical Elements", "Mech. Elem."),
    TechnologicalFieldAbbreviation("Transport", "Transport"),
    TechnologicalFieldAbbreviation("Furniture, Games", "Furniture, Games"),
    TechnologicalFieldAbbreviation("Other Consumer Goods", "Other Cons. Goods"),
    TechnologicalFieldAbbreviation("Civil Engineering", "Civil Engin.")
]
