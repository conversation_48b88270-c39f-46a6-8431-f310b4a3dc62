{% extends 'mail/base.html' %}
{% block style %}
   span { background-color: #e7e7e7; color: #ffffff; font-weight: bold; padding: 3px 5px 3px 5px; border-radius: 3px; }
   th { font-size: 12px; text-align: left; }
   td { font-size: 12px; }
{% endblock %}
{% block content %}
   {% set active = 'background-color: #389a85;' %}
   <h1>
      {% if parameters.user_name  %}
      Dear {{ parameters.user_name }},
      {% else %}
      Dear Octimine user,
      {% endif %}
   </h1>
   <p>
      Below is a summary of the latest patent publications that match your monitor profile.
      To view and analyse the full results directly, click <a href="{{ parameters.url }}/result?runID={{ parameters.run_id }}">here</a>.
   </p>
   <br />
   <table width="100%" cellpadding="5">
      <tr>
         <td width="50%" valign="top" align="left">
            <h1>
               Monitoring Report
            </h1>
            <h2>
               {{ parameters.profile_name }}
            </h2>
            <p>
               {{ parameters.profile_frequency }}: {{ parameters.date_from }} - {{ parameters.date_to }}
            </p>
         </td>
         <td width="50%" valign="top" align="right">
            <table cellpadding="4">
               <tr>
                  <td colspan="2"><h3>Monitoring method</h3></td>
               </tr>
               <tr>
                  <td><span style="{% if 'SEMANTIC' in parameters.profile_types %}{{ active }}{% endif %}">S</span> Semantic:</td>
                  <td><b>{{ parameters.docs_by_type['SEMANTIC'] }}</b> results</td>
               </tr>
               <tr>
                  <td><span style="{% if 'MACHINE_LEARNING' in parameters.profile_types %}{{ active }}{% endif %}">DL</span> Deep learning:</td>
                  <td><b>{{ parameters.docs_by_type['MACHINE_LEARNING'] }}</b> results</td>
               </tr>
               <tr>
                  <td><span style="{% if 'BOOLEAN' in parameters.profile_types %}{{ active }}{% endif %}">B</span> Boolean:</td>
                  <td><b>{{ parameters.docs_by_type['BOOLEAN'] }}</b> results</td>
               </tr>
            </table>
         </td>
      </tr>
   </table>
   {% if parameters.results %}
   <table width="100%" cellpadding="5">
      <tr>
         <td colspan="6"><hr /></td>
      </tr>
      <tr>
         <td colspan="6">
            <h3>Result highlights - top {{ parameters.top }} out of {{ parameters.total }} results</h3>
         </td>
      </tr>
      <tr>
         <th width="10%">Type</th>
         <th width="15%">Publication number</th>
         <th width="30%">Title</th>
         <th width="15%" class="extra">IPC4</th>
         <th width="20%" class="extra">Applicants</th>
         <th width="10%" class="extra">Priority date</th>
      </tr>
      <tr>
         <td colspan="6"><hr /></td>
      </tr>
      {% for doc in parameters.results %}
      <tr>
         <td width="10%" valign="top">
            {% if 'SEMANTIC' in doc.snapshot_types %}<span style="{{ active }}">S</span>{% endif %}
            {% if 'MACHINE_LEARNING' in doc.snapshot_types %}<span style="{{ active }}">DL</span>{% endif %}
            {% if 'BOOLEAN' in doc.snapshot_types %}<span style="{{ active }}">B</span>{% endif %}
         </td>
         <th width="15%" valign="top">{{ doc.general.raw_publication_number or doc.general.publication_number }}</th>
         <td width="30%" valign="top">{{ doc.bibliographic.title }}</td>
         <td width="15%" valign="top" class="extra">
            {% if doc.bibliographic.ipc4 %}
            {{ doc.bibliographic.ipc4|join(', ') }}
            {% endif %}
         </td>
         <td width="20%" valign="top" class="extra">
            {% if doc.bibliographic.applicants %}
            {{ doc.bibliographic.applicants|join(', ') }}
            {% endif %}
         </td>
         <td width="10%" valign="top" class="extra">{{ doc.bibliographic.priority_date }}</td>
      </tr>
      <tr>
         <td colspan="6"><hr /></td>
      </tr>
      {% endfor %}
   </table>
   <p>
      You can see full results here: <a href="{{ parameters.url }}/result?runID={{ parameters.run_id }}">{{ parameters.url }}/result?runID={{ parameters.run_id }}</a>
   </p>
   {% else %}
   <hr >
   <p>
      No results have been generated for this profile.<br />
      Please click <a href="{{ parameters.url }}/setting">here</a> to check out your profile settings.
   </p>
   {% endif %}
{% endblock %}
