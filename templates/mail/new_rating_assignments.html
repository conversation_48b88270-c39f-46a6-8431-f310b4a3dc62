{% extends 'mail/base.html' %}

{% block style %}
span { background-color: #e7e7e7; color: #ffffff; font-weight: bold; padding: 3px 5px 3px 5px; border-radius: 3px; }
th { font-size: 12px; text-align: left; }
td { font-size: 12px; }
.abstract { display: -webkit-box !important; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 2; width: 100%; }
{% endblock %}

{% block content %}

<h1>
    {% if parameters.to_name %}
    Dear {{ parameters.to_name }},
    {% else %}
    Dear Octimine user,
    {% endif %}
</h1>

{% if parameters.to_group_id %}
    {% set view_all_filters = 'assigneed_group_ids=' ~ parameters.to_group_id ~ '&status=NEW,OPEN' %}
    {% set recipient = 'your team ' ~ parameters.to_group_name %}
{% else %}
    {% set view_all_filters = 'assigneed_user_ids=' ~ parameters.to_user_id ~ '&status=NEW,OPEN' %}
    {% set recipient = 'you'%}
{% endif %}

{% if parameters.list | length > 1%}
    <b>{{ parameters.from_name }} has requested ratings from {{recipient}} </b>
{% else %}
    <b>{{ parameters.from_name }} has requested a rating from {{recipient}} </b>
{% endif %}

    <table width="100%" cellpadding="5">
        {% for entry in parameters.list %}
            <tr>
                <td colspan="2"><hr/></td>
            </tr>
            <tr>
                <th width="150px" valign="top">
                    <a style="{{ link_style }}" href="{{ entry.task.url }}" target="_blank">
                        <b>{{ entry.task.document.raw_publication_number or entry.task.document.publication_number}}</b>
                    </a>
                </th>
                <td valign="top"><b>{{ entry.task.document.title }}</b></td>
            </tr>
            <tr>
                <td valign="top" colspan="2">
                    <div class="abstract">{{ entry.task.document.abstract }}</div>
                </td>
            </tr>

            {% if entry.task.topics %}
            <tr>
                <td width="50px" valign="top">Topic:</td>
                <td valign="top">{{ entry.task.topics }}</td>
            </tr>
            {% endif %}

            {% if entry.task.deadline %}
            <tr>
                <td width="50px" valign="top">Deadline:</td>
                <td valign="top">{{ entry.task.deadline }}</td>
            </tr>
            {% endif %}

            {% if entry.task.description %}
            <tr>
                <td width="50px" valign="top">Message:</td>
                <td valign="top">{{ entry.task.description }}</td>
            </tr>
            {% endif %}
        {% endfor %}
        <tr>
            <td colspan="2"><hr/></td>
        </tr>
    </table>


{% if parameters.email_representative.ratings_url %}
<a href="{{ parameters.email_representative.ratings_url }}?{{view_all_filters}}" style="color: #fff; background-color: #ff6700;padding: 12px 15px;text-decoration: none;margin-top: 5px;display: inline-block;">
      See all
   </a>
{% endif %}

{% endblock %}
