{% extends 'mail/base.html' %}
{% block content %}
<h1>Scheduled runs report - {{ parameters.environment }}</h1>
<br />
<table width="100%" cellpadding="5">
    <tr>
        <td>
            <table width="100%" cellpadding="5">
                <tr>
                    <td colspan="6"><hr /></td>
                </tr>
                <tr>
                    <td colspan="6">
                        <h3>Scheduled runs: {{ parameters.report.scheduled_runs }}</h3> Out of them:
                    </td>
                </tr>
                <tr>
                    <td colspan="6"><hr /></td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Completed</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_status.finished }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Processing</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_status.processing }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Failed</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_status.error }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Pending</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_status.pending }}</td>
                </tr>
                <tr>
                    <td colspan="6"><hr /></td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Paid user runs</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_user_type.paid }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Trial user runs</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_user_type.trial }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Free user runs</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_user_type.free }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Internal user runs</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_user_type.internal }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">External user runs</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_user_type.external }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Other user runs</td>
                    <td width="25%" valign="top">{{ parameters.report.runs_by_user_type.other }}</td>
                </tr>
                <tr>
                    <td colspan="6"><hr /></td>
                </tr>
                <tr>
                    <td colspan="6">
                        <h3>Total results generated: {{ parameters.report.results_by_type.overall.total }}</h3> Average results per run:
                    </td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Total</td>
                    <td width="25%" valign="top">{{ parameters.report.results_by_type.overall.avg_per_run }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Semantic</td>
                    <td width="25%" valign="top">{{ parameters.report.results_by_type.semantic.avg_per_run }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Boolean</td>
                    <td width="25%" valign="top">{{ parameters.report.results_by_type.boolean.avg_per_run }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Machine learning</td>
                    <td width="25%" valign="top">{{ parameters.report.results_by_type.machine_learning.avg_per_run }}</td>
                </tr>
                <tr>
                    <td width="25%" valign="top">Legal status</td>
                    <td width="25%" valign="top">{{ parameters.report.results_by_type.legal_status.avg_per_run }}</td>
                </tr>
            </table>
        </td>
    </tr>
</table>
{% endblock %}
