{% extends 'mail/base.html' %}

{% block style %}
span { background-color: #e7e7e7; color: #ffffff; font-weight: bold; padding: 3px 5px 3px 5px; border-radius: 3px; }
th { font-size: 12px; text-align: left; }
td { font-size: 12px; }
.abstract { display: -webkit-box !important; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 2; width: 100%; }
{% endblock %}

{% block content %}

<h1>
    {% if parameters.to_name %}
    Dear {{ parameters.to_name }},
    {% else %}
    Dear Octimine user,
    {% endif %}
</h1>

<b>Here are the ratings completed today</b>

<table width="100%" cellpadding="5">
    <tr>
        <td colspan="2"><hr/></td>
    </tr>
    {% for group in parameters.list | groupby('task.id') %}
        {% set task = group.list[0].task %}
        {% set users = group.list | selectattr('from_group_name', 'equalto', none) | map(attribute='from_name') | unique | list %}
        {% set groups = group.list | selectattr('from_group_name', 'ne', none) | map(attribute='from_group_name') | unique | list %}
        {% set completed_by = users + groups | unique | list %}
        {% set total_completed = group.list | length %}

        {% if total_completed == 1 %}
            {% set completed_label = '1 rating has been completed by' %}
        {% else %}
            {% set completed_label = '{} ratings have been completed by'.format(total_completed) %}
        {% endif %}
        <tr>
            <th width="150px" valign="top">
                <a style="{{ link_style }}" href="{{ task.url }}" target="_blank">
                    <b>{{ task.document.raw_publication_number or task.document.publication_number}}</b>
                </a>
            </th>
            <td valign="top"><b>{{ task.document.title }}</b></td>
        </tr>
        <tr>
            <td valign="top" colspan="2">
                <div class="abstract">{{ task.document.abstract }}</div>
            </td>
        </tr>
        <tr>
            <td valign="top" colspan="2">
            <b>{{ completed_label }}: 
                {% if completed_by | length > 2 %}
                    {{ completed_by[:2] | join(', ') }} and {{ (completed_by | length) - 2}} other(s)</b>
                {% else %}
                    {{ completed_by | join(', ') }}</b>
                {% endif %}
            </b>
            </td>
        </tr>
        <tr>
            <td colspan="2"><hr/></td>
        </tr>
    {% endfor %}
</table>


{% if parameters.email_representative.ratings_url %}
<a href="{{ parameters.email_representative.ratings_url }}?author_id={{parameters.to_user_id}}&status=DONE" style="color: #fff; background-color: #ff6700;padding: 12px 15px;text-decoration: none;margin-top: 5px;display: inline-block;">
      See all
   </a>
{% endif %}

{% endblock %}
