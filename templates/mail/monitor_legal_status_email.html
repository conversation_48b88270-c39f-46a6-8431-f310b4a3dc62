{% extends 'mail/base.html' %}
{% block style %}
   span { background-color: #e7e7e7; color: #ffffff; font-weight: bold; padding: 3px 5px 3px 5px; border-radius: 3px; }
   th { font-size: 12px; text-align: left; }
   td { font-size: 12px; }
{% endblock %}
{% block content %}
   {% set active = 'background-color: #389a85;' %}
   {% set status_styles = {
   "valid": 'background-color: #00d464',
   "invalid": 'background-color: #ff4d55',
   "unknown": 'background-color: #95a5ab;'
   }%}
   {% set general_status_display = {
   "valid": 'Valid',
   "invalid": 'Invalid',
   "unknown": 'Unknown'
   }%}
   {% set extended_status_display = {
   "active": 'Active',
   "expired": 'Expired',
   "pe": 'Unknown'
   }%}

   <h1>
      {% if parameters.user_name  %}
      Dear {{ parameters.user_name }},
      {% else %}
      Dear Octimine user,
      {% endif %}
   </h1>
   <p>
      Below is a summary of the latest changes in legal status for publications in your monitor profile.
      To view and analyse the full results directly, click <a href="{{ parameters.url }}{{ parameters.profile_id }}/result?runID={{ parameters.run_id }}">here</a>.
   </p>
   <br />
   <table width="80%" cellpadding="5">
      <tr>
         <td width="50%" valign="top" align="left">
            <h1>
               Monitoring Report
            </h1>
            <h2>
               {{ parameters.profile_name }}
            </h2>
            <p>
               {{ parameters.profile_frequency }}
            </p>
         </td>
         <td width="50%" valign="top" align="right">
            <table cellpadding="4">
               <tr>
                  <td colspan="2"><h3>Monitoring method</h3></td>
               </tr>
               <tr>
                  <td><span style="{{ active }}">LS</span> Legal Status:</td>
                  <td><b>{{ parameters.results|length }}</b> results</td>
               </tr>
            </table>
         </td>
      </tr>
   </table>
   {% if parameters.results %}
   <table width="100%" cellpadding="5">
      <tr>
         <td colspan="3"><hr /></td>
      </tr>
      <tr>
         <th width="40%">Publication number</th>
         <th width="30%" class="extra">Previous status</th>
         <th width="30%" class="extra">New status</th>
      </tr>
      <tr>
         <td colspan="3"><hr /></td>
      </tr>
      {% for doc in parameters.results %}
      <tr>
         <th width="40%" valign="top">{{ doc.publication_number }}</th>
         <td width="30%" valign="top"><span style="{{ status_styles.get(doc.previous_status.general) }}"><b>{{ doc.previous_status.general|title }}</b></span> - {{ doc.previous_status.extended|replace('_', ' ')|title}}</td>
         <td width="30%" valign="top"><span style="{{ status_styles.get(doc.current_status.general) }}"><b>{{ doc.current_status.general|title }}</b></span> - {{ doc.current_status.extended|replace('_', ' ')|title}}</td>
      </tr>
      <tr>
         <td colspan="3"><hr /></td>
      </tr>
      {% endfor %}
   </table>
   <p>
      You can see full results here: <a href="{{ parameters.url }}{{ parameters.profile_id }}/result?runID={{ parameters.run_id }}">{{ parameters.url }}{{ parameters.profile_id }}/result?runID={{ parameters.run_id }}</a>
   </p>
   {% else %}
   <hr >
   <p>
      There were no changes in legal status this time.<br />
   </p>
   {% endif %}
{% endblock %}