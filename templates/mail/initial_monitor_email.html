{% extends 'mail/base.html' %}
{% block style %}
   span { background-color: #e7e7e7; color: #ffffff; font-weight: bold; padding: 3px 5px 3px 5px; border-radius: 3px; }
   th { font-size: 12px; text-align: left; }
   td { font-size: 12px; }
{% endblock %}

{% block content %}
   {% set active = 'background-color: #389a85;' %}
   <h1>
      {% if parameters.user_name  %}
      Dear {{ parameters.user_name }},
      {% else %}
      Dear Octimine user,
      {% endif %}
   </h1>
   <p>
      Below is a summary of the latest patent publications that match your monitor profile for the last {{ parameters.profile_humanable_frequency }}.
      To view and analyse the full results directly, click <a href="{{ parameters.url }}/result">here</a>.
   </p>
   <br />
   <table width="100%" cellpadding="5" style="margin-bottom: 10px;">
      <tr>
         <td width="100%" valign="top" align="left">
            <h1>
               {% if parameters.monitor_run_frequency %}
               Initial monitoring report with {{ parameters.monitor_run_frequency }} data
               {% else %}
               Initial monitoring report with last {{ parameters.profile_humanable_frequency }} data
               {% endif %}
            </h1>
            <h2>
               <a href="{{ parameters.url }}/result">{{ parameters.profile_name }}</a>
            </h2>
         </td>
      </tr>
   </table>
   {% if parameters.results %}
   {% for results_per_run in parameters.results.values() %}
   <div style="border-top: 2px solid #808080; padding-top: 10px; padding-bottom: 10px;">
      <table width="100%" cellpadding="5"{% if results_per_run.index % 2 == 0 %} style="background-color: #f2f2f2;"{% endif %}>
         <tr>
            <td colspan="6">
               <a href="{{ parameters.url }}/result">
                  <h3>Top {{ results_per_run.parameters.top }} out of {{ results_per_run.parameters.total }} results for {{ results_per_run.parameters.date_from }} - {{ results_per_run.parameters.date_to }}</h3>
               </a>
            </td>
         </tr>
         <tr>
            <th width="10%">Type</th>
            <th width="15%">Publication number</th>
            <th width="30%">Title</th>
            <th width="15%" class="extra">IPC4</th>
            <th width="20%" class="extra">Applicants</th>
            <th width="10%" class="extra">Priority date</th>
         </tr>
         <tr>
            <td colspan="6"><hr style="background: #e6e6e6; border: 0.5px solid #e6e6e6" /></td>
         </tr>
         {% for doc in results_per_run.documents %}
         <tr>
            <td width="10%" valign="top">
               {% if 'SEMANTIC' in doc.snapshot_types %}<span style="{{ active }}">S</span>{% endif %}
               {% if 'MACHINE_LEARNING' in doc.snapshot_types %}<span style="{{ active }}">DL</span>{% endif %}
               {% if 'BOOLEAN' in doc.snapshot_types %}<span style="{{ active }}">B</span>{% endif %}
            </td>
            <th width="15%" valign="top">{{ doc.general.raw_publication_number or doc.general.publication_number }}</th>
            <td width="30%" valign="top">{{ doc.bibliographic.title }}</td>
            <td width="15%" valign="top" class="extra">
               {% if doc.bibliographic.ipc4 %}
               {{ doc.bibliographic.ipc4|join(', ') }}
               {% endif %}
            </td>
            <td width="20%" valign="top" class="extra">
               {% if doc.bibliographic.applicants %}
               {{ doc.bibliographic.applicants|join(', ') }}
               {% endif %}
            </td>
            <td width="10%" valign="top" class="extra">{{ doc.bibliographic.priority_date }}</td>
         </tr>
         {% endfor %}
      </table>
   </div>
   {% endfor %}
   <p>
      You can see full results here: <a href="{{ parameters.url }}/result">{{ parameters.url }}/results</a>
   </p>
   {% else %}
   <hr >
   <p>
      No results have been generated for this profile.<br />
      Please click <a href="{{ parameters.url }}/setting">here</a> to check out your profile settings.
   </p>
   {% endif %}
{% endblock %}
