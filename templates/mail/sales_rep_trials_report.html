{% extends 'mail/base.html' %}

{% block content %}

{% set table_heading_style = font_style + 'color: #000000; font-weight: bold; font-size: 12px;' %}
{% set table_body_style = font_style + 'font-size: 12px; text-align: center;' %}

<h1 style="{{ title_style }}">Dear {{ parameters.user_name }},</h1>
<p>
    This is your weekly <span style="{{ text_bold_style }}">Octimine</span> trial status report.
    Check out below how your prospects have used <span style="{{ text_bold_style }}">Octimine</span> so far
</p>
{% set valid_trials = [] %}
{% for entry in parameters.report_data %}
    {% if entry.days_left_in_trial is none or entry.days_left_in_trial > 0 %}
        {% set _ = valid_trials.append(entry) %}
    {% endif %}
{% endfor %}
{% set expired_trials = parameters.report_data | rejectattr("days_left_in_trial", "none") | rejectattr("days_left_in_trial", ">", 0) | list %}
{% if valid_trials | list | length > 0 %}
    <h2 style="{{ table_heading_style }} text-align: center;">Active trials</h2>
    <table width="100%">
        <tr style="{{ table_heading_style }}">
            <th>User email</th>
            <th>Days left in trial</th>
            <th>Searches this week</th>
            <th>Total searches</th>
        </tr>
        {% for entry in valid_trials %}
            <tr style="{{ table_body_style }}">
                <td><a style="{{ link_style }}" href="mailto:{{ entry.user_email }}">{{ entry.user_email if entry.user_email|length <= 25 else entry.user_email[:22] + '...'}}</a></td>
                <td>{{ entry.days_left_in_trial if entry.days_left_in_trial is not none else '-' }}</td>
                <td>{{ entry.this_week_searches_count }}</td>
                <td>{{ entry.all_searches_count }}</td>
            </tr>
        {% endfor %}
    </table>
{% endif %}
{% if expired_trials | list | length > 0 %}
    <h2 style="{{ table_heading_style }} text-align: center;">Trials finished in the last 30 days</h2>
    <table width="100%">
        <tr style="{{ table_heading_style }}">
            <th>User email</th>
            <th>Finished</th>
            <th>Total searches</th>
        </tr>
        {% for entry in expired_trials %}
            <tr style="{{ table_body_style }}">
                <td><a style="{{ link_style }}" href="mailto:{{ entry.user_email }}">{{ entry.user_email if entry.user_email|length <= 25 else entry.user_email[:22] + '...'}}</a></td>
                <td>{{ entry.days_left_in_trial | abs | string + " day(s) ago" if entry.days_left_in_trial < 0 else 'today' }}</td>
                <td>{{ entry.all_searches_count }}</td>
            </tr>
        {% endfor %}
    </table>
{% endif %}
{% endblock %}