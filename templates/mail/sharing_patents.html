{% extends 'mail/base.html' %}

{% block style %}
span { background-color: #e7e7e7; color: #ffffff; font-weight: bold; padding: 3px 5px 3px 5px; border-radius: 3px; }
th { font-size: 12px; text-align: left; }
td { font-size: 12px; }
.abstract { display: -webkit-box !important; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 2; width: 100%; }
{% endblock %}

{% block content %}

<h1>
    {% if parameters.to_name %}
    Dear {{ parameters.to_name }},
    {% else %}
    Dear Octimine user,
    {% endif %}
</h1>

{% if parameters.documents | length > 1 %}
    <b>{{ parameters.from_name }} shared the following patents with you. Open them in Octimine to review and collaborate. </b>
{% else %}
    <b>{{ parameters.from_name }} shared the following patent with you. Open them in Octimine to review and collaborate. </b>
{% endif %}

<table width="100%" cellpadding="5">
    <tr>
        <td colspan="3"><hr/></td>
    </tr>
    {% for doc in parameters.documents %}
        <tr>
            <th width="150px" valign="top">
                <a style="{{ link_style }}" href="{{ doc.url }}" target="_blank">
                    <b>{{ doc.general.raw_publication_number or doc.general.publication_number }}</b>
                </a>
            </th>
            <td valign="top"><b>{{ doc.bibliographic.title }}</b></td>
            <td align="center" rowspan="2" style="width: 150px;">
                {% if doc.main_image_cid %}
                    <img src="cid:{{ doc.main_image_cid }}" alt="{{ doc.general.raw_publication_number or doc.general.publication_number }}"
                         style="max-height: 150px; max-width: 150px;"/>
                {% else %}
                    No image available
                {% endif %}
            </td>
        </tr>
        <tr>
            <td valign="top" colspan="2">
                <div class="abstract">{{ doc.bibliographic.abstract }}</div>
            </td>
        </tr>
        <tr>
            <td colspan="3"><hr/></td>
        </tr>
    {% endfor %}
</table>


{% if parameters.url %}
    <a href="{{ parameters.url }}" style="color: #fff; background-color: #ff6700;padding: 12px 15px;text-decoration: none;margin-top: 5px;display: inline-block;">
        {% if parameters.documents | length > 1 %}
            View patents
        {% else %}
            View patent
        {% endif %}
    </a>
{% endif %}

{% endblock %}
