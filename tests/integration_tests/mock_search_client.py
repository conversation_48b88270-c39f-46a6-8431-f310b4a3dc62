import json
import sys
import uuid
import pika
from datetime import datetime

responses = {}

text = """
A self-driving car, also known as an autonomous car, driver-less car, or robotic car (robo-car),[1][2][3]
is a car incorporating vehicular automation, that is, a ground vehicle that is capable of sensing its environment
and moving safely with little or no human input.
"""


def run(rabbit_mq_url, exchange):
    message = {"user_id": 1,
               "text": text,
               "top_term_weights": 1000,
               "max_results": 1000
               }
    parameters = pika.connection.URLParameters(rabbit_mq_url)
    connection = pika.BlockingConnection(parameters)
    print("Connected to RabbitMQ")
    channel = connection.channel()
    channel.basic_consume(queue="amq.rabbitmq.reply-to", on_message_callback=on_response, auto_ack=True)
    channel.exchange_declare(exchange=exchange, exchange_type='fanout')
    corr_id = str(uuid.uuid4())  # Random id
    channel.basic_publish(
        exchange=exchange,
        routing_key='',
        properties=pika.BasicProperties(
            reply_to="amq.rabbitmq.reply-to",
            correlation_id=corr_id,
            content_type="application/json",
            priority=1,
            expiration=str(30 * 1000),  # expiration is in milliseconds
        ),
        body=json.dumps(message))
    print("Published message: " + json.dumps(message))

    # Keep checking until all producers have finished or an error occurs
    print("Waiting for response ...")
    while not is_any_response_complete():
        connection.process_data_events()
    connection.close()


def on_response(ch, method, props, body):
    print("Received response: " + body.decode())
    response = json.loads(body.decode())
    response_group = response['total_servers']
    chunk_id = response['server_number']
    if response_group not in responses:
        responses[response_group] = {}
    if chunk_id not in responses[response_group]:
        responses[response_group][chunk_id] = response["results"]


def is_any_response_complete():
    for response_group in responses:
        if is_response_group_completed(response_group):
            return True
    return False


def is_response_group_completed(response_group):
    return response_group in responses and len(responses[response_group]) == response_group


if __name__ == '__main__':
    start = datetime.now()
    run(sys.argv[1], f'octimine.engines.exchange.{sys.argv[2]}' if len(sys.argv) == 3 else 'octimine.engines.exchange')
    end = datetime.now()
    print(f'took {(end - start).total_seconds()} seconds')
