from http import HTTPStatus

from tests.integration_tests.base_integration_test import TestIntegration, family_id, publication_numbers


class TestIntegrationOcti(TestIntegration):

    def test_ask_octi_about_patent(self):
        chat_id = str(family_id)

        #  Make sure there's nothing in the chat before starting
        self.send_request(f'/web/patent/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        payload = {
            "family_id": family_id,
            "chat_id": chat_id,
            "text": "Summarize this patent"
        }
        resp = self.send_request('/web/patent/ask_octi', payload, 'post', HTTPStatus.OK)
        assert resp['data']['message'] != ""
        resp = self.send_request(f'/web/patent/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 2
        payload["text"] = "Why is this patent important"
        resp = self.send_request('/web/patent/ask_octi', payload, 'post', HTTPStatus.OK)
        assert resp['data']['message'] != ""
        resp = self.send_request(f'/web/patent/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 4
        self.send_request(f'/web/patent/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)
        resp = self.send_request(f'/web/patent/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 0

    def test_ask_octi_about_families_list(self):
        chat_id = str(family_id)

        #  Make sure there's nothing in the chat before starting
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        payload = {
            "family_ids": [family_id],
            "chat_id": chat_id,
            "text": "What are the main topics discussed in those patents"
        }
        resp = self.send_request('/web/patent/list/ask_octi', payload, 'post', HTTPStatus.OK)
        assert resp['data']['message'] != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 2
        payload["text"] = "What are the main legal statuses in the patent list"
        resp = self.send_request('/web/patent/list/ask_octi', payload, 'post', HTTPStatus.OK)
        assert resp['data']['message'] != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 4
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 0

    def test_ask_octi_about_publications_list(self):
        chat_id = str(publication_numbers)

        #  Make sure there's nothing in the chat before starting
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        payload = {
            "publication_numbers": publication_numbers,
            "chat_id": chat_id,
            "text": "What are the main topics discussed in those publications"
        }
        resp = self.send_request('/web/patent/list/ask_octi', payload, 'post', HTTPStatus.OK)
        assert resp['data']['message'] != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 2
        payload["text"] = "What are the main legal statuses in the patent list"
        resp = self.send_request('/web/patent/list/ask_octi', payload, 'post', HTTPStatus.OK)
        assert resp['data']['message'] != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 4
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 0

    def test_ask_octi_about_patent_stream(self):
        chat_id = str(family_id)

        #  Make sure there's nothing in the chat before starting
        self.send_request(f'/web/patent/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        payload = {
            "family_id": family_id,
            "chat_id": chat_id,
            "prompt_id": "summary"
        }

        stream = self.send_request('/web/patent/ask_octi/stream', payload, 'post', HTTPStatus.OK)
        assert stream != ""
        resp = self.send_request(f'/web/patent/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 2

        del payload["prompt_id"]
        payload["text"] = "Why this patent exists"
        stream = self.send_request('/web/patent/ask_octi/stream', payload, 'post', HTTPStatus.OK)
        assert stream != ""
        resp = self.send_request(f'/web/patent/chat_history/{chat_id}', payload, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 4
        self.send_request(f'/web/patent/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_ask_octi_about_families_list_stream(self):
        chat_id = str(family_id)

        #  Make sure there's nothing in the chat before starting
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        payload = {
            "family_ids": [family_id],
            "chat_id": chat_id,
            "text": "What are the main topics discussed in those patents"
        }

        stream = self.send_request('/web/patent/list/ask_octi/stream', payload, 'post', HTTPStatus.OK)
        assert stream != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 2

        payload["text"] = "What are the main legal statuses in the patent list"
        stream = self.send_request('/web/patent/list/ask_octi/stream', payload, 'post', HTTPStatus.OK)
        assert stream != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 4
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_ask_octi_about_publications_list_stream(self):
        chat_id = str(publication_numbers)

        #  Make sure there's nothing in the chat before starting
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        payload = {
            "publication_numbers": publication_numbers,
            "chat_id": chat_id,
            "text": "What are the main topics discussed in those patents"
        }

        stream = self.send_request('/web/patent/list/ask_octi/stream', payload, 'post', HTTPStatus.OK)
        assert stream != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 2

        payload["text"] = "What are the main legal statuses in the patent list"
        stream = self.send_request('/web/patent/list/ask_octi/stream', payload, 'post', HTTPStatus.OK)
        assert stream != ""
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 4
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_octi_ai_chat_events(self):
        chat_id = "events"

        #  Make sure there's nothing in the chat before starting
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)
        payload = {
            "event_type": "page_changed",
            "event_data": {"range": "1-50"}
        }
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 0
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}/events', payload, 'post', HTTPStatus.OK)
        resp = self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'get', HTTPStatus.OK)
        assert len(resp['data']) == 1
        self.send_request(f'/web/octi_ai/chat_history/{chat_id}', None, 'delete', HTTPStatus.NO_CONTENT)
