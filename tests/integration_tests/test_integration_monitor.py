import time
from datetime import datetime, timedelta
from http import HTTPStatus

import pytest

from app.features import Feature
from app.models.collaboration.collaboration import CollaborationResourceType
from tests.integration_tests.base_integration_test import TestIntegration, publication_numbers, family_ids
from tests.unit_tests.web_api import data


@pytest.fixture
def create_monitor_profile_request():
    def _(name, legal_status_active=False):
        return {
            "name": name,
            "delivery_method": "Excel",
            "delivery_frequency": "BiWeekly",
            "ipc_codes": ["A61B1/24", "H04N7/18", "H04N7/18"],
            "boolean_query": {
                "search_input": "APPLICANTS=(BMW Group OR Daimler) OR CPC=H01L2925/1",
                "search_filters": {
                    "earliest_priority_date": "2000-01-01"
                }
            },
            "semantic_query": {
                "search_input": "Intel x86",
                "patent_numbers": publication_numbers,
                "search_filters": {
                    "earliest_priority_date": "2000-01-01"
                }
            },
            "machine_learning_profile": {
                "search_input": "Anaerobic biological digester for a space station or a starship",
                "patent_numbers": publication_numbers,
                "selected_text_publications": publication_numbers,
                "ipc_codes": ["A61B1/24", "H04N7/18", "H04N7/18"],
                "selected_number_publications": []
            },
            "legal_status_profile": {
                "patent_numbers": publication_numbers,
                "scope": "publication"
            },
            "legal_status_active": legal_status_active,
            "machine_learning_active": True,
            "boolean_query_active": False,
            "semantic_query_active": False,
            "machine_learning_status": "Training",
            "machine_learning_dataset_last_update": "2019-08-24T14:15:22Z",
            "machine_learning_last_trained": "2019-08-23T14:15:22Z",
        }
    return _


class TestIntegrationMonitor(TestIntegration):
    def test_monitor_profile_user_settings(self):
        payload = {
            "name": f"Test profile {self._generate_unique_id()}",
            "delivery_method": "Excel",
            "delivery_frequency": "BiWeekly",
            "boolean_query": {
                "search_input": "APPLICANTS=(BMW Group OR Daimler) OR CPC=H01L2925/1"
            },
            "shared_users": "2",
            "boolean_query_active": True
        }
        user_one = self._create_access_token(features=[Feature.MONITOR], company_id=1, user_id=1)
        user_two = self._create_access_token(features=[Feature.MONITOR], company_id=1, user_id=2)
        profile = self.send_request('/web/monitor/profile', payload, 'post', HTTPStatus.CREATED, access_token=user_one)
        settings_two = self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/user/settings', '', 'get',
                                         HTTPStatus.OK, access_token=user_two)
        assert settings_two['data']['send_emails']
        settings_two['data']['send_emails'] = False
        settings_two = self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/user/settings',
                                         settings_two['data'], 'post', HTTPStatus.OK, access_token=user_two)
        assert not settings_two['data']['send_emails']
        settings_one = self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/user/settings', '', 'get',
                                         HTTPStatus.OK, access_token=user_one)
        assert settings_one['data']['send_emails']

    def test_monitor_profile(self, create_monitor_profile_request):
        profile_path = '/web/monitor/profile'
        response = self.send_request(profile_path, None, 'get', HTTPStatus.OK)
        number_of_profile = response['data']['page']['total_hits']
        profile_name_prefix = f'{datetime.now().timestamp()}-e2e'
        # Test feature disabled
        self.send_request(profile_path, '', 'get', HTTPStatus.FORBIDDEN,
                          self._create_access_token(features=[]))
        # Create a new profile
        self.send_request(profile_path, '', 'get', HTTPStatus.OK)
        profile_body = create_monitor_profile_request(name=f'{profile_name_prefix} profile')
        created_profile = self.send_request(profile_path, profile_body, 'post', HTTPStatus.CREATED)
        response = self.send_request(profile_path, None, 'get', HTTPStatus.OK)
        assert number_of_profile + 1 == response['data']['page']['total_hits']
        # Retrieve the profile just created
        profile = self.send_request(f'{profile_path}/{created_profile["data"]["id"]}', '', 'get', HTTPStatus.OK)
        assert profile["data"]["name"] == profile_body["name"]
        # check profile filter
        response = self.send_request(f'{profile_path}?name=like:%profile should not exist%',
                                     None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0
        response = self.send_request(f'{profile_path}?name=like:%{profile_name_prefix} profile%',
                                     None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 1
        response = self.send_request(f'{profile_path}?legal_status_active=1&name=like:%{profile_name_prefix} profile%',
                                     None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0

        # Change the name of the profile
        new_name = f'New name {self._generate_unique_id()}'
        self.send_request(f'/web/monitor/profile/{created_profile["data"]["id"]}',
                          {"name": new_name},
                          'patch', HTTPStatus.OK)
        profile = self.send_request(f'/web/monitor/profile/{created_profile["data"]["id"]}', '', 'get', HTTPStatus.OK)
        assert profile["data"]["name"] == new_name

        # Get the dataset generated for the profile (machine learning)
        dataset = self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/ml/dataset', '', 'get',
                                    HTTPStatus.OK)
        dataset_length = dataset['data']['page']['total_hits']
        assert dataset_length > 0

        # Add a new entry to the dataset
        new_entry_id = 54321
        self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/ml/dataset/{new_entry_id}',
                          {
                              'source': "FEEDBACK",
                              'match': "POSITIVE"
                          },
                          'put', HTTPStatus.OK)
        # Replace the entry
        self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/ml/dataset/{new_entry_id}',
                          {
                              'source': "FEEDBACK",
                              'match': "NEGATIVE"
                          },
                          'put', HTTPStatus.OK)
        # Check for dataset lenght after addition
        dataset = self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/ml/dataset', '', 'get',
                                    HTTPStatus.OK)
        new_dataset_length = dataset['data']['page']['total_hits']
        assert new_dataset_length == dataset_length + 1
        self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/ml/dataset/{new_entry_id}', '', 'delete',
                          HTTPStatus.NO_CONTENT)

        # Test bulk insert/update
        self.send_request(f'/web/monitor/profile/{profile["data"]["id"]}/ml/dataset',
                          [
                              {
                                  'document_id': 1,
                                  'source': "FEEDBACK",
                                  'match': "POSITIVE"
                              },
                              {
                                  'document_id': 2,
                                  'source': "FEEDBACK",
                                  'match': "NEGATIVE"
                              }
                          ],
                          'post', HTTPStatus.OK)

        # Delete profile. Should delete dataset also
        self.send_request('/web/monitor/profile/' + str(profile["data"]["id"]), '', 'delete', HTTPStatus.NO_CONTENT)

    def _create_monitor_run(self, profile_id, *, run_from=None, run_to=None):
        return self.send_request(
            '/web/monitor/run',
            {
                "name": f'{profile_id}-e2e profile run', "run_type": "Manual", "profile_id": profile_id,
                "run_from": ((run_from or datetime.now()) - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S'),
                "run_to": (run_to or datetime.now()).strftime('%Y-%m-%d %H:%M:%S'),
            },
            'post', HTTPStatus.CREATED
        )

    def _create_monitor_result_ml_entries(self, run_id: int, words_scores):
        ml_params = dict(
            monitor_run_id=run_id,
            type="MONITOR",
            name="Monitor results for run " + str(run_id),
            source_type="MACHINE_LEARNING",
            document_ids=[10072326],
            confidence_scores=[0.058],
            words_scores=words_scores,
            matching_documents_ids=[[41234, 43121, 561132]]
        )
        self.send_request('/web/result_collections', ml_params, 'post', HTTPStatus.CREATED)

    def _delete_monitor_profile(self, profile_id: int):
        self.send_request('/web/monitor/profile/' + str(profile_id), '', 'delete', HTTPStatus.NO_CONTENT)

    def _delete_monitor_run(self, run_id: int):
        self.send_request('/web/monitor/run/' + str(run_id), '', 'delete', HTTPStatus.NO_CONTENT)

    def test_monitor_runs(self, create_monitor_profile_request):
        share_path = '/web/monitor/shared-profile'
        profile_prefix = f'{datetime.now().timestamp()}-e2e'
        # Create profile
        profile_body = create_monitor_profile_request(f'{profile_prefix} profile')
        profile = self.send_request('/web/monitor/profile', profile_body, 'post', HTTPStatus.CREATED)
        profile_id = profile["data"]["id"]

        ls_profile_body = create_monitor_profile_request(name=f'{profile_prefix} ls profile',
                                                         legal_status_active=True)
        ls_profile = self.send_request('/web/monitor/profile', ls_profile_body, 'post', HTTPStatus.CREATED)

        # Create run
        response = self.send_request('/web/monitor/run', None, 'get', HTTPStatus.OK)
        number_of_runs = response['data']['page']['total_hits']
        run = self._create_monitor_run(profile_id)
        response = self.send_request('/web/monitor/run', None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == number_of_runs + 1
        run_id = run["data"]["id"]

        # Create results for ls profile run
        ls_run = self._create_monitor_run(ls_profile["data"]["id"])
        legal_status_results = {
            'entries': [
                {
                    'publication_number': publication_numbers[0],
                    'family_id': family_ids[0],
                    'previous_status': {
                        'general': 'valid',
                        'extended': 'active'
                    },
                    'current_status': {
                        'general': 'invalid',
                        'extended': 'expired'
                    }
                }
            ]
        }
        self.send_request(f'/web/monitor/run/{ls_run["data"]["id"]}/legal_status/results',
                          legal_status_results, 'post', HTTPStatus.CREATED)

        # Verify run data
        run_data = self.send_request('/web/monitor/run/' + str(run["data"]["id"]), None, 'get', HTTPStatus.OK)
        assert run_data['data']['number_of_documents'] == {'BOOLEAN': 0, 'MACHINE_LEARNING': 0,
                                                           'SEMANTIC': 0, 'TOTAL': 0, 'LEGAL_STATUS': 0}

        results = self.send_request(f'/web/monitor/run/{ls_run["data"]["id"]}/legal_status/results', None, 'get',
                                    HTTPStatus.OK)
        assert len(results['data']['documents']) == 1

        # Share a collection from the run
        param = {
            'name': run["data"]["name"] + self._generate_unique_id(),
            'monitor_run_id': run_id}
        collection = self.send_request('/web/result_collections', param, 'post', HTTPStatus.CREATED)
        user_ids = [u['id'] for u in data.team_users['data']['users'] if u['id'] != data.user_id]  # Exclude yourself
        share_user_id = user_ids[0]
        access_token = self._create_access_token(is_admin=False, user_id=share_user_id)
        response = self.send_request(share_path, '', "get", HTTPStatus.OK, access_token)
        number_of_shared_profile = response['data']['page']['total_hits']

        self._share_resource({
            "resource_id": collection['data']['id'],
            "resource_type": CollaborationResourceType.COLLECTION.name,
            "user_ids": [share_user_id]
        })
        response = self.send_request(share_path, '', "get", HTTPStatus.OK, access_token)
        assert number_of_shared_profile == response['data']['page']['total_hits']

        # check shared profile filter
        response = self.send_request(f'{share_path}?name=like:%The Mandalorian%',
                                     None, 'get', HTTPStatus.OK, access_token)
        assert response['data']['page']['total_hits'] == 0
        response = self.send_request(f'{share_path}?name=like:%{profile_prefix} profile%',
                                     None, 'get', HTTPStatus.OK, access_token)
        assert response['data']['page']['total_hits'] == 0
        response = self.send_request(f'{share_path}?legal_status_active=1&name=like:%{profile_prefix}%',
                                     None, 'get', HTTPStatus.OK, access_token)
        assert response['data']['page']['total_hits'] == 0

        # for shared ls profile
        param = {
            'name': ls_run["data"]["name"],
            'monitor_run_id': ls_run["data"]["id"]}
        collection = self.send_request('/web/result_collections', param, 'post', HTTPStatus.CREATED)
        self._share_resource({
            "resource_id": collection['data']['id'],
            "resource_type": CollaborationResourceType.COLLECTION.name,
            "user_ids": [share_user_id]
        })
        response = self.send_request(share_path, '', "get", HTTPStatus.OK, access_token)
        assert number_of_shared_profile == response['data']['page']['total_hits']

        # check monitor profile owner collection folder
        response = self.send_request('/web/monitor/profile/collections', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 2
        assert response['data']['profiles'][0]['name'] == ls_profile["data"]["name"]
        assert response['data']['profiles'][0]['collections_count'] == 1
        assert response['data']['profiles'][1]['name'] == profile["data"]["name"]
        assert response['data']['profiles'][1]['collections_count'] == 1
        response = self.send_request(f'/web/monitor/profile/{profile_id}/collections', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 1
        assert response['data']['profile']['name'] == profile["data"]["name"]
        assert len(response['data']['result_collections']) == 1

        # delete as shared user
        self.send_request(f'/web/monitor/profile/{profile_id}', '', 'delete', HTTPStatus.FORBIDDEN, access_token)

        # Update run
        patch_data = {"status": "Processing", "ml_status": "RunningPrediction", "semantic_status": "Finished",
                      "boolean_status": "Error"}
        run = self.send_request('/web/monitor/run/' + str(run['data']['id']), patch_data, 'patch', HTTPStatus.OK)
        assert all(item in run['data'].items() for item in patch_data.items())

        # Delete run
        self._delete_monitor_run(run["data"]["id"])
        self._delete_monitor_run(ls_run["data"]["id"])
        response = self.send_request('/web/monitor/run', None, 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == number_of_runs

        # Delete profile. Should delete shared profile also
        self._delete_monitor_profile(profile_id)
        self._delete_monitor_profile(ls_profile["data"]["id"])
        response = self.send_request(share_path, '', "get", HTTPStatus.OK, access_token)
        assert response['data']['page']['total_hits'] == number_of_shared_profile

    def test_monitor_widget_profile(self, create_monitor_profile_request):
        # Create profile
        request_body = create_monitor_profile_request(f'{datetime.now().timestamp()}-Test Widget')
        profile = self.send_request('/web/monitor/profile', request_body, 'post', HTTPStatus.CREATED)
        profile_id = profile["data"]["id"]
        empty_profile_body = create_monitor_profile_request(f'{datetime.now().timestamp()}-empty profile')
        empty_profile = self.send_request('/web/monitor/profile', empty_profile_body, 'post', HTTPStatus.CREATED)

        # Create run
        run1 = self._create_monitor_run(profile_id)
        run2 = self._create_monitor_run(profile_id)
        # get widget profiles
        profiles = self.send_request('/web/monitor/widget', '', 'get', HTTPStatus.OK)
        assert 'last_monitor_run' in profiles['data']['monitor_profiles'][0]
        assert profiles['data']['monitor_profiles'][0]['id'] == profile_id
        assert profiles['data']['monitor_profiles'][0]['last_monitor_run']['id'] == run2["data"]["id"]

        # Delete run
        self.send_request('/web/monitor/run/' + str(run1["data"]["id"]), '', 'delete', HTTPStatus.NO_CONTENT)
        self.send_request('/web/monitor/run/' + str(run2["data"]["id"]), '', 'delete', HTTPStatus.NO_CONTENT)
        self._delete_monitor_profile(profile_id)
        self.send_request('/web/monitor/profile/' + str(empty_profile["data"]["id"]), '', 'delete',
                          HTTPStatus.NO_CONTENT)

    def test_monitor_statistics(self, create_monitor_profile_request):
        access_token = self._create_access_token(is_admin=False)
        self.send_request('/web/monitor/statistics?user_id=1', None, 'get', HTTPStatus.FORBIDDEN, access_token)
        access_token = self._create_access_token(is_admin=True)
        request_body = create_monitor_profile_request(f"Test Statistics - {self._generate_unique_id()}")
        self.send_request('/web/monitor/profile', request_body, 'post', HTTPStatus.CREATED, access_token)
        self.send_request('/web/monitor/statistics', None, 'get', HTTPStatus.BAD_REQUEST, access_token)
        stats = self.send_request('/web/monitor/statistics?user_id=1', None, 'get', HTTPStatus.OK, access_token)
        assert len(stats['data']['monitor_statistics']) > 0

    def test_monitor_result_generation(self):
        # Create profile
        pns = ['JP-4448000-B2']
        request_payload = {
            "name": f"Testing run result generation {self._generate_unique_id()}",
            "delivery_method": "PDF",
            "delivery_frequency": "Weekly",
            "boolean_query": self.get_boolean_query(has_filters=False),
            "boolean_query_active": True,
            "semantic_query": {
                "search_input": "electric cars",
            },
            "semantic_query_active": True,
            "legal_status_profile": {
                "patent_numbers": pns,
                "scope": "publication"
            },
            "legal_status_active": True,
            "machine_learning_profile": {
                "search_input": "Anaerobic biological digester for a space station or a starship",
                "patent_numbers": pns,
                "selected_text_publications": pns,
                "selected_number_publications": []
            },
            "machine_learning_active": True,
        }
        profile = self.send_request('/web/monitor/profile', request_payload, 'post', HTTPStatus.CREATED)
        profile_id = profile["data"]["id"]

        self.send_request(f'/web/monitor/ml_training?monitor_profile_id={profile_id}', {}, 'post', HTTPStatus.CREATED)
        self._check_monitor_ml_training_status_until("Ready", profile_id)

        # Generate result
        run_id = self.send_request(
            f'/web/monitor/prediction?monitor_profile_id={profile_id}',
            {'from_date': "2000-01-01", "to_date": "2020-01-01"},
            'post', HTTPStatus.CREATED
        )['data']['monitor_run_id']
        run = self._check_monitor_run_status_until('Finished', run_id)

        assert run['data']['boolean_status'] == 'Finished'
        assert run['data']['semantic_status'] == 'Finished'
        assert run['data']['legal_status_status'] == 'Finished'

        results = self.send_request(f'/web/monitor/run/{run_id}/results', '', 'get', HTTPStatus.OK)
        assert results['data']['page']['total_hits']

    def test_legal_status_monitoring(self):
        request_payload = {
            "name": f"Testing legal status monitoring {self._generate_unique_id()}",
            "delivery_method": "PDF",
            "delivery_frequency": "Weekly",
            "legal_status_profile": {
                "patent_numbers": ['JP-4448000-B2'],
                "scope": "publication"
            },
            "legal_status_active": True
        }
        profile = self.send_request('/web/monitor/profile', request_payload, 'post', HTTPStatus.CREATED)
        profile_id = profile['data']['id']

        admin_user = self._create_access_token(is_admin=True)
        self._clear_legal_status_snapshot(profile_id, admin_user)

        run = self._create_monitor_run(profile_id)
        run_id = run['data']['id']

        self.send_request('/web/monitor/admin/runs/process', {'run_ids': [run_id]}, 'post',
                          HTTPStatus.CREATED, admin_user)

        run = self._check_monitor_run_status_until('Finished', run_id)

        assert run['data']['legal_status_status'] == 'Finished'

        results = self.send_request(f'/web/monitor/run/{run["data"]["id"]}/legal_status/results', None, 'get',
                                    HTTPStatus.OK)
        assert len(results['data']['documents']) == 1

        changes = results['data']['documents'][0]['legal_status_changes'][0]
        assert changes['previous_status']['general'] == 'unknown'
        assert changes['previous_status']['extended'] == 'unknown'
        assert changes['current_status']['general'] == 'invalid'
        assert changes['current_status']['extended'] == 'expired_fee_related'

    def _clear_legal_status_snapshot(self, profile_id, access_token):
        url = f'/web/monitor/profile/{profile_id}/legal_status_snapshot'
        self.send_request(url, None, 'delete', HTTPStatus.NO_CONTENT, access_token)

    def test_monitor_result_generation_ml_publication_number(self):
        # Create profile
        pns = ['JP-4448000-B2']
        request_payload = {
            "name": f"Testing run result generation publication {self._generate_unique_id()}",
            "delivery_method": "PDF",
            "delivery_frequency": "Weekly",
            "boolean_query": {
                "search_input": "APPLICANTS=(BMW Group OR Daimler)",
            },
            "boolean_query_active": True,
            "legal_status_profile": {
                "patent_numbers": pns,
                "scope": "publication"
            },
            "legal_status_active": True,
            "machine_learning_profile": {
                "search_input": "Anaerobic biological digester for a space station or a starship",
                "patent_numbers": pns,
                "selected_text_publications": pns,
                "selected_number_publications": []
            },
            "machine_learning_active": True,
            "scope": "Publications"
        }
        profile = self.send_request('/web/monitor/profile', request_payload, 'post', HTTPStatus.CREATED)
        profile_id = profile["data"]["id"]

        self.send_request(f'/web/monitor/ml_training?monitor_profile_id={profile_id}', {}, 'post', HTTPStatus.CREATED)
        self._check_monitor_ml_training_status_until("Ready", profile_id)

        # Generate results
        run_id = self.send_request(
            f'/web/monitor/prediction?monitor_profile_id={profile_id}',
            {'from_date': "2000-01-01", "to_date": "2020-01-01"},
            'post', HTTPStatus.CREATED
        )['data']['monitor_run_id']
        run = self._check_monitor_run_status_until('Finished', run_id)

        assert run['data']['boolean_status'] == 'Finished'
        assert run['data']['semantic_status'] == 'Skipped'
        assert run['data']['legal_status_status'] == 'Finished'

        results = self.send_request(f'/web/monitor/run/{run_id}/results', '', 'get', HTTPStatus.OK)
        assert results['data']['page']['total_hits']

    def _check_monitor_ml_training_status_until(self, status: str, profile_id: int):
        def test():
            response = self.send_request(f'/web/monitor/profile/{profile_id}', '', 'get', HTTPStatus.OK)
            return response['data']['machine_learning_status'] == status

        return self._check_until(test)

    def _check_monitor_run_status_until(self, status: str, run_id: int, access_token=None):
        def test():
            response = self.send_request(f'/web/monitor/run/{run_id}', '', 'get', HTTPStatus.OK, access_token)
            if response['data']['status'] == 'Error':
                assert False
            if response['data']['status'] == status:
                return response

        return self._check_until(test)

    @staticmethod
    def _check_until(test, timeout=15):
        started = time.time()
        while time.time() - started < timeout:
            res = test()
            if res:
                return res
            print(res)
            time.sleep(0.5)
        assert False, 'Timeout exceeded while checking condition'

    def test_sync_monitor_profile(self):
        shared_profiles_before = self.send_request('/web/monitor/shared-profile', '', 'get', HTTPStatus.OK)
        # Create profile
        uid = self._generate_unique_id()
        access_token = self._create_access_token(user_id=2, is_admin=True)
        request_payload = {
            "name": f"Sync {uid}",
            "delivery_method": "PDF",
            "delivery_frequency": "Weekly",
            "boolean_query": {
                "search_input": "APPLICANTS=(BMW Group OR Daimler)",
            },
            "boolean_query_active": True,
            "user_ids": [1]
        }
        profile = self.send_request('/web/monitor/profile', request_payload, 'post', HTTPStatus.CREATED, access_token)
        profile_id = profile["data"]["id"]
        run_id = self.send_request(
            f'/web/monitor/prediction?monitor_profile_id={profile_id}',
            {'from_date': "2000-01-01", "to_date": "2020-01-01"},
            'post', HTTPStatus.CREATED, access_token
        )['data']['monitor_run_id']
        self._check_monitor_run_status_until('Finished', run_id, access_token)

        time.sleep(1)  # Sharing is performed asynchronously
        shared_profiles_after = self.send_request('/web/monitor/shared-profile', '', 'get', HTTPStatus.OK)
        assert len(shared_profiles_after['data']['shared_profiles']) == \
               len(shared_profiles_before['data']['shared_profiles']) + 1
        response = self.send_request(f'/web/monitor/profile/{profile_id}/clone', '', 'post', HTTPStatus.OK,
                                     access_token)
        assert profile['data']['name'] in response['data']['name']

    def test_legal_status_tracking(self):
        self.send_request(f'/web/monitor/legal_status/trackable?publication_numbers={",".join(publication_numbers)}',
                          '', 'get', HTTPStatus.OK)
        self.send_request(f'/web/monitor/legal_status/tracking?publication_numbers={",".join(publication_numbers)}',
                          '', 'get', HTTPStatus.OK)
        req_body = {
            'publication_numbers': publication_numbers,
            'scope': 'publication'
        }
        self.send_request('/web/monitor/legal_status/tracking', req_body, 'post', HTTPStatus.OK)
        self.send_request(f'/web/monitor/legal_status/tracking?publication_numbers={",".join(publication_numbers)}',
                          '', 'get', HTTPStatus.OK)
