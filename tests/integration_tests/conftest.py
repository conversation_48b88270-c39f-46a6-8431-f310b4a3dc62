import os
import subprocess
import time
import pytest
import requests
from pathlib import Path
from alembic import command
from alembic.config import Config
from app.app import create_app
from tests.integration_tests.constants import API_PORT, API_BASE_URL


def get_working_directory():
    return Path(__file__).parents[2].resolve()


def get_env():
    return {
        'FLASK_APP': 'run.py',
        'FLASK_ENV': 'development',
        'FLASK_OVERWRITE_CONFIG': os.getenv('FLASK_OVERWRITE_CONFIG') or '../conf/development-integration-test.conf',
        **os.environ
    }


def healthcheck_process(*, process, timeout=5):
    try:
        process.communicate(timeout=timeout)
        assert not process.returncode
    except subprocess.TimeoutExpired:
        pass  # Timeout is good, It means process is still up and running


@pytest.fixture(scope='session')
def background_tasks():
    worker_process = subprocess.Popen(['celery', '-A', 'task_executor:celery', 'worker'],
                                      cwd=get_working_directory(),
                                      env=get_env())
    healthcheck_process(process=worker_process)
    yield worker_process
    worker_process.terminate()


@pytest.fixture(scope='session')
def app():
    env = get_env()
    app = create_app(env.get("FLASK_ENV"), env.get("FLASK_OVERWRITE_CONFIG"))
    return app


@pytest.fixture(scope='session')
def external_services():
    process = subprocess.Popen(['python', '-m', 'tests.integration_tests.mock_external_api'],
                               cwd=get_working_directory())
    yield process
    process.terminate()


@pytest.fixture(scope='session', autouse=True)
def api(app, external_services, background_tasks):
    process = subprocess.Popen(
        ['flask', 'run', '--port', str(API_PORT), '--debug'],
        cwd=get_working_directory(),
        env=get_env()
    )
    try:
        healthcheck_process(process=process)
        _wait_until_started()
        with app.app_context():
            _run_migrations()  # Make sure DB is up-to-date after running app
        yield process
    finally:
        process.terminate()


def _wait_until_started():
    seconds = 0
    timeout = 240
    while True:
        try:
            response = requests.get(f'{API_BASE_URL}/healthcheck')
            response.raise_for_status()
            break
        except (requests.exceptions.HTTPError, requests.exceptions.ConnectionError) as ex:
            seconds += 1
            assert seconds < timeout, f"App failed to start in {timeout} seconds"
            print(f"App not ready yet ({str(ex)}), waiting for startup...")
            time.sleep(1)


def _run_migrations():
    path = os.path.join(get_working_directory(), "migrations")
    alembic_cfg = Config(os.path.join(path, "alembic.ini"))
    alembic_cfg.set_main_option("script_location", path)
    command.upgrade(alembic_cfg, "head")
