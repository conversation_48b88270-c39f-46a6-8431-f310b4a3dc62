import time
from http import HTTPStatus

from app.models.collaboration.collaboration import CollaborationResourceType
from tests.integration_tests.base_integration_test import TestIntegration, publication_numbers, family_ids
from tests.unit_tests.web_api import data


class TestIntegrationLandscape(TestIntegration):

    @classmethod
    def get_create_landscape_request_body(cls, **kwargs):
        req = {
            'name': "Test",
            'patent_numbers': publication_numbers + ['none-sense', 'EP-1234'],
            'semantic_query': {"search_input": "test"},
            'boolean_query': {"search_input": "TITLE=test"},
            'family_ids': family_ids,
        }
        if kwargs:
            req.update(kwargs)
        return req

    def _check_landscape_profile_status_until(self, status: str, profile_id: int, timeout: int = 60):
        started = time.time()
        while time.time() - started < timeout:
            response = self.send_request(f'/web/landscape/profile/{profile_id}/status', '', 'get', HTTPStatus.OK)
            if response['data']['status'] == 'FAILED':
                assert False
            if response['data']['status'] == status:
                return
            time.sleep(0.5)
        assert False, f'Timeout exceeded till waiting for status {status} in landscape profile'

    def _check_landscape_rni_results_until(self, status: str, profile_id: int, timeout: int = 60):
        started = time.time()
        while time.time() - started < timeout:
            response = self.send_request(f'/web/landscape/profile/{profile_id}/rni_results', '', 'get', HTTPStatus.OK)
            if response['data']['status'] == 'FAILURE':
                assert False
            if response['data']['status'] == status:
                return
            time.sleep(0.5)
        assert False, f'Timeout exceeded till waiting for status {status} in landscape rni results'

    def test_landscape_rni_results(self):
        create_request = self.get_create_landscape_request_body(
            name="RNI results",
            patent_numbers=[publication_numbers[0], publication_numbers[1]],
            semantic_query=None,
            boolean_query=None,
            family_ids=None
        )
        response = self.send_request('/web/landscape/async/profile', create_request, 'post', HTTPStatus.CREATED)
        profile_id = response['data']['id']
        self._check_landscape_profile_status_until('COMPLETED', profile_id)
        self.send_request(f'/web/landscape/profile/{profile_id}/rni_results', {}, 'post', HTTPStatus.CREATED)
        self._check_landscape_rni_results_until('SUCCESS', profile_id)

    def test_competitive_landscape(self):
        create_request = self.get_create_landscape_request_body(name="Profile")
        response = self.send_request('/web/landscape/profile', create_request, 'post', HTTPStatus.CREATED)
        profile_id = response['data']['id']
        self._check_landscape_profile_status_until('COMPLETED', profile_id)

        response = self.send_request(f'/web/landscape/profile/{profile_id}/competitive', '', 'post', HTTPStatus.CREATED)
        competitive_profile_id = response['data']['id']
        self._check_landscape_profile_status_until('COMPLETED', competitive_profile_id, timeout=180)

        response = self.send_request(f'/web/landscape/profile/{competitive_profile_id}/documents', '', 'get',
                                     HTTPStatus.OK)
        assert response['data']['page']['total_hits'] > 0

        response = self.send_request(f'/web/landscape/profile/{profile_id}/competitive/insights', {},
                                     'post', HTTPStatus.OK)
        assert response['data']

        # delete profile
        self.send_request(f'/web/landscape/profile/{profile_id}', '', 'delete', HTTPStatus.NO_CONTENT)

    def test_landscape_profile_draft(self):
        base_path = '/web/landscape/profile'
        create_request = self.get_create_landscape_request_body(name="Draft", auto_compute=False)
        response = self.send_request(base_path, create_request, 'post', HTTPStatus.CREATED)
        assert response['data']['status'] == 'FETCHING_PORTFOLIO'
        profile_id = response['data']['id']
        self._check_landscape_profile_status_until('DRAFT', profile_id)
        response = self.send_request(f'/web/landscape/profile/{profile_id}/draft/documents', '', 'get', HTTPStatus.OK)
        assert response['data']['status'] == 'READY'  # should be ready since draft portfolio is already cached
        response = self.send_request(f'{base_path}/{profile_id}/draft/documents', '', 'get', HTTPStatus.OK)
        active_documents = len([d for d in response['data']['documents'] if d['landscape']['status'] == "ACTIVE"])
        doc = response['data']['documents'][0]
        change_status_request = [
            {'id': doc['general']['docdb_family_id'], 'status': "INACTIVE"}
        ]
        self.send_request(f'{base_path}/{profile_id}/draft/documents', change_status_request, 'put',
                          HTTPStatus.OK)
        response = self.send_request(f'{base_path}/{profile_id}/draft/documents', '', 'get', HTTPStatus.OK)
        active_documents_after = len([d for d in response['data']['documents'] if d['landscape']['status'] == "ACTIVE"])
        assert active_documents_after == active_documents - 1  # The document disabled
        response = self.send_request(f'{base_path}/{profile_id}/draft/documents?source=PATENT_NUMBER', '',
                                     'get', HTTPStatus.OK)
        assert not [d for d in response['data']['documents'] if d['landscape']['source'] != "PATENT_NUMBER"]
        self.send_request(f'{base_path}/{profile_id}/draft/complete', '', 'put', HTTPStatus.OK)
        self._check_landscape_profile_status_until('COMPLETED', profile_id)
        response = self.send_request(f'{base_path}/{profile_id}', '', 'get', HTTPStatus.OK)
        assert response['data']['status'] == "COMPLETED"
        self.send_request(f'{base_path}/{profile_id}', '', 'delete', HTTPStatus.NO_CONTENT)

    def test_landscape_profile(self):
        base_path = '/web/landscape/profile'
        self.send_request(base_path, '', 'get', HTTPStatus.FORBIDDEN, self._create_access_token(features=[]))
        response = self.send_request(base_path, '', 'get', HTTPStatus.OK)
        number_of_profiles = response['data']['page']['total_hits']
        create_request = self.get_create_landscape_request_body(name="Profile")
        response = self.send_request(base_path, create_request, 'post', HTTPStatus.CREATED)
        assert response['data']['status'] == 'FETCHING_PORTFOLIO'
        profile_id = response['data']['id']
        self._check_landscape_profile_status_until('COMPLETED', profile_id)
        response = self.send_request(f'/web/landscape/profile/{profile_id}', '', 'get', HTTPStatus.OK)
        assert len(response['data']['processed_input']['invalid']) == 1
        assert "none-sense" in response['data']['processed_input']['invalid']
        assert "EP-1234" in response['data']['processed_input']['not_found']
        assert response['data']['processed_input'].get('semantic_query')
        assert response['data']['submitted_semantic_results_count'] > 0
        assert response['data']['submitted_boolean_results_count'] > 0
        assert response['data']['submitted_patent_numbers_count'] > 0
        assert response['data']['submitted_family_ids_count'] > 0
        assert response['data']['families_count'] > 0
        assert response['data']['publications_count'] > 0
        response = self.send_request(base_path, '', 'get', HTTPStatus.OK)
        number_of_profiles_after = response['data']['page']['total_hits']
        assert number_of_profiles + 1 == number_of_profiles_after
        # get profile document
        self.send_request(f'{base_path}/{profile_id}/documents', '', 'get', HTTPStatus.OK)
        self.send_request(f'{base_path}/{profile_id}',
                          {
                             'name': "Updated",
                             'category': "Updated",
                             'semantic_query': {"search_input": "updated test"},
                             'boolean_query': {"search_input": "TITLE=test"}
                          }, 'patch', HTTPStatus.OK)
        self._check_landscape_profile_status_until('COMPLETED', profile_id)
        response = self.send_request(f'/web/landscape/profile/{profile_id}', '', 'get', HTTPStatus.OK)
        assert response['data']['name'] == 'Updated'
        assert response['data']['category'] == 'Updated'
        assert response['data']['processed_input'].get('boolean_query')
        assert response['data']['submitted_boolean_results_count'] > 0
        assert response['data']['submitted_semantic_results_count'] > 0
        assert response['data']['submitted_patent_numbers_count'] == 0
        assert response['data']['submitted_family_ids_count'] == 0

        # delete profile
        self.send_request(f'{base_path}/{profile_id}', '', 'delete', HTTPStatus.NO_CONTENT)

    def test_share_landscape_profile(self):
        base_path = '/web/landscape/profile'
        share_path = '/web/landscape/shared-profile'

        create_request = self.get_create_landscape_request_body(name="Shared Profile")
        response = self.send_request(base_path, create_request, 'post', HTTPStatus.CREATED)
        profile_id = response['data']['id']
        self._check_landscape_profile_status_until('COMPLETED', profile_id)

        # share profile
        user_ids = [u['id'] for u in data.team_users['data']['users'] if u['id'] != data.user_id]  # Exclude yourself

        access_token = self._create_access_token(is_admin=False, user_id=user_ids[0])
        response = self.send_request(share_path, '', "get", HTTPStatus.OK, access_token)
        number_of_shared_profile = response['data']['page']['total_hits']

        self._share_resource({
            "resource_id": profile_id,
            "resource_type": CollaborationResourceType.LANDSCAPE.name,
            "permission": "READONLY",
            "user_ids": [user_ids[0]]
        })
        response = self.send_request(share_path, '', "get", HTTPStatus.OK, access_token)
        assert number_of_shared_profile + 1 == response['data']['page']['total_hits']
        # get profile documents as shared user
        self.send_request(f'{base_path}/{profile_id}/documents', '', 'get', HTTPStatus.OK, access_token)
        # update as shared user
        self.send_request(f'{base_path}/{profile_id}',
                          {
                              'name': "Updated",
                              'category': "Updated",
                              'boolean_query': {"search_input": "APPLICANTS=(test)"}
                          }, 'patch', HTTPStatus.FORBIDDEN, access_token)
        # delete as shared user
        self.send_request(f'{base_path}/{profile_id}', '', 'delete', HTTPStatus.NOT_FOUND, access_token)
        # delete profile
        self.send_request(f'{base_path}/{profile_id}', '', 'delete', HTTPStatus.NO_CONTENT)
        # Profile should be gone for shared users
        response = self.send_request(share_path, '', 'get', HTTPStatus.OK, access_token)
        assert response['data']['page']['total_hits'] == number_of_shared_profile
