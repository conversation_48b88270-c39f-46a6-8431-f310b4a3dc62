import random
import string
from http import HTTPStatus

from tests.integration_tests.base_integration_test import TestIntegration, family_id


class TestIntegrationAnnotations(TestIntegration):
    def test_annotation_label(self):
        response = self.send_request('/web/annotations/labels?is_highlight=0', '', 'get', HTTPStatus.OK)
        number_of_labels = response['data']['page']['total_hits']

        name = ''.join(random.choices(string.ascii_uppercase + string.digits, k=20))

        if number_of_labels == 0:
            response = self.send_request('/web/annotations/labels', {"name": name, "shortcut": "X", "color": "XXXXXX"},
                                         'post', HTTPStatus.CREATED)
            label = response['data']
            first_label = None
        else:
            first_label = response['data']['labels'][0]
            self.send_request(f'/web/annotations/labels/{first_label["id"]}', '', 'delete', HTTPStatus.NO_CONTENT)

            response = self.send_request('/web/annotations/labels',
                                         {"name": name, "shortcut": first_label['shortcut'], "color": "XXXXXX"},
                                         'post', HTTPStatus.CREATED)
            label = response['data']

        self.send_request(f'/web/annotations/labels/{label["id"]}',
                          {"name": f"{name}_updated", "shortcut": label['shortcut'], "color": label['color']},
                          'patch', HTTPStatus.OK)
        self.send_request(f'/web/annotations/labels/{label["id"]}', '', 'get', HTTPStatus.OK)
        self.send_request(f'/web/annotations/labels/{label["id"]}', '', 'delete', HTTPStatus.NO_CONTENT)

        if first_label is not None:
            req_data = {"name": first_label['name'], "shortcut": first_label['shortcut'], "color": first_label['color']}
            self.send_request('/web/annotations/labels', req_data, 'post', HTTPStatus.CREATED)

        response = self.send_request('/web/annotations/labels?is_highlight=0', '', 'get', HTTPStatus.OK)
        number_of_labels_after = response['data']['page']['total_hits']
        assert number_of_labels == number_of_labels_after

    def test_annotation_document_label(self):
        response = self.send_request('/web/annotations/labels', '', 'get', HTTPStatus.OK)
        number_of_labels = response['data']['page']['total_hits']

        if number_of_labels == 0:
            name = ''.join(random.choices(string.ascii_uppercase + string.digits, k=20))
            response = self.send_request('/web/annotations/labels', {"name": name, "shortcut": "X", "color": "XXXXXX"},
                                         'post', HTTPStatus.CREATED)
            label_id = str(response['data']['id'])
        else:
            label_id = str(response['data']['labels'][0]['id'])

        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)
        number_of_labels = len(response['data']['labels'])

        text = ''.join(random.choices(string.ascii_uppercase + string.digits, k=100))

        req_data = {'entries': [
            {"label_id": label_id, "field": "abstract", "text": text, "start_pos": 0, "end_pos": 10}
        ]}
        response = self.send_request(f'/web/annotations/documents/{family_id}/labels', req_data, 'post',
                                     HTTPStatus.CREATED)

        document_label_id = response['data'][0]['id']

        req_data = {"label_id": label_id, "field": "abstract", "text": text + '_updated', "start_pos": 0, "end_pos": 10}
        self.send_request(f'/web/annotations/documents/{family_id}/labels/{document_label_id}', req_data,
                          'patch', HTTPStatus.CREATED)

        self.send_request(f'/web/annotations/documents/{family_id}/labels/{document_label_id}', '',
                          'delete', HTTPStatus.NO_CONTENT)
        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)
        number_of_labels_after = len(response['data']['labels'])
        assert number_of_labels == number_of_labels_after

    def test_annotation_document_comment(self):
        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)
        number_of_comments = len(response['data']['comments'])

        text = ''.join(random.choices(string.ascii_uppercase + string.digits, k=100))

        req_data = {"comment": text, "field": "abstract", "text": text, "start_pos": 0, "end_pos": 10,
                    "color": "FFFFFF"}
        response = self.send_request(f'/web/annotations/documents/{family_id}/comments', req_data, 'post',
                                     HTTPStatus.CREATED)
        document_comment_id = response['data']['id']
        self.send_request(f'/web/annotations/documents/{family_id}/comments/{document_comment_id}', req_data, 'patch',
                          HTTPStatus.CREATED)
        # start: reply comment
        req_data["parent_comment_id"] = document_comment_id
        reply = self.send_request(f'/web/annotations/documents/{family_id}/comments', req_data, 'post',
                                  HTTPStatus.CREATED)
        comment_reply_id = reply['data']['id']
        self.send_request(f'/web/annotations/documents/{family_id}/comments/{comment_reply_id}', req_data, 'patch',
                          HTTPStatus.CREATED)
        self.send_request(f'/web/annotations/documents/{family_id}/comments/{comment_reply_id}', '',
                          'delete', HTTPStatus.NO_CONTENT)
        # additional reply for cascade delete with parent comment
        self.send_request(f'/web/annotations/documents/{family_id}/comments', req_data, 'post', HTTPStatus.CREATED)
        # end: reply comment

        self.send_request(f'/web/annotations/documents/{family_id}/comments/{document_comment_id}', '',
                          'delete', HTTPStatus.NO_CONTENT)
        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)
        number_of_comments_after = len(response['data']['comments'])
        assert number_of_comments == number_of_comments_after

    def test_annotation_documents(self):
        response = self.send_request('/web/annotations/labels', '', 'get', HTTPStatus.OK)

        self.send_request('/web/annotations/labels/9999999999/documents', '', 'get', HTTPStatus.NOT_FOUND)

        if response['data']['page']['total_hits'] == 0:
            name = ''.join(random.choices(string.ascii_uppercase + string.digits + '     ', k=20))
            response = self.send_request('/web/annotations/labels', {"name": name, "shortcut": "X", "color": "XXXXXX"},
                                         'post', HTTPStatus.CREATED)
            label_id = str(response['data']['id'])
        else:
            label_id = str(response['data']['labels'][0]['id'])

        text = ''.join(random.choices(string.ascii_uppercase + string.digits + '      ', k=100))

        label_req_data = {'entries': [
            {"label_id": label_id, "field": "abstract", "text": text, "start_pos": 0, "end_pos": 10}
        ]}
        comment_req_data = {"comment": text, "field": "abstract", "text": text, "start_pos": 0, "end_pos": 10,
                            "color": "FFFFFF"}

        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)
        resp = self.send_request(f'/web/annotations/labels/{label_id}/documents', '', 'get', HTTPStatus.OK)
        labeled_count = len(resp['data']['documents'])

        if not response['data'] or len(response['data']['labels']) == 0:
            self.send_request(f'/web/annotations/documents/{family_id}/labels', label_req_data, 'post',
                              HTTPStatus.CREATED)
            resp = self.send_request(f'/web/annotations/labels/{label_id}/documents', '', 'get', HTTPStatus.OK)
            assert labeled_count + 1 == len(resp['data']['documents'])

        if not response['data'] or len(response['data']['comments']) == 0:
            self.send_request(f'/web/annotations/documents/{family_id}/comments', comment_req_data, 'post',
                              HTTPStatus.CREATED)

        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)

        number_of_labels = len(response['data']['labels'])
        number_of_comments = len(response['data']['comments'])

        self.send_request(f'/web/annotations/documents/{family_id}/labels', label_req_data, 'post', HTTPStatus.CREATED)
        self.send_request(f'/web/annotations/documents/{family_id}/comments', comment_req_data, 'post',
                          HTTPStatus.CREATED)

        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)

        number_of_labels_after = len(response['data']['labels'])
        number_of_comments_after = len(response['data']['comments'])

        response = self.send_request('/web/annotations/documents', '', 'get', HTTPStatus.OK)
        number_of_documents = response['data']['page']['total_hits']

        assert number_of_labels + 1 == number_of_labels_after
        assert number_of_comments + 1 == number_of_comments_after
        assert number_of_documents > 0

        self.send_request(f'/web/annotations/documents/{family_id}', None, 'delete', HTTPStatus.NO_CONTENT)
        response = self.send_request(f'/web/annotations/documents/{family_id}', '', 'get', HTTPStatus.OK)

        assert len(response['data']['labels']) == 0
        assert len(response['data']['comments']) == 0
        resp = self.send_request(f'/web/annotations/labels/{label_id}/documents', '', 'get', HTTPStatus.OK)
        assert labeled_count == len(resp['data']['documents'])
