from datetime import datetime, timed<PERSON>ta
from http import HTTPStatus

from tests.integration_tests.base_integration_test import TestIntegration, family_id
from tests.unit_tests.web_api import data


class TestIntegrationTasks(TestIntegration):

    def _create_topic(self, name, is_predefined=False, access_token=None, expected_status=HTTPStatus.CREATED):
        payload = {"name": name, "is_predefined": is_predefined}
        response = self.send_request('/web/topics', payload, 'post', expected_status, access_token)
        if expected_status == HTTPStatus.CREATED:
            return response['data']
        return response

    def _get_topic(self, topic_id, access_token=None, expected_status=HTTPStatus.OK):
        response = self.send_request(f'/web/topics/{topic_id}', None, 'get', expected_status, access_token)
        if expected_status == HTTPStatus.OK:
            return response['data']
        return response

    def _update_topic(self, topic_id, name, access_token=None, expected_status=HTTPStatus.OK):
        payload = {"name": name}
        response = self.send_request(f'/web/topics/{topic_id}', payload, 'patch', expected_status, access_token)
        if expected_status == HTTPStatus.OK:
            return response['data']
        return response

    def test_patent_rating_requests_management(self):
        # Create a topic for the rating
        topic = self._create_topic(f"Test Topic {self._generate_unique_id()}")
        topic_id = topic['id']
        rating_request = {
            "subject": "Patent rating",
            "description": "example description",
            "assignees": [
                {"id": 2, "type": "USER"},
                {"id": 3, "type": "USER"},
                {"id": 4, "type": "USER"},
                {"id": 5, "type": "USER"},
                {"id": 6, "type": "USER"},
            ],
            "document_id": family_id,
            "deadline": f"{datetime.now() + timedelta(days=7)}",
            "task_type": "STAR_RATING",
            "topic_ids": [topic_id]
        }

        # create a rating
        response = self.send_request('/web/tasks', rating_request, 'post', HTTPStatus.CREATED)
        rating_id = response['data']['id']

        # Test updating rating without topic_ids
        update_request = {
            "subject": "updated task",
            "description": "updated description"
        }
        updated_rating = self.send_request(f'/web/tasks/{rating_id}', update_request, 'patch', HTTPStatus.OK)
        assert updated_rating['data']['topics'][0]['id'] == topic_id  # topics should remain unchanged

        # user involved in the rating should be able to edit topics
        assignee_2_token = self._create_access_token(user_id=2, company_id=1)
        self.send_request(f'/web/tasks/{rating_id}', dict(topic_ids=[topic_id]), 'patch',
                          HTTPStatus.OK, assignee_2_token)
        # but should not be able to edit other fields of the rating
        self.send_request(f'/web/tasks/{rating_id}', dict(subject="updated task"), 'patch',
                          HTTPStatus.BAD_REQUEST, assignee_2_token)

        # a user not involved in the task can not modify the rating
        other_token = self._create_access_token(user_id=11, company_id=1)
        self.send_request(f'/web/tasks/{rating_id}', dict(subject="updated task"), 'patch',
                          HTTPStatus.FORBIDDEN, other_token)
        self.send_request(f'/web/tasks/{rating_id}', dict(topic_ids=[topic_id]), 'patch',
                          HTTPStatus.FORBIDDEN, other_token)

        # Edit assignees of the rating request
        update_request = {
            "assignees": [
                {"id": 2, "type": "USER"},
                {"id": 3, "type": "USER"},
                {"id": 4, "type": "USER"},
            ],
        }
        self.send_request(f'/web/tasks/{rating_id}', update_request, 'patch', HTTPStatus.OK)
        response = self.send_request(f'/web/tasks/{rating_id}/assignments', None, 'get', HTTPStatus.OK)
        assignments = response['data']['task_assignments']
        assert len(assignments) == 3

        # user involded in the rating should not be able to delete it's assignment if hasn't completed it
        assignment_2 = next(a for a in assignments if a["assignee_id"] == 2)
        self.send_request(f'/web/tasks/assignments/{assignment_2["id"]}', '', 'delete', HTTPStatus.FORBIDDEN,
                          assignee_2_token)

        # a manager can delete assignments from any task in it's company
        assignment_3 = next(a for a in assignments if a["assignee_id"] == 3)
        self.send_request(f'/web/tasks/assignments/{assignment_3["id"]}', '', 'delete', HTTPStatus.NO_CONTENT,
                          self._create_access_token(user_id=11, is_manager=True))

        # get documents
        filter_payload = {
            "document_id": f"{family_id}",
            "author_id": f"{data.user_id}",
            "assigned_user_ids": "1,2"
        }
        response = self.send_request('/web/tasks/documents', filter_payload, 'get', HTTPStatus.OK)
        assert len(response['data']['documents']) == 1
        assert response['data']['documents'][0]['general']['docdb_family_id'] == family_id

        response = self.send_request('/web/tasks/documents', None, 'get',
                                     HTTPStatus.OK, assignee_2_token)
        assert len(response['data']['documents']) == 1
        assert response['data']['documents'][0]['general']['docdb_family_id'] == family_id

        response = self.send_request('/web/tasks/documents', None, 'get',
                                     HTTPStatus.OK, other_token)
        assert len(response['data']['documents']) == 1

        self.send_request(f'/web/tasks/{rating_id}', None, 'delete', HTTPStatus.NO_CONTENT)
        self.send_request(f'/web/topics/{topic_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_completing_ratings(self):
        user_id_2 = 2
        user_id_3 = 3
        token_2 = self._create_access_token(user_id=user_id_2, company_id=1)
        token_3 = self._create_access_token(user_id=user_id_3, company_id=1)

        rating_request = {
            "subject": "Patent rating",
            "description": "example description",
            "assignees": [
                {"id": user_id_2, "type": "USER"},
                {"id": user_id_3, "type": "USER"},
            ],
            "document_id": family_id,
            "deadline": f"{datetime.now() + timedelta(days=7)}",
            "task_type": "STAR_RATING",
        }

        # create a rating
        response = self.send_request('/web/tasks', rating_request, 'post', HTTPStatus.CREATED)
        rating_id = response['data']['id']

        # Get the rating assignments
        response = self.send_request(f'/web/tasks/{rating_id}/assignments', None, 'get', HTTPStatus.OK)
        assignments = response['data']['task_assignments']
        assert len(assignments) == 2

        # user_id_2 marks rating assignment as opened
        assignment_2 = next(a for a in assignments if a["assignee_id"] == user_id_2)
        open_payload = dict(task_assignment_ids=[assignment_2['id']])
        response = self.send_request('/web/tasks/assignments/open', open_payload, 'post', HTTPStatus.OK, token_2)
        assert response['data']['task_assignments'][0]['status'] == 'OPEN'

        # user_id_2 rates patent
        answer_dict = dict(answer="5")
        response = self.send_request(f'/web/tasks/assignments/{assignment_2["id"]}', answer_dict, 'post',
                                     HTTPStatus.CREATED, token_2)

        # user_id_2 updates message for it's rating
        answer_dict = dict(message="a message")
        response = self.send_request(f'/web/tasks/assignments/{assignment_2["id"]}', answer_dict, 'patch',
                                     HTTPStatus.OK, token_2)
        assert response['data']['message'] == answer_dict['message']

        # should be open, because not all assignments are done
        response = self.send_request(f'/web/tasks/{rating_id}', "", 'get', HTTPStatus.OK)
        assert response['data']['status'] == "OPEN"

        # user_id_3 submits his answer
        assignment_3 = next(a for a in assignments if a["assignee_id"] == user_id_3)
        self.send_request(f'/web/tasks/assignments/{assignment_3["id"]}', dict(answer="4"), 'post', HTTPStatus.CREATED,
                          token_3)

        # Now the task should be done, since all answers are done
        response = self.send_request(f'/web/tasks/{rating_id}', "", 'get', HTTPStatus.OK)
        assert response['data']['status'] == "DONE"

        self.send_request(f'/web/tasks/{rating_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_completing_ratings_as_group(self):
        # Users with id 2 and 3 are pre-configured to be part of group with id 1
        group = 1
        user2 = self._create_access_token(user_id=2, company_id=1)
        user3 = self._create_access_token(user_id=3, company_id=1)

        # create a task and assign it to a whole group
        response = self.send_request('/web/tasks', {
            "subject": "example task",
            "description": "example description",
            "assignees": [{"id": 1, "type": "GROUP"}],
            "document_id": family_id,
            "task_type": "STAR_RATING",
        }, 'post', HTTPStatus.CREATED)
        task = response['data']

        # Check that user2 can see this task in his inbox
        response = self.send_request('/web/tasks?assigned_group_ids=1', "", 'get', HTTPStatus.OK, user2)
        assert [t for t in response['data']['tasks'] if t['id'] if t['id'] == task['id']]

        # Check that user3 can also see this task in his inbox
        response = self.send_request('/web/tasks?assigned_group_ids=1', "", 'get', HTTPStatus.OK, user3)
        task = next(t for t in response['data']['tasks'] if t['id'] if t['id'] == task['id'])
        assignment = next(a for a in task["assignments"] if a["assignee_id"] == group and a["assignee_type"] == 'GROUP')

        # Assignment status should be new
        assert assignment['status'] == 'NEW'

        # user2 should be able to submit an answer since he's part of the group
        self.send_request(f'/web/tasks/assignments/{assignment["id"]}', {
            "answer": "5"
        }, 'post', HTTPStatus.CREATED, user2)

    def test_manager_can_delete_ratings(self):
        # Manager can delete other users' ratings
        user2 = self._create_access_token(user_id=2, company_id=1)
        user3 = self._create_access_token(user_id=3, company_id=1, is_manager=True)

        # create a task and assign it to a whole group
        response = self.send_request('/web/tasks', {
            "subject": "example task",
            "description": "example description",
            "assignees": [
                {"id": 2, "type": "USER"}
            ],
            "document_id": family_id,
            "task_type": "STAR_RATING",
        }, 'post', HTTPStatus.CREATED)
        task = response['data']

        response = self.send_request('/web/tasks?assigned_user_ids=2', "", 'get', HTTPStatus.OK, user3)
        task = next(t for t in response['data']['tasks'] if t['id'] if t['id'] == task['id'])
        assignment = next(a for a in task["assignments"] if a["assignee_id"] == 2 and a["assignee_type"] == 'USER')
        self.send_request(f'/web/tasks/assignments/{assignment["id"]}', {
            "answer": "5"
        }, 'post', HTTPStatus.CREATED, user2)

        self.send_request(f'/web/tasks/assignments/{assignment["id"]}', "", 'delete', HTTPStatus.FORBIDDEN)
        self.send_request(f'/web/tasks/assignments/{assignment["id"]}', "", 'delete', HTTPStatus.NO_CONTENT, user3)

    def test_topic_crud_and_permissions(self):
        # Create General topic
        general_topic_name = 'General'
        general_topic = self._create_topic(general_topic_name, is_predefined=True)
        general_topic_id = general_topic['id']
        assert general_topic['name'] == general_topic_name

        # Create a topic
        topic_name = f"Test Topic {self._generate_unique_id()}"
        topic = self._create_topic(topic_name)
        topic_id = topic['id']
        assert topic['name'] == topic_name

        # Try to create a topic with the same name
        duplicate_response = self._create_topic(topic_name, expected_status=HTTPStatus.CREATED)
        assert topic['id'] == duplicate_response['id']
        assert topic['name'] == duplicate_response['name']

        # List topics
        response = self.send_request('/web/topics', None, 'get', HTTPStatus.OK)
        assert len(response['data']['topics']) > 0
        assert any(topic['id'] == general_topic_id for topic in response['data']['topics'])

        # Update topic
        updated_name = f"Updated Topic {self._generate_unique_id()}"
        updated_topic = self._update_topic(topic_id, updated_name)
        assert updated_topic['name'] == updated_name

        # Try to update topic with an existing name
        another_topic = self._create_topic(f"Another Topic {self._generate_unique_id()}")
        duplicate_update = self._update_topic(another_topic['id'], updated_name, expected_status=HTTPStatus.BAD_REQUEST)
        assert "already exists" in duplicate_update['message']

        # Get single topic
        topic = self._get_topic(topic_id)
        assert topic['name'] == updated_name

        # Test permissions
        user_without_company = self._create_access_token(user_id=2, company_id=None)
        self.send_request('/web/topics', None, 'get', HTTPStatus.FORBIDDEN, user_without_company)
        self._create_topic(topic_name, access_token=user_without_company, expected_status=HTTPStatus.FORBIDDEN)
        self._get_topic(topic_id, user_without_company, HTTPStatus.FORBIDDEN)
        self._update_topic(topic_id, f"New Name {self._generate_unique_id()}", user_without_company,
                           HTTPStatus.FORBIDDEN)

        # Delete topics
        self.send_request(f'/web/topics/{topic_id}', None, 'delete', HTTPStatus.NO_CONTENT)
        self.send_request(f'/web/topics/{another_topic["id"]}', None, 'delete', HTTPStatus.NO_CONTENT)

        # Verify topics are deleted
        self._get_topic(topic_id, expected_status=HTTPStatus.NOT_FOUND)
        self._get_topic(another_topic['id'], expected_status=HTTPStatus.NOT_FOUND)

    def test_topic_filtering_and_pagination(self):
        # Create multiple topics
        topic_uid = self._generate_unique_id()
        topics = [
            ('General', True),
            (f"Alpha Topic {topic_uid}", False),
            (f"Beta Topic {self._generate_unique_id()}", False),
            (f"Gamma Topic {self._generate_unique_id()}", False)
        ]
        topic_ids = []
        for topic_name, is_predefined in topics:
            topic = self._create_topic(topic_name, is_predefined=is_predefined)
            topic_ids.append(topic['id'])

        # Test filtering
        response = self.send_request(f'/web/topics?name=like:%{topic_uid}%', None, 'get', HTTPStatus.OK)
        assert not any(topic['name'] == 'General' for topic in response['data']['topics'])

        # Test pagination
        response = self.send_request('/web/topics?page=1&page_size=2', None, 'get', HTTPStatus.OK)
        assert len(response['data']['topics']) > 0
        assert response['data']['page']['total_hits'] > 0
        assert any(topic['name'] == 'General' for topic in response['data']['topics'])

        # Clean up
        for topic_id in topic_ids:
            self.send_request(f'/web/topics/{topic_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_topic_not_found(self):
        non_existent_id = 99999
        self._get_topic(non_existent_id, expected_status=HTTPStatus.NOT_FOUND)
        self.send_request(f'/web/topics/{non_existent_id}', {"name": "Updated"}, 'patch', HTTPStatus.NOT_FOUND)
        self.send_request(f'/web/topics/{non_existent_id}', None, 'delete', HTTPStatus.NOT_FOUND)
