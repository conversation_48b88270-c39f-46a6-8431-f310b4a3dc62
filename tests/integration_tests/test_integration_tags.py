import time
from http import HTTPStatus

from tests.integration_tests.base_integration_test import TestIntegration, family_id, family_ids


class TestIntegrationTags(TestIntegration):
    def _list_tags(self, name="", access_token=None, expected_status=HTTPStatus.OK):
        path = f'/web/tags{"?name=" + name if name else ""}'
        return self.send_request(path, None, 'get', expected_status, access_token)

    def _create_tag(self, name, access_token=None, expected_status=HTTPStatus.CREATED):
        body = {'name': name, 'color': 'FFFFFF'}
        return self.send_request('/web/tags', body, 'post', expected_status, access_token)

    def _create_private_tag(self, name, access_token=None, expected_status=HTTPStatus.CREATED):
        body = {'name': name, 'color': 'FFFFFF', 'private': True}
        return self.send_request('/web/tags', body, 'post', expected_status, access_token)

    def _update_tag(self, tag_id, to_update, access_token=None, expected_status=HTTPStatus.OK):
        return self.send_request(f'/web/tags/{tag_id}', to_update, 'patch', expected_status, access_token)

    def _remove_tag(self, tag_id, access_token=None, expected_status=HTTPStatus.NO_CONTENT):
        self.send_request(f'/web/tags/{tag_id}', None, 'delete', expected_status, access_token)

    def _remove_tags(self, tag_ids, access_token=None, expected_status=HTTPStatus.NO_CONTENT):
        self.send_request('/web/tags/delete', {'tag_ids': tag_ids}, 'post', expected_status, access_token)

    def _assign_tag_to_documents(self, tag_id, document_ids, access_token=None, expected_status=HTTPStatus.NO_CONTENT):
        body = {'document_ids': document_ids}
        self.send_request(f'/web/tags/{tag_id}/documents', body, 'post', expected_status, access_token)

    def _unassign_tag_from_document(self, tag_id, document_id, access_token=None,
                                    expected_status=HTTPStatus.NO_CONTENT):
        self.send_request(f'/web/tags/{tag_id}/documents/{document_id}', None, 'delete', expected_status, access_token)

    def _list_document_tags(self, document_id, access_token=None, name=None):
        response = self.send_request(f'/web/search/document/{document_id}', None, 'get', HTTPStatus.OK, access_token)
        custom_tags = response['data']['document']['custom_tags']
        if not name:
            return custom_tags
        return [ct for ct in custom_tags if ct['name'] == name]

    def test_tags(self):
        unique_id = self._generate_unique_id()
        tag_name = f'tag1-{unique_id}'
        user = self._create_access_token(user_id=1, company_id=1)
        self._create_tag(tag_name, user)
        tags = self._list_tags(tag_name, user)['data']['tags']
        assert len(tags) == 1
        tag = tags[0]
        self._assign_tag_to_documents(tag['id'], family_ids, user)
        document_tags = self._list_document_tags(family_id, user, tag_name)
        assert len(document_tags) == 1

        bool_request = {"search_fields": [{"field": "TAG", "value": str(tag['id']), "operator": "="}]}
        self.send_request('/web/search/boolean', bool_request, 'post', HTTPStatus.OK)

        bool_request = {"search_fields": [{"field": "TAG", "value": str(tag['id']), "operator": "<>"}]}
        self.send_request('/web/search/boolean', bool_request, 'post', HTTPStatus.OK)

        self._update_tag(tag['id'], {'name': tag_name}, user)
        self._unassign_tag_from_document(tag['id'], family_id, user)
        document_tags = self._list_document_tags(family_id, user, tag_name)
        assert len(document_tags) == 0
        self._remove_tag(tag['id'], user)
        tag = self._create_tag(tag_name, user)['data']
        assert len(self._list_tags(tag_name, user)['data']['tags']) == 1
        self._remove_tags([tag['id']], user)
        assert len(self._list_tags(tag_name, user)['data']['tags']) == 0

    def test_private_tags(self):
        unique_id = self._generate_unique_id()
        tag_name = f'private-tag-{unique_id}'
        tag_owner = self._create_access_token(user_id=1, company_id=1)
        team_member = self._create_access_token(user_id=2, company_id=1)
        tag = self._create_private_tag(tag_name, tag_owner)['data']

        # only owner should see it available to be assigned
        assert len(self._list_tags(tag_name, tag_owner)['data']['tags']) == 1
        assert len(self._list_tags(tag_name, team_member)['data']['tags']) == 0

        self._assign_tag_to_documents(tag['id'], [family_id], tag_owner)

        # only owner should be able to see it attached to documents
        assert self._list_document_tags(family_id, tag_owner, tag_name)
        assert not self._list_document_tags(family_id, team_member, tag_name)

        # only owner can edit it if private
        self._update_tag(tag['id'], {'color': '000000'}, team_member, expected_status=HTTPStatus.NOT_FOUND)
        self._update_tag(tag['id'], {'color': '000000'}, tag_owner)

        # if owner makes it public, other should be able to see it
        self._update_tag(tag['id'], {'private': False}, tag_owner)
        assert len(self._list_tags(tag_name, team_member)['data']['tags']) == 1
        assert self._list_document_tags(family_id, team_member, tag_name)

        # if it's public, only owner can make it private
        self._update_tag(tag['id'], {'private': True}, team_member, expected_status=HTTPStatus.FORBIDDEN)
        self._update_tag(tag['id'], {'private': True}, tag_owner)

        # only owner can remove it
        self._remove_tag(tag['id'], team_member, expected_status=HTTPStatus.NOT_FOUND)
        self._remove_tag(tag['id'], tag_owner)

    def test_ai_enabled_tags(self):
        tag_name = f'tag-{self._generate_unique_id()}'
        user = self._create_access_token(user_id=1, company_id=1)
        tag = self._create_tag(tag_name, user)['data']
        self._update_tag(tag['id'], {'ai_enabled': True})
        self._assign_tag_to_documents(tag['id'], [family_ids[2]], user)

        # Trigger AI tag recommendations

        time.sleep(1)
        req = {'documents_ids': family_ids[:2]}
        self.send_request('/web/tags/recommendations', req, 'post', HTTPStatus.OK)

        # Now let's check what the AI suggested

        res = self.send_request(f'/web/tags/{tag["id"]}/recommendations', '', 'get', HTTPStatus.OK)
        tag_recommendations = res['data']['tag_recommendations']
        to_accept = tag_recommendations[0]['tag_recommendations'][0]['recommendation']
        to_reject = tag_recommendations[1]['tag_recommendations'][0]['recommendation']

        # Pending tag recommendations should appear as part of search responses

        req = {'documents_ids': family_ids}
        res = self.send_request('/search/document', req, 'post', HTTPStatus.OK)
        docs = res['data']['documents']
        assert self._has_tag_recommendation(docs[0]['tag_recommendations'], to_accept['id'])
        assert self._has_tag_recommendation(docs[1]['tag_recommendations'], to_reject['id'])

        # And let's accept / reject some

        res = self.send_request(f'/web/tags/recommendations/{to_accept["id"]}',
                                {'status': 'ACCEPTED'},
                                'patch', HTTPStatus.OK)

        res = self.send_request(f'/web/tags/recommendations/{to_reject["id"]}',
                                {'status': 'REJECTED'},
                                'patch', HTTPStatus.OK)

        # There should be no pending recommendations after managing them
        res = self.send_request(f'/web/tags/{tag["id"]}/recommendations?status=pending', '', 'get', HTTPStatus.OK)
        assert not res['data']['tag_recommendations']

        # Tag should appear for the one that was accepted
        req = {'documents_ids': family_ids}
        res = self.send_request('/search/document', req, 'post', HTTPStatus.OK)
        docs = res['data']['documents']
        assert not self._has_tag_recommendation(docs[0]['tag_recommendations'], to_accept['id'])
        assert not self._has_tag_recommendation(docs[1]['tag_recommendations'], to_reject['id'])
        assert self._has_tag_recommendation(docs[0]['custom_tags'], to_accept['id'])
        assert not self._has_tag_recommendation(docs[1]['custom_tags'], to_reject['id'])

        self._remove_tag(tag['id'], user)

        # All recommendations should be gone after removing tag
        res = self.send_request(f'/web/tags/{tag["id"]}/recommendations?status=accepted', '', 'get', HTTPStatus.OK)
        assert not res['data']['tag_recommendations']
        res = self.send_request(f'/web/tags/{tag["id"]}/recommendations?status=rejected', '', 'get', HTTPStatus.OK)
        assert not res['data']['tag_recommendations']

    def _has_tag_recommendation(self, tags, tag_recommendation_id):
        rids = {t.get('recommendation').get('id') for t in tags if t.get('recommendation')}
        return tag_recommendation_id in rids
