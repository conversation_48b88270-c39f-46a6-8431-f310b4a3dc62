import random
import re
import string
from http import HTTPStatus
from datetime import datetime
from typing import Optional, List

import pytest
import requests
from flask_jwt_extended import create_access_token, get_jti

from app.extensions import session_store
from app.features import Feature
from tests.integration_tests.constants import API_BASE_URL

family_ids = [59662569, 52693395, 13963964]
family_id = family_ids[0]
publication_numbers = ["US-20090094134-A1", "AU-2012100567-A4", "WO-2015127225-A1"]


class TestIntegration:

    @classmethod
    def _get_search_hash(cls, search_response):
        return search_response["data"]["search_info"]["search_hash"]

    def _list_folders(self, folder_id=None):
        url = "/web/result_collections/folders"
        if folder_id:
            url = f"{url}?folder_id={folder_id}"
        return self.send_request(url, "", "get", HTTPStatus.OK)

    @pytest.fixture(scope='class')
    def access_token(self, app):
        with app.app_context():
            return self._create_access_token()

    @pytest.fixture(autouse=True)
    def authenticate(self, access_token):
        self.token = access_token

    @classmethod
    def _generate_unique_id(cls):
        return re.sub(r'\W+', '', str(datetime.now()))

    @classmethod
    def _create_access_token(cls, is_admin: bool = False, features: List[Feature] = None, user_id: int = 1,
                             company_id: Optional[int] = 1, subscription_type="PROFESSIONAL", is_manager=False) -> str:
        unique_id = str(datetime.now().timestamp())[-12:]
        claims = {
            "user_id": user_id,
            "sh": True,
            "is_admin": is_admin,
            "is_manager": is_manager,
            "company_id": company_id,
            "ft": [f.value for f in features] if features is not None else [f.value for f in Feature],
            "mp": 20,
            "st": subscription_type
        }
        access_token = create_access_token(identity=str(unique_id) + "@test.octimine.com",
                                           additional_claims={'user_claims': claims})
        session_store.set(get_jti(access_token), "false", 300)
        return access_token

    def send_request(self, url, request_dict=None, method='post', expected_status=HTTPStatus.CREATED,
                     access_token=None, is_anonymous=False):
        server_response = self.get_server_response(url, request_dict, method,
                                                   access_token=access_token,
                                                   is_anonymous=is_anonymous)
        assert server_response.status_code == expected_status.value, \
            f"Unexpected server response: {server_response.status_code}, {server_response.content}"
        if server_response.headers['Content-Type'] == 'application/json' and server_response.content:
            return server_response.json()
        return dict()

    def get_server_response(self, url, json_dict, method, allow_redirects=True, access_token=None, is_anonymous=False):
        if not url.startswith(API_BASE_URL):
            url = API_BASE_URL + url
        headers = {}
        access_token = access_token or self.token
        if not is_anonymous and access_token:
            headers['Authorization'] = "Bearer " + access_token
        return requests.request(method, url, json=json_dict, headers=headers, allow_redirects=allow_redirects)

    @classmethod
    def get_boolean_query(cls, has_filters=True):
        cache_breaker = cls.break_cache()
        query = {
            "search_input": "APPLICANTS=(SYMANTEC OR PLASTIC PALLETS) OR CPC=G02C7/027 OR TITLE=" + cache_breaker
        }
        if has_filters:
            query['search_filters'] = {
                "earliest_publication_date": "2000-01-01",
                "latest_priority_date": "2006-01-01"
            }
        return query

    def _share_resource(self, payload, expected_status=HTTPStatus.NO_CONTENT, access_token=None):
        self.send_request('/web/collaborations', payload, "post", expected_status, access_token=access_token)

    @classmethod
    def break_cache(cls):
        return ''.join(random.choices(string.digits, k=10))

    @classmethod
    def get_semantic_query(cls):
        cache_breaker = cls.break_cache()
        return {
            "search_input": "wireless communications " + cache_breaker,
            "docdb_family_ids": [42118808, 47010782, 37069117, 36649762, 55860147],
            "search_filters": {
                "text_weighting": 2.3,
                "quantity_cut_off": 150,
                "applicants_minus": ["ibm", "sony"]
            }
        }

    @classmethod
    def _get_total_hits(cls, list_response):
        return list_response["data"]["page"]["total_hits"]
