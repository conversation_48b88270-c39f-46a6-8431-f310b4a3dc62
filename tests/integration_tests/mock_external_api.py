from http import HTTPStatus
from flask import Flask, jsonify, make_response

mock_external_app = Flask('mock_external_app')


@mock_external_app.route('/recaptcha/api/siteverify', methods=['POST'])
def verify_recaptcha():
    return make_response(jsonify({"success": True}), HTTPStatus.OK)


@mock_external_app.route('/openai/deployments/<model>/chat/completions', methods=['POST'])
def mock_openai_completions(model):
    result = {
        "id": "chatcmpl-123",
        "object": "chat.completion",
        "created": 1677652288,
        "model": "gpt-3.5-turbo-0613",
        "choices": [{
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hello there, how may I assist you today?",
            },
            "finish_reason": "stop"
        }],
        "usage": {
            "prompt_tokens": 9,
            "completion_tokens": 12,
            "total_tokens": 21
        }
    }
    return make_response(jsonify(result), HTTPStatus.OK)


def run(port=5006):
    mock_external_app.run(port=port, debug=True, use_reloader=False)


if __name__ == '__main__':
    run()
