from http import HTTPStatus
from typing import List

from app.models.collaboration.collaboration import CollaborationResourceType, CollaborationPermission
from tests.integration_tests.base_integration_test import TestIntegration, family_id
from tests.unit_tests.web_api import data


class TestIntegrationCollaborations(TestIntegration):

    def _create_result_collection(self, name=None, document_ids=None, monitor_run_id=None,
                                  search_history_id=None, search_hash=None,
                                  publication_numbers=None, collection_type=None):
        payload = {
            "name": name or "Test Collection",
            "search_history_id": search_history_id,
            "monitor_run_id": monitor_run_id,
            "search_hash": search_hash,
            "publication_numbers": publication_numbers,
            "collection_type": collection_type,
            "document_ids": document_ids
        }
        payload = {k: v for k, v in payload.items() if v}
        response = self.send_request('/web/result_collections', payload, 'post', HTTPStatus.CREATED)
        return response["data"]

    def _get_result_collection_by_share_code(self, share_code, expected_status=HTTPStatus.OK):
        response = self.send_request(f'/web/result_collections/shared/{share_code}', '', 'get', expected_status, None,
                                     True)
        return response

    def _test_users_of_collaboration_resource(self, user_id: int, resource_id: int,
                                              resource_type: CollaborationResourceType, expected_user_ids: List[int]):
        access_token = self._create_access_token(is_admin=False, user_id=user_id)
        _url = f'/web/collaborations/collaborators?resource_id={resource_id}&resource_type={resource_type.value}' \
               f'&collaborator_type=USER'
        resp = self.send_request(_url, None, "get", HTTPStatus.OK, access_token)
        assert set(expected_user_ids) == set([u['id'] for u in resp['data']['collaborators']])

    def _test_collaborations(self, resource_id: int, resource_type: CollaborationResourceType):
        user_ids = [2, 3]
        self._share_resource({'resource_id': resource_id, 'resource_type': resource_type.name,
                             "permission": CollaborationPermission.READONLY.name, "user_ids": user_ids})
        self._test_users_of_collaboration_resource(data.user_id, resource_id, resource_type, user_ids)
        self._share_resource({'resource_id': resource_id, 'resource_type': resource_type.name,
                             "permission": CollaborationPermission.READ_WRITE.name, "user_ids": user_ids})

    def test_collaborations(self):
        uid = self._generate_unique_id()
        collection = self._create_result_collection(name=f"Collaborations {uid}", document_ids=[family_id])
        self._test_collaborations(collection['id'], CollaborationResourceType.COLLECTION)
        self._test_collaborations(family_id, CollaborationResourceType.PATENT)
        self._test_collaborations(collection['id'], CollaborationResourceType.COLLECTION)  # Test re-share
