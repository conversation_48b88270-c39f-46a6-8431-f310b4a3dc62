import os
import random
import re
import string
from datetime import datetime
from http import HTTPStatus
from typing import List, Tu<PERSON>, Dict
import pytest
import requests
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from flask import current_app
from octimine_common.enums import SubscriptionType
from app.extensions import db
from app.models.company import CompanyModel
from app.models.stats_log.common_queries import CommonStatsQueries
from app.models.stats_log.custom.activity_log import UserCustomActivityLog
from app.models.stats_log.download.download_log import UserDownloadLog
from app.models.stats_log.search.fresh_search_log import UserFreshSearchLog
from app.models.stats_log.search.recurring_search_log import UserRecurringSearchLog
from app.models.stats_log.search.search_export_log import UserSearchExportLog
from app.models.stats_log.storer import StatsStorer
from app.models.user import <PERSON><PERSON><PERSON><PERSON><PERSON>ode<PERSON>, UserStatus, <PERSON><PERSON><PERSON>ser<PERSON>ode<PERSON>, UserModel, SubscriptionModel
from app.scheduled_tasks.clean_old_user_logs import clean_old_user_logs
from app.scheduled_tasks.trials_report import send_trials_report_email
from app.scheduled_tasks.convert_trial_users_to_free import convert_trial_users_to_free
from tests.unit_tests.auth_api.url import COMPANY_URL, TEAM_URL, GROUP_URL, USER_GROUP_URL
from tests.integration_tests.data import saml_response, idp_params, sp_params
from tests.integration_tests.constants import API_BASE_URL


class TestIntegration:
    token = None
    manager_stats = [(True, False, {"user_statistics": HTTPStatus.OK,
                                    "user_count": HTTPStatus.OK,
                                    "search_count": HTTPStatus.OK,
                                    "recurring_search_count": HTTPStatus.OK,
                                    "export_count": HTTPStatus.OK,
                                    "custom_activity_count": HTTPStatus.OK,
                                    "no_searches": HTTPStatus.OK,
                                    "most_searches": HTTPStatus.OK,
                                    "user_growth": HTTPStatus.OK,
                                    "active_user_growth": HTTPStatus.OK,
                                    "search_growth": HTTPStatus.OK,
                                    "average_query_length": HTTPStatus.OK,
                                    "most_trials": HTTPStatus.OK,
                                    "user_searches": HTTPStatus.OK,
                                    "user_history": HTTPStatus.OK,
                                    "company_statistics": HTTPStatus.OK}),
                     (False, True, {"user_statistics": HTTPStatus.OK,
                                    "user_count": HTTPStatus.FORBIDDEN,
                                    "search_count": HTTPStatus.FORBIDDEN,
                                    "recurring_search_count": HTTPStatus.OK,
                                    "export_count": HTTPStatus.OK,
                                    "custom_activity_count": HTTPStatus.OK,
                                    "no_searches": HTTPStatus.OK,
                                    "most_searches": HTTPStatus.OK,
                                    "user_growth": HTTPStatus.OK,
                                    "active_user_growth": HTTPStatus.OK,
                                    "search_growth": HTTPStatus.OK,
                                    "average_query_length": HTTPStatus.OK,
                                    "most_trials": HTTPStatus.OK,
                                    "user_searches": HTTPStatus.OK,
                                    "user_history": HTTPStatus.OK,
                                    "company_statistics": HTTPStatus.OK})]

    @classmethod
    def send_request(cls, path, request_dict=None, method='post', expected_status=HTTPStatus.CREATED,
                     auth_type='Bearer', auth_value=None, headers=None, files=None, response_json=True):
        if headers is None:
            headers = dict()
        if auth_type == 'Bearer' and (auth_value or cls.token):
            headers["Authorization"] = 'Bearer ' + str(auth_value or cls.token)

        server_response = requests.request(method, f"{API_BASE_URL}{path}",
                                           json=request_dict, headers=headers, files=files)
        assert server_response.status_code == expected_status.value, \
            "Unexpected server response: " + str(server_response) + server_response.text
        if response_json:
            return server_response.json() if server_response.content else dict()
        else:
            return server_response.content

    @classmethod
    def generate_token(cls, email, company_id=None):
        return generate_confirmation_link({'email': email, 'company_id': company_id})

    @classmethod
    def signup_and_login_new_app_user(cls, email, as_admin=False, as_sales=False, as_manager=False, company_id=None,
                                      subscription_type=None):
        password = "Abcd+12345"
        cls.signup_app_user(email, password, as_admin=as_admin, as_sales=as_sales, as_manager=as_manager,
                            company_id=company_id, subscription_type=subscription_type)
        access_token = cls.login_app_user(email, password)
        TestIntegration.token = access_token

    @classmethod
    def signup_app_user(cls, email, password, as_admin=False, as_sales=False, as_manager=False, company_id=None,
                        subscription_type=None):
        # Signup
        signup_request = {"password": password, "email": email, "first_name": "Integration", "last_name": "Test",
                          "token": "*********"}
        cls.send_request("/auth/signup", signup_request, auth_type='')

        # Confirm account
        confirmation_token = cls.generate_token(email)
        cls.send_request("/auth/signup_confirmation/" + confirmation_token, None, 'get', HTTPStatus.OK,
                         auth_type='')
        app_user = AppUserModel.query.filter_by(email=email).first()

        # Set admin rights
        if as_admin:
            UserModel.query.filter_by(id=app_user.id).update(dict(is_admin=True))

        # Set sales rights
        if as_sales:
            UserModel.query.filter_by(id=app_user.id).update(dict(is_sales=True))

        # Set manager rights
        if as_manager:
            UserModel.query.filter_by(id=app_user.id).update(dict(is_manager=True))

        # Set company
        if company_id:
            UserModel.query.filter_by(id=app_user.id).update(dict(company_id=company_id))

        # Set subscription type
        if subscription_type:
            SubscriptionModel.query.filter_by(user_id=app_user.id).update(dict(type=subscription_type))

        db.session.commit()

    @classmethod
    def login_app_user(cls, email, password, expected_status=HTTPStatus.OK):
        # Login and retrieve token
        login_request = {"username": email, "password": password}
        login_response = cls.send_request("/auth/login", login_request, 'post', expected_status)
        if expected_status == HTTPStatus.OK:
            return login_response["data"]["access_token"]

    def create_and_login_api_user(self, user_name: str = None, is_admin: bool = False):
        api_key, signature = self.create_api_user(user_name, is_admin)
        access_token = self.login_api_user(api_key, signature)
        TestIntegration.token = access_token

    def create_api_user(self, name: str = None, is_admin: bool = False):
        if not name:
            name = re.sub(r'\W+', '', str(datetime.now()))
        api_key = ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(32))
        crypto_key = rsa.generate_private_key(backend=default_backend(), public_exponent=65537, key_size=2048)
        public_key = crypto_key.public_key().public_bytes(serialization.Encoding.PEM,
                                                          serialization.PublicFormat.SubjectPublicKeyInfo)
        pss_padding = padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=32)
        signature = crypto_key.sign(api_key.encode(), pss_padding, hashes.SHA256()).hex()

        new_name = re.sub(r'\W+', '', str(datetime.now()))
        ApiUserModel.query.filter_by(name=name).update({'name': new_name})

        api_user = ApiUserModel(api_key=api_key, name=name, public_key=public_key.decode(),
                                status=UserStatus.Active, is_admin=is_admin)
        db.session.add(api_user)
        db.session.flush()
        subscription = SubscriptionModel(user_id=api_user.id)
        db.session.add(subscription)
        db.session.commit()
        return api_key, signature

    def login_api_user(self, api_key, signature):
        login_request = {"api_key": api_key, "signature": signature}
        login_response = self.send_request("/auth/api_token", login_request, 'post', HTTPStatus.OK)
        return login_response["data"]["access_token"]

    def logout_user(self, access_token=None):
        self.send_request('/auth/logout', None, 'delete', HTTPStatus.NO_CONTENT,
                          auth_type="Bearer", auth_value=access_token)
        TestIntegration.token = None

    def test_documentation(self):
        self.send_request('/auth/docs', '', 'get', HTTPStatus.OK)

    def test_login_app_user(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email)

        # Get user data
        profile_data = self._get_user_profile()
        assert profile_data["user"]
        assert profile_data["subscription"]

        self.logout_user()

    def test_login_app_user_rate_limiter(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email)
        self.logout_user()

        for _ in range(5):
            self.login_app_user(email, 'wrong_pass', HTTPStatus.UNAUTHORIZED)

        self.login_app_user(email, 'wrong_pass', HTTPStatus.TOO_MANY_REQUESTS)

    def test_forgot_password_rate_limiter(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email)
        self.logout_user()

        self._forgot_password(email, HTTPStatus.CREATED)
        self._forgot_password(email, HTTPStatus.TOO_MANY_REQUESTS)

    def test_previous_auth_revocation_after_app_user_logs_in(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        password = "Abcd+12345"
        self.signup_app_user(email=email, password=password)
        first_access_token = self.login_app_user(email=email, password=password)
        second_access_token = self.login_app_user(email=email, password=password)
        self.send_request('/auth/user/profile', None, 'get', HTTPStatus.UNAUTHORIZED, auth_value=first_access_token)
        self.send_request('/auth/user/profile', None, 'get', HTTPStatus.OK, auth_value=second_access_token)

    def test_login_api_user(self):
        self.create_and_login_api_user()
        # Get user data
        profile_data = self._get_user_profile()
        assert profile_data["user"]
        self.logout_user()

    def test_sign_up_user_with_company_subscription(self):
        # create amin
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)

        # create a company
        uid = self._generate_nonce()
        domain = f"{uid}.example.org"
        company = {
            "name": f"{uid} - Test Company",
            "active": True,
            "seats": 10,
            "free_seats": 2,
            "domains": [domain]
        }
        company_response = self.send_request(COMPANY_URL, company, 'post', HTTPStatus.CREATED)
        company_id = company_response['data']['id']

        # signup user matching company domain
        self.signup_and_login_new_app_user(self._auto_generate_email(domain=domain))
        profile_data = self._get_user_profile()
        assert profile_data['user']['company_id'] == company_id
        assert profile_data['subscription']['type'] == 'ENTERPRISE'

    def test_saml_connector(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)

        domain = f"{self._generate_nonce()}.example.org"
        company = {
            "name": f"{domain} - Test SAML",
            "active": True,
            "domains": [domain]
        }
        response = self.send_request(COMPANY_URL, company, 'post', HTTPStatus.CREATED)
        company_id = response['data']['id']

        connector = {
            'company_id': company_id,
            'params': {
                # This will disable extra validations (audience, timestamp, etc) since it is pretty difficult to test
                'strict': False,
                'debug': True,
                'sp': sp_params(0),  # Put a fake id to the sake of creating the connector, we will update it later
                'idp': idp_params,
            }
        }
        response = self.send_request('/auth/saml/connectors', connector, 'post', HTTPStatus.CREATED)
        connector_id = response['data']['connector']['id']
        connector['params']['sp'] = sp_params(connector_id)
        response = self.send_request(f'/auth/saml/connectors/{connector_id}', connector, 'patch', HTTPStatus.OK)
        self.send_request('/auth/saml/connectors', None, 'get', HTTPStatus.OK)
        self.send_request(f'/auth/saml/connectors/{connector_id}', None, 'get', HTTPStatus.OK)

        # retrieve metadata as XML
        response = requests.get(f'{API_BASE_URL}/auth/saml/metadata?connector_id={connector_id}')
        assert response.headers.get('Content-Type') == 'text/xml'
        assert f'connector_id={connector_id}' in response.text

        # Start SP initiated SSO flow
        response = requests.get(
            f'{API_BASE_URL}/auth/saml/sso?connector_id={connector_id}',
            allow_redirects=False
        )

        assert response.status_code == HTTPStatus.FOUND
        assert response.headers['Location']  # User should be redirected to the IdP for logging in

        # Send Fake response to ACS endpoint from IdP to test assertion
        response = requests.post(
            f'{API_BASE_URL}/auth/saml/acs?connector_id={connector_id}',
            data={'SAMLResponse': saml_response},
            allow_redirects=False
        )
        assert response.status_code == HTTPStatus.FOUND
        assert response.headers['Location']

        self.send_request(f'/auth/saml/connectors/{connector_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_previous_auth_keeps_after_api_user_logs_in(self):
        first_api_key, first_signature = self.create_api_user()
        first_access_token = self.login_api_user(first_api_key, first_signature)
        second_api_key, second_signature = self.create_api_user()
        second_access_token = self.login_api_user(second_api_key, second_signature)
        self.send_request('/auth/user/profile', None, 'get', HTTPStatus.OK, auth_value=first_access_token)
        self.send_request('/auth/user/profile', None, 'get', HTTPStatus.OK, auth_value=second_access_token)

    def test_password_change_and_reset(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id

        # Signup
        signup_request = {"password": "Abcd+12345", "email": email, "first_name": "Integration", "last_name": "Test",
                          "token": "*********"}
        self.send_request("/auth/signup", signup_request)

        # Request confirmation link again
        self.send_request("/auth/request_confirmation", {"email": email})

        # Confirm account
        confirmation_link = self.generate_token(email)
        self.send_request("/auth/signup_confirmation/" + confirmation_link, None, 'get', HTTPStatus.OK, auth_type='')

        # Request password reset
        self._forgot_password(email)

        # Reset password
        token = self.generate_token(email)
        self.send_request("/auth/password/reset", {"email": email, "new_password": "Abcd+123456", "token": token},
                          'put')

        # Login
        login_request = {"username": email, "password": "Abcd+123456"}
        login_response = self.send_request("/auth/login", login_request, 'post', HTTPStatus.OK)
        TestIntegration.token = login_response["data"]["access_token"]

        # Change password
        self.send_request("/auth/password/change",
                          {"email": email, "current_password": "Abcd+123456", "new_password": "Abcd+7894",
                           'terms_and_conditions': True, 'gdpr': True}, 'put')
        # Login again
        self.send_request("/auth/login", {"username": email, "password": "Abcd+7894"}, 'post', HTTPStatus.OK)

    def _forgot_password(self, email, expected_status=HTTPStatus.CREATED):
        self.send_request("/auth/password/forgot", {"email": email}, 'post',
                          expected_status=expected_status)

    def test_signup(self):
        email = self._auto_generate_email()
        # Signup
        signup_request = {"password": "Abcd+12345", "email": email, "first_name": "Integration", "last_name": "Test",
                          "token": "*********"}
        self.send_request("/auth/signup", signup_request)

        signup_request['email'] = f"           {email}          "
        self.send_request("/auth/signup", signup_request, expected_status=HTTPStatus.BAD_REQUEST)

        signup_request['email'] = f"           {email.upper()}          "
        self.send_request("/auth/signup", signup_request, expected_status=HTTPStatus.BAD_REQUEST)

    def test_manager_get_users(self):
        # Create user 1
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email)

        # Create user 2
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email, as_admin=True, subscription_type='COLLABORATOR')

        # Get both users
        response = self.send_request("/auth/user?name=" + user_id, None, 'get', HTTPStatus.OK)
        assert len(response["data"]["users"]) == 2

        # Filter by collaborator
        response = self.send_request("/auth/user?subscription_type=COLLABORATOR&name=" + user_id, None, 'get',
                                     HTTPStatus.OK)
        assert len(response["data"]["users"]) == 1

        # Filter by free
        response = self.send_request("/auth/user?subscription_type=FREE&name=" + user_id, None, 'get', HTTPStatus.OK)
        assert len(response["data"]["users"]) == 1

    def test_for_manager(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)

        # create a company
        company_data = self._create_simple_company()
        company_id = company_data['id']

        # create test user
        request_data = {"profile": {"email": self._auto_generate_email(), "first_name": "ABC", "last_name": "123",
                                    "country": "Germany", "company_id": company_id},
                        "subscription": {"type": "FREE"}}
        user_response = self._create_users(request_data, HTTPStatus.CREATED)
        assert 'data' in user_response
        assert user_response['data']['profile']['company_id'] == company_id
        assert user_response['data']['profile']['status'] == 'Registered'
        assert user_response['data']['subscription']['type'] == 'FREE'

        user_id = user_response['data']['profile']['id']

        # Create manager user
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_manager=True, company_id=company_id)

        # get test user
        self.send_request(f"/auth/user/{user_id}", None, 'get', HTTPStatus.OK)

        # update test user
        request_data = {"first_name": "ABC_updated", "last_name": "123_updated"}
        user_response = self.send_request(f"/auth/user/{user_id}", request_data, 'patch', HTTPStatus.OK)
        assert 'data' in user_response
        assert user_response['data']['profile']['first_name'] == 'ABC_updated'

        # get test user journal
        self.send_request(f"/auth/user/journals/{user_id}", None, 'get', HTTPStatus.FORBIDDEN)

        # listing team users
        users_response = self.send_request("/auth/users/team", None, 'get', HTTPStatus.OK)
        assert 'data' in users_response
        assert all([u['id'] == user_id for u in users_response['data']['users']])

    def test_for_manager_without_company_id(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)

        # Create manager user without company id
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_manager=True)

        # create test user
        request_data = {"profile": {"email": self._auto_generate_email(), "first_name": "ABC", "last_name": "123",
                                    "country": "Germany"}}
        self._create_users(request_data, HTTPStatus.FORBIDDEN)

        # listing team users
        self.send_request("/auth/user", None, 'get', HTTPStatus.FORBIDDEN)

    def test_for_manager_invite_user(self):

        # create test user
        self.signup_and_login_new_app_user(self._auto_generate_email())
        profile_data = self._get_user_profile()
        user_access_token = TestIntegration.token
        user_profile = profile_data['user']
        assert not user_profile['company_id']

        # create amin
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)
        admin_access_token = TestIntegration.token

        # create a company
        company_data = self._create_simple_company(admin_access_token)
        company_id = company_data['id']

        # Create manager user
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_manager=True, company_id=company_id)

        # invite user to manager's company
        request_data = {"email": user_profile['email']}
        self.send_request("/auth/users/invite", request_data, 'post', HTTPStatus.CREATED)

        # accept invitation
        token = self.generate_token(user_profile['email'], company_id)
        self.send_request("/auth/users/invite", {"token": token}, 'patch',
                          HTTPStatus.OK, auth_value=user_access_token)

        # check company id of user
        resp = self.send_request(f"/auth/user/{user_profile['id']}", None, 'get', HTTPStatus.OK)
        assert resp['data']['profile']['company_id'] == company_id

        # remove user from company
        self.send_request(f"/auth/users/invite/{user_profile['id']}", None, 'delete', HTTPStatus.NO_CONTENT)

        # check company id, subscription of user
        resp = self.send_request(f"/auth/user/{user_profile['id']}", None, 'get', HTTPStatus.OK,
                                 auth_value=admin_access_token)
        assert resp['data']['profile']['company_id'] is None
        assert not resp['data']['profile']['is_manager']
        assert resp['data']['subscription']['type'] == 'FREE'

    def test_update_user(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email, as_admin=True)

        # Get user
        response = self.send_request("/auth/user?name=" + user_id, None, 'get', HTTPStatus.OK)
        assert len(response["data"]["users"]) == 1
        user_id = response["data"]["users"][0]["profile"]["id"]

        # Update user status to Blocked
        update = {
            "status": "Blocked"
        }
        self.send_request("/auth/user/%s" % user_id, update, 'patch', HTTPStatus.OK)

        # Update user password
        update = {
            "password": "Abcd+1234"
        }
        response = self.send_request("/auth/user/%s" % user_id, update, 'patch', HTTPStatus.OK)
        assert response['data']['profile']['must_change_password']

    def test_delete_user(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email)
        # Schedule user for deletion
        self.send_request("/auth/user/profile", None, 'delete', HTTPStatus.NO_CONTENT)

    def test_login_delegated(self):

        self.create_and_login_delegated_user(None, True)

        # Get user data
        profile_data = self._get_user_profile()
        assert profile_data["user"]

        # should not delete delegated user
        self.send_request("/auth/user/profile", None, 'delete', HTTPStatus.FORBIDDEN)

        # should not change password for delegated user
        self.send_request("/auth/password/change",
                          {"email": profile_data["user"]['email'], "current_password": "Abcd+123456",
                           "new_password": "Abcd+7894",
                           'terms_and_conditions': True, 'gdpr': True}, 'put', HTTPStatus.FORBIDDEN)
        self.logout_user()

    def create_and_login_delegated_user(self, user_name: str = None, is_admin: bool = False, company: str = None):
        api_key, signature = self.create_delegated_user(user_name, is_admin, company)
        access_token = self.login_delegated_user(api_key, signature)
        TestIntegration.token = access_token

    def create_delegated_user(self, name: str = None, is_admin: bool = False, company: str = None):
        name = name if name else re.sub(r'\W+', '', str(datetime.now()))
        api_key = ''.join(random.choice(string.ascii_lowercase + string.digits) for _ in range(32))
        crypto_key = rsa.generate_private_key(backend=default_backend(), public_exponent=65537, key_size=2048)
        public_key = crypto_key.public_key().public_bytes(serialization.Encoding.PEM,
                                                          serialization.PublicFormat.SubjectPublicKeyInfo)

        pss_padding = padding.PSS(mgf=padding.MGF1(hashes.SHA256()), salt_length=32)
        signature = crypto_key.sign(api_key.encode(), pss_padding, hashes.SHA256()).hex()

        company = CompanyModel(name=company if company else f'{self._generate_nonce()} Test Company')
        db.session.add(company)
        db.session.flush()
        api_user = ApiUserModel(api_key=api_key, name=name, public_key=public_key.decode(),
                                status=UserStatus.Active, company_id=company.id, is_admin=is_admin)
        db.session.add(api_user)
        db.session.flush()
        subscription = SubscriptionModel(user_id=api_user.id)
        db.session.add(subscription)
        db.session.commit()
        return api_key, signature

    def login_delegated_user(self, api_key, signature):
        # Login and retrieve token
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        login_request = {
            "api_key": api_key, "signature": signature, "customer_id": 1, "customer_name":
                "Test Enterprise", "user_id": 2, "user_email": email, "user_name_first": "Max",
            "user_name_last": "Mustermann"}
        self.send_request("/auth/login_delegated", login_request, 'post', HTTPStatus.OK)
        login_response = self.send_request("/auth/login_delegated", login_request, 'post', HTTPStatus.OK)
        return login_response["data"]["access_token"]

    @pytest.mark.parametrize('is_admin, is_sales, expected_get_user_status, expected_impersonate_status', [
        (True, False, HTTPStatus.OK, HTTPStatus.OK),
        (False, True, HTTPStatus.OK, HTTPStatus.FORBIDDEN)
    ])
    def test_impersonate_user(self,
                              is_admin, is_sales, expected_get_user_status, expected_impersonate_status):
        # Create user 1
        user_email = self._auto_generate_email(domain='octimine.de')
        self.signup_and_login_new_app_user(user_email)

        # Create main user (admin/sales)
        main_email = self._auto_generate_email()
        self.signup_and_login_new_app_user(main_email, as_admin=is_admin, as_sales=is_sales)

        # Get user id
        response = self.send_request("/auth/user?name=" + user_email, None, 'get', expected_get_user_status)

        if is_admin:
            assert len(response["data"]["users"]) == 1
            user_id = response["data"]["users"][0]["profile"]["id"]
        else:
            user_id = 1

        # Impersonate user
        response = self.send_request('/auth/impersonate', {"user_id": user_id}, 'post', expected_impersonate_status)
        if is_admin:
            assert response['data']['access_token']

    def test_update_profile(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email)

        profile_data = self._get_user_profile()
        assert profile_data["user"]["phone1"] is None

        self.send_request('/auth/user/profile', {"phone1": "+4915158242420"}, 'patch', HTTPStatus.OK)

        profile_data = self._get_user_profile()
        assert profile_data["user"]["phone1"] == "+4915158242420"

    @pytest.mark.parametrize('is_admin, is_sales, expected_statuses', manager_stats)
    def test_manager_stats(self, is_admin, is_sales, expected_statuses):
        email = self._auto_generate_email()
        self.signup_and_login_new_app_user(email, as_admin=is_admin, as_sales=is_sales)

        self.send_request('/auth/user/statistics', None, 'get', expected_statuses["user_statistics"])
        self.send_request('/auth/statistics/user_count', None, 'get', expected_statuses["user_count"])
        self.send_request('/auth/statistics/search_count', None, 'get', expected_statuses["search_count"])
        self.send_request('/auth/statistics/recurring_search_count', None, 'get',
                          expected_statuses["recurring_search_count"])
        self.send_request('/auth/statistics/export_count', None, 'get', expected_statuses["export_count"])
        self.send_request('/auth/statistics/custom_activity_count', None, 'get',
                          expected_statuses["custom_activity_count"])
        self.send_request('/auth/statistics/no_searches', None, 'get', expected_statuses["no_searches"])
        self.send_request('/auth/statistics/most_searches', None, 'get', expected_statuses["most_searches"])
        self.send_request('/auth/statistics/user_growth', None, 'get', expected_statuses["user_growth"])
        self.send_request('/auth/statistics/active_user_growth', None, 'get',
                          expected_statuses["active_user_growth"])
        self.send_request('/auth/statistics/search_growth', None, 'get', expected_statuses["search_growth"])
        self.send_request('/auth/statistics/average_query_length', None, 'get',
                          expected_statuses["average_query_length"])
        self.send_request('/auth/statistics/most_trials', None, 'get', expected_statuses["most_trials"])
        self.send_request('/auth/statistics/user_searches/1', None, 'get', expected_statuses["user_searches"])
        self.send_request('/auth/statistics/user_history/1', None, 'get', expected_statuses["user_history"])
        self.send_request('/auth/statistics/company/1', None, 'get', expected_statuses["company_statistics"])

    def test_trials_report(self, mocker):
        mocker.patch('app.scheduled_tasks.trials_report._send_email')
        send_trials_report_email()

    def test_clean_user_logs(self):
        clean_old_user_logs()

    def test_user_features(self):
        # Create user 1
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email)

        # Create user 2
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email, as_admin=True)

        # Get all features
        response = self.send_request("/auth/features", None, 'get', HTTPStatus.OK)
        all_features = response['data']['features']

        # Get user
        response = self.send_request(f"/auth/user?name={user_id}", None, 'get', HTTPStatus.OK)
        user_id = response["data"]["users"][0]["profile"]["id"]

        # Disable user features
        self.send_request(f"/auth/user/{user_id}/features", {'feature_ids': []}, 'patch', HTTPStatus.OK)

        # Get user features
        response = self.send_request(f"/auth/user/{user_id}/features", None, 'get', HTTPStatus.OK)
        user_features = response['data']
        assert not user_features

        # Enable all features for user
        self.send_request(f"/auth/user/{user_id}/features", {'feature_ids': [f['id'] for f in all_features]}, 'patch',
                          HTTPStatus.OK)

        # Get user features
        response = self.send_request(f"/auth/user/{user_id}/features", None, 'get', HTTPStatus.OK)
        user_features = response['data']
        assert len(user_features) == len(all_features)

        # get test user journal
        journal = self.send_request(f"/auth/user/journals/{user_id}", None, 'get', HTTPStatus.OK)
        assert len(journal['data']['journals']) > 0
        assert journal['data']['journals'][0]['user_id'] == user_id
        assert len(journal['data']['journals'][0]['entries']) > 0

    def test_batch_user_update(self):
        # batch update as admin
        # Create user 1
        email_1 = self._auto_generate_email()
        self.signup_and_login_new_app_user(email_1)

        # Create user 2
        email_2 = self._auto_generate_email()
        self.signup_and_login_new_app_user(email_2, as_admin=True)

        user_ids = self._get_user_ids_from_emails([email_1, email_2])
        user_sales_ids = self._get_user_sales()

        self._update_users({"user_ids": user_ids}, HTTPStatus.OK)

        profile = {"user_ids": user_ids,
                   "profile": {"status": "Active", "is_admin": True, "is_sales": False}, "sale_ids": user_sales_ids}
        self._update_users(profile, HTTPStatus.OK)

        subscription = {"user_ids": user_ids,
                        "subscription": {"type": "PROFESSIONAL", "valid_until": "2020-10-23T00:00:00",
                                         "api_package": 1000, "api_access_throttle": 2000, "api_max_result": 3000,
                                         "api_max_monitor_profile": 20}}
        self._update_users(subscription, HTTPStatus.OK)

        for user_id in user_ids:
            profile, subscription = self._get_user(user_id)
            assert profile['status'] == 'Active'
            assert len(profile['sales']) == len(user_sales_ids)
            assert profile['is_admin']
            assert not profile['is_sales']
            assert subscription['type'] == 'PROFESSIONAL'
            assert subscription['api_package'] == 1000
            assert subscription['api_access_throttle'] == 2000
            assert subscription['api_max_result'] == 3000
            assert subscription['api_max_monitor_profile'] == 20

            # get test user journal
            journal = self.send_request(f"/auth/user/journals/{user_id}", None, 'get', HTTPStatus.OK)
            assert len(journal['data']['journals']) > 0
            assert journal['data']['journals'][0]['user_id'] == user_id
            assert len(journal['data']['journals'][0]['entries']) > 0

        features = {"user_ids": user_ids, "enabled_feature_ids": [1], "disabled_feature_ids": [2]}
        self._update_users(features, HTTPStatus.OK)

        features = {"user_ids": user_ids, "enabled_feature_ids": [1, 2], "disabled_feature_ids": [2]}
        self._update_users(features, HTTPStatus.BAD_REQUEST)

        for user_id in user_ids:
            features = self._get_features_of_user(user_id)
            assert len(features) == 1
            assert features[0]['id'] == 1

        no_attributes = {"user_ids": user_ids, "profile": {}, "subscription": {},
                         "enabled_feature_ids": [], "disabled_feature_ids": []}
        self._update_users(no_attributes, HTTPStatus.OK)

        user_sales_ids = self._get_user_sales()
        all_attributes = {"user_ids": user_ids,
                          "profile": {"status": "Active", "is_admin": True, "is_sales": True},
                          "subscription": {"type": "BASIC", "valid_until": "2020-10-23T00:00:00", "api_package": 1500,
                                           "api_access_throttle": 2500, "api_max_result": 3500,
                                           "api_max_monitor_profile": 25},
                          "enabled_feature_ids": [2], "disabled_feature_ids": [1], "sale_ids": user_sales_ids}
        self._update_users(all_attributes, HTTPStatus.OK)

        for user_id in user_ids:
            features = self._get_features_of_user(user_id)
            assert len(features) == 1
            assert features[0]['id'] == 2

        # batch update as sales
        # create sales user
        sales_email = self._auto_generate_email()
        self.signup_and_login_new_app_user(sales_email, as_sales=True)

        # update other user
        self._update_users({"user_ids": user_ids}, HTTPStatus.NOT_FOUND)

        # create test user
        email = self._auto_generate_email()

        request_data = {"profile": {"email": email, "first_name": "ABC", "last_name": "123",
                                    "country": "Germany"},
                        "subscription": {"type": "BASIC", "api_package": 1500, "api_access_throttle": 1200,
                                         "api_max_result": 1400, "api_max_monitor_profile": 10}, "feature_ids": [1, 2]}
        resp = self._create_users(request_data, HTTPStatus.CREATED)
        assert 'data' in resp
        assert resp['data']['profile']['email'] == email
        assert resp['data']['profile']['id'] is not None

        user_id = resp['data']['profile']['id']

        update_attributes = {"user_ids": [user_id],
                             "profile": {"status": "Active", "is_admin": True, "is_sales": True},
                             "subscription": {"type": "BASIC", "api_package": 1500,
                                              "api_access_throttle": 2500, "api_max_result": 3500,
                                              "api_max_monitor_profile": 15},
                             "disabled_feature_ids": [1]}
        resp = self._update_users(update_attributes, HTTPStatus.BAD_REQUEST)
        assert resp["details"]["profile"]["is_admin"] == ["Unknown field."]
        assert resp["details"]["profile"]["is_sales"] == ["Unknown field."]
        assert resp["details"]["subscription"]["api_access_throttle"] == ["Unknown field."]
        assert resp["details"]["subscription"]["api_max_result"] == ["Unknown field."]
        assert resp["details"]["subscription"]["api_package"] == ["Unknown field."]
        assert resp["details"]["subscription"]["api_max_monitor_profile"] == ["Unknown field."]

        update_attributes = {"user_ids": [user_id],
                             "profile": {"status": "Active"},
                             "subscription": {"type": "PROFESSIONAL"},
                             "enabled_feature_ids": [1]}
        resp = self._update_users(update_attributes, HTTPStatus.OK)

    @pytest.mark.parametrize('is_admin, is_sales, expected_statuses', [
        (True, False, {"create_main_user": HTTPStatus.CREATED,
                       "create_app_user": HTTPStatus.CREATED,
                       "invalid_data": HTTPStatus.BAD_REQUEST}),
        (False, True, {"create_main_user": HTTPStatus.BAD_REQUEST,
                       "create_app_user": HTTPStatus.CREATED,
                       "invalid_data": HTTPStatus.BAD_REQUEST})
    ])
    def test_create_user(self, is_admin, is_sales, expected_statuses):
        # Create user as admin
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=is_admin, as_sales=is_sales)

        email = self._auto_generate_email()

        user_sales_ids = self._get_user_sales()

        # create main user
        request_data = {"profile": {"email": email, "first_name": "ABC", "last_name": "123",
                                    "country": "Germany", "is_admin": True, "is_sales": True,
                                    "status": "Active", "company_id": "1"},
                        "subscription": {"type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200,
                                         "api_max_result": 1400, "api_max_monitor_profile": 15},
                        "feature_ids": [1, 2], "sale_ids": user_sales_ids}
        resp = self._create_users(request_data, expected_statuses["create_main_user"])
        if is_admin:
            assert 'data' in resp
            assert resp['data']['profile']['email'] == email
            assert resp['data']['profile']['id'] is not None
        else:
            assert "message" in resp
            assert resp["details"]["sale_ids"] == ["Unknown field."]
            assert resp["details"]["profile"]["is_admin"] == ["Unknown field."]
            assert resp["details"]["profile"]["company_id"] == ["Unknown field."]
            assert resp["details"]["profile"]["is_sales"] == ["Unknown field."]

        # create app user
        email = self._auto_generate_email()
        request_data['profile']["email"] = email
        request_data['profile'].pop('is_admin')
        request_data['profile'].pop('company_id')
        request_data['profile'].pop('is_sales')
        request_data.pop('sale_ids')
        resp = self._create_users(request_data, expected_statuses["create_app_user"])

        assert 'data' in resp
        assert resp['data']['profile']['email'] == email
        assert resp['data']['profile']['id'] is not None

        self._create_users(request_data, expected_statuses["invalid_data"])
        self._create_users({}, expected_statuses["invalid_data"])
        request_data['profile'].pop('email')
        self._create_users(request_data, expected_statuses["invalid_data"])
        request_data['profile']["email"] = f"           {email}          "
        self._create_users(request_data, expected_statuses["invalid_data"])
        request_data['profile']["email"] = f"           {email.upper()}          "
        self._create_users(request_data, expected_statuses["invalid_data"])

    def _get_user_ids_from_emails(self, emails: List[str]) -> List[int]:
        user_ids = []
        for email in emails:
            response = self.send_request(f"/auth/user?name={email}", None, 'get', HTTPStatus.OK)
            assert response['data']['users']
            user_ids.append(response['data']['users'][0]['profile']['id'])
        return user_ids

    def _get_user_sales(self) -> List[int]:
        response = self.send_request("/auth/user?sales_only=1&page_size=2", None, 'get', HTTPStatus.OK)
        return [u['profile']['id'] for u in response['data']['users']] \
            if 'data' in response and 'users' in response['data'] else []

    def _update_users(self, request_data, expected_status):
        batch_user_update_url = '/auth/users/batch'
        return self.send_request(batch_user_update_url, request_data, 'patch', expected_status)

    def _create_users(self, request_data, expected_status):
        batch_user_update_url = '/auth/user'
        return self.send_request(batch_user_update_url, request_data, 'post', expected_status)

    def _get_user(self, user_id) -> Tuple[Dict, Dict]:
        response = self.send_request(f"/auth/user/{user_id}", None, 'get', HTTPStatus.OK)
        assert 'data' in response
        return response['data']['profile'], response['data']['subscription']

    def _get_features_of_user(self, user_id) -> Tuple[Dict, Dict]:
        response = self.send_request(f"/auth/user/{user_id}/features", None, 'get', HTTPStatus.OK)
        assert 'data' in response
        return response['data']

    def test_company(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email, as_admin=True)

        response = self.send_request(COMPANY_URL, None, 'get', HTTPStatus.OK)
        count_companies = response['data']['page']['total_hits']

        companies_data = [
            {"name": f"{user_id}1 - Test Company"},
            {"name": f"{user_id}2 - Test Company", "default_feature_ids": [1]},
            {"name": f"{user_id}3 - Test Company", "default_feature_ids": [1, 2]},
            {"name": f"{user_id}4 - Test Company", "default_feature_ids": None},
            {"name": f"{user_id}5 - Test Company", "default_feature_ids": [1, 1, 1]},
            {"name": f"{user_id}6 - Test Company", "default_feature_ids": []},
            {"name": f"{user_id}7 - Test Company", "active": True, "valid_to": "2020-11-07T00:00:00"},
        ]
        company_ids = []
        for company in companies_data:
            response = self.send_request(COMPANY_URL, company, 'post', HTTPStatus.CREATED)
            company_ids.append(response['data']['id'])
            if 'default_feature_ids' in company:
                assert len(response['data']['default_features']) == len(set(company['default_feature_ids'] or []))
        count_companies += len(company_ids)

        for company in companies_data:
            req_body = {'default_feature_ids': company.get('default_feature_ids')}
            response = self.send_request(f'{COMPANY_URL}/{company_ids[0]}', req_body, 'patch', HTTPStatus.OK)
            if 'default_feature_ids' in company:
                assert len(response['data']['default_features']) == len(set(company['default_feature_ids'] or []))

        response = self.send_request(f'{COMPANY_URL}/{company_ids[0]}', None, 'get', HTTPStatus.OK)
        assert company_ids[0] == response['data']['id']

        response = self.send_request(COMPANY_URL, None, 'get', HTTPStatus.OK)
        new_count_companies = response['data']['page']['total_hits']
        assert count_companies == new_count_companies

        for company_id in company_ids:
            self.send_request(f'{COMPANY_URL}/{company_id}', None, 'delete', HTTPStatus.NO_CONTENT)
            count_companies -= 1

        response = self.send_request(COMPANY_URL, None, 'get', HTTPStatus.OK)
        new_count_companies = response['data']['page']['total_hits']
        assert count_companies == new_count_companies

    def test_all_companies(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)

        response = self.send_request(f'{COMPANY_URL}?load_all=1', None, 'get', HTTPStatus.OK)
        assert 'page' not in response['data']

    def test_stats_log(self):
        email = self._auto_generate_email()
        self.signup_and_login_new_app_user(email)
        user = AppUserModel.query.filter_by(email=email).one()
        StatsStorer.save({
            'user_id': user.id, 'type': "FRESH_SEARCH", 'search_type': "SEMANTIC", 'search_hash': 'shash'}
        )
        StatsStorer.save({
            'user_id': user.id, 'type': "FRESH_SEARCH", 'search_type': "SEMANTIC", 'search_hash': 'shash2',
            'patent_numbers': ["EP0000001"]}
        )
        StatsStorer.save({
            'user_id': user.id, 'type': "FRESH_SEARCH", 'search_type': "NPL"}
        )
        StatsStorer.save({
            'created_at': datetime.now().isoformat(),
            'user_id': user.id, 'type': "RECURRING_SEARCH", 'search_type': "SEMANTIC", 'search_hash': 'shash'}
        )
        StatsStorer.save({
            'user_id': user.id, 'type': "SEARCH_EXPORT", 'export_format': "pdf", 'size': 1000}
        )
        StatsStorer.save({
            'user_id': user.id, 'type': "SEARCH_EXPORT", 'export_format': "csv", 'size': 50}
        )
        StatsStorer.save({
            'user_id': user.id, 'type': "DOWNLOAD", 'format': "pdf", 'source_type': 'PATENT_FILE', 'size': 1000}
        )
        StatsStorer.save({
            'created_at': datetime.now().isoformat(),
            'user_id': user.id, 'type': "CUSTOM_EVENT"}
        )
        assert CommonStatsQueries.entries_for_user(UserFreshSearchLog, user.id) == 3
        assert CommonStatsQueries.entries_for_user(UserRecurringSearchLog, user.id) == 1
        assert CommonStatsQueries.entries_for_user(UserSearchExportLog, user.id) == 2
        assert CommonStatsQueries.entries_for_user(UserCustomActivityLog, user.id) == 1
        assert CommonStatsQueries.entries_for_user(UserCustomActivityLog, user.id) == 1
        assert CommonStatsQueries.entries_for_user(UserDownloadLog, user.id) == 1

    def test_create_stats_log_from_api(self):
        self.signup_and_login_new_app_user(self._auto_generate_email())
        self.send_request('/auth/user/statistics/logs', {'type': "PREVIEW_PATENT"}, 'post')
        self.send_request('/auth/user/statistics/logs', {'type': "WRONG"}, 'post', HTTPStatus.BAD_REQUEST)

    def test_company_seat_restrictions(self):
        email_1 = self._auto_generate_email()
        password_1 = self._auto_generate_password()
        email_2 = self._auto_generate_email()
        password_2 = self._auto_generate_password()
        company = CompanyModel(name=f"{self._generate_nonce()} - Test Company (Seats)", seats=1)
        db.session.add(company)
        db.session.add(AppUserModel(email=email_1, password=password_1, status=UserStatus.Active, company=company,
                                    subscription=SubscriptionModel(type=SubscriptionType.ENTERPRISE)))
        db.session.add(AppUserModel(email=email_2, password=password_2, status=UserStatus.Active, company=company,
                                    subscription=SubscriptionModel(type=SubscriptionType.ENTERPRISE)))
        db.session.commit()
        access_token = self.login_app_user(email_1, password_1)
        self.login_app_user(email_2, password_2, HTTPStatus.FORBIDDEN)
        self.logout_user(access_token)
        self.login_app_user(email_2, password_2)

    def test_user_attached_to_company_via_domain_on_signup(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)
        uid = self._generate_nonce()
        domain = f"{uid}.example.org"
        company = {"name": f"{uid} - Test Company", "active": True, "domains": [domain]}
        response = self.send_request(COMPANY_URL, company, 'post', HTTPStatus.CREATED)
        company_id = response['data']['id']
        response = self.send_request(f'{COMPANY_URL}/{company_id}', None, 'get', HTTPStatus.OK)
        assert response['data']['count_users'] == 0
        email = self._auto_generate_email(domain=domain)
        password = self._auto_generate_password()
        self.signup_app_user(email=email, password=password)
        response = self.send_request(f'{COMPANY_URL}/{company_id}', None, 'get', HTTPStatus.OK)
        assert response['data']['count_users'] == 1

    def test_team_users(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)
        uid = self._generate_nonce()
        domain = f"{uid}.example.org"
        company = {"name": f"{uid} - Test Company", "active": True, "domains": [domain]}
        self.send_request(COMPANY_URL, company, 'post', HTTPStatus.CREATED)

        other_email = self._auto_generate_email(domain=domain)
        other_password = self._auto_generate_password()
        self.signup_app_user(email=other_email, password=other_password)
        my_email = self._auto_generate_email(domain=domain)
        my_password = self._auto_generate_password()
        self.signup_app_user(email=my_email, password=my_password)
        access_token = self.login_app_user(my_email, my_password)
        TestIntegration.token = access_token

        profile_data = self._get_user_profile()

        team_users_response = self.send_request(TEAM_URL, None, 'get', HTTPStatus.OK)
        assert 'users' in team_users_response['data']
        assert len(team_users_response['data']['users']) > 0

        team_user_ids = [u['id'] for u in team_users_response['data']['users']]
        assert profile_data['user']['id'] not in team_user_ids

        url = TEAM_URL + '?email=eq:' + other_email
        filtered_team_users_response = self.send_request(url, None, 'get', HTTPStatus.OK)
        assert len(filtered_team_users_response['data']['users']) == 1
        assert filtered_team_users_response['data']['users'][0]['email'] == other_email

        url = f'{TEAM_URL}?fullname={requests.utils.quote("like:%test%")}'
        filtered_team_users_response = self.send_request(url, None, 'get', HTTPStatus.OK)
        assert len(filtered_team_users_response['data']['users']) == 1

        self.send_request(f'{TEAM_URL}/{team_user_ids[0]}', None, 'get', HTTPStatus.OK)

    def test_user_image(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)

        with open(os.path.join(current_app.instance_path, '..', 'templates', 'mail', 'logo.png'), mode='rb') as f:
            files = {'file': ('logo.png', f, 'image/png')}
            headers = {'enctype': 'multipart/form-data'}
            self.send_request(path="/auth/user/image", method='post', expected_status=HTTPStatus.NO_CONTENT,
                              headers=headers, files=files)

        profile_data = self._get_user_profile()
        self.send_request(path=f"/auth/user/{profile_data['user']['id']}/image", method='get',
                          expected_status=HTTPStatus.OK, response_json=False)

        self.send_request(path='/auth/user/image', method='delete', expected_status=HTTPStatus.NO_CONTENT)

    @classmethod
    def _auto_generate_email(cls, domain="octimine.com"):
        return f"it-{cls._generate_nonce()}@{domain}"

    @classmethod
    def _auto_generate_password(cls):
        return "Abcde12345$"

    @classmethod
    def _generate_nonce(cls):
        return re.sub(r'\W+', '', str(datetime.now()))

    def test_ip_lounge_companies_synchronization(self):
        user_id = re.sub(r'\W+', '', str(datetime.now()))
        email = "<EMAIL>" % user_id
        team_user_1 = "<EMAIL>" % user_id
        team_user_2 = "<EMAIL>" % user_id
        self.signup_and_login_new_app_user(email, as_admin=True)

        payload = {'company_name': 'IP lounge Test',
                   'users': [
                       {'email': team_user_1},
                       {'email': team_user_2}
                   ]}

        self.send_request('/auth/integrations/ip-lounge/companies/synchronization', None, 'post',
                          HTTPStatus.BAD_REQUEST)
        response = self.send_request('/auth/integrations/ip-lounge/companies/synchronization', payload, 'post',
                                     HTTPStatus.OK)
        company_id = response['data']['company_id']
        assert response['data']['company_id']
        assert response['data']['users']['added'] == 2
        assert response['data']['users']['updated'] == 0
        assert response['data']['users']['removed'] == 0

        payload['users'].pop()
        payload['company_id'] = company_id
        response = self.send_request('/auth/integrations/ip-lounge/companies/synchronization', payload, 'post',
                                     HTTPStatus.OK)
        assert response['data']['company_id'] == company_id
        assert response['data']['users']['added'] == 0
        assert response['data']['users']['updated'] == 1
        assert response['data']['users']['removed'] == 1

        payload['users'].pop()
        response = self.send_request('/auth/integrations/ip-lounge/companies/synchronization', payload, 'post',
                                     HTTPStatus.OK)
        assert response['data']['company_id'] == company_id
        assert response['data']['users']['added'] == 0
        assert response['data']['users']['updated'] == 0
        assert response['data']['users']['removed'] == 1

        user_ids = self._get_user_ids_from_emails([email])
        # remove company id from users
        payload = {"user_ids": user_ids,
                   "profile": {"company_id": None}}
        self._update_users(payload, HTTPStatus.OK)
        # delete company
        self.send_request(f'{COMPANY_URL}/{company_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        # Deleted user account
        self.send_request('/auth/user/profile', None, 'delete', HTTPStatus.NO_CONTENT)

    def _get_user_profile(self):
        response = self.send_request('/auth/user/profile', None, 'get', HTTPStatus.OK)
        assert 'data' in response
        return response['data']

    def _create_simple_company(self, auth_value=None):
        company = {"name": f"{self._generate_nonce()} - Test Company", "active": True}
        response = self.send_request(COMPANY_URL, company, 'post', HTTPStatus.CREATED, auth_value=auth_value)
        assert 'data' in response
        return response['data']

    def _get_groups_of_company(self):
        response = self.send_request(GROUP_URL, None, 'get', HTTPStatus.OK)
        return response['data']

    def _get_groups_of_user(self, user_id: int):
        response = self.send_request(f'{USER_GROUP_URL}/{user_id}/groups', None, 'get', HTTPStatus.OK)
        return response['data']

    def _get_users_of_group(self, group_id: int):
        response = self.send_request(f'{GROUP_URL}/{group_id}/users', None, 'get', HTTPStatus.OK)
        return response['data']

    def test_user_group(self):
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True)
        company_data = self._create_simple_company()
        self.signup_and_login_new_app_user(self._auto_generate_email(), as_admin=True, company_id=company_data['id'])

        initial_groups = self._get_groups_of_company()
        count_initial_groups = initial_groups['page']['total_hits']

        group_data = dict(name="Group " + self._generate_nonce(), description=str(datetime.now()))
        response = self.send_request(GROUP_URL, group_data, 'post', HTTPStatus.CREATED)
        group_id = response['data']['id']

        added_groups = self._get_groups_of_company()
        assert count_initial_groups + 1 == added_groups['page']['total_hits']

        group_data = dict(name="Group " + self._generate_nonce(), description=str(datetime.now()))
        self.send_request(f'{GROUP_URL}/{group_id}', group_data, 'patch', HTTPStatus.OK)

        updated_groups = self._get_groups_of_company()
        assert count_initial_groups + 1 == updated_groups['page']['total_hits']

        profile_data = self._get_user_profile()
        user_id = profile_data['user']['id']

        initial_groups_of_user = self._get_groups_of_user(user_id)
        count_initial_groups_of_user = len(initial_groups_of_user['groups'])

        initial_users_of_group = self._get_users_of_group(group_id)
        count_initial_users_of_group = len(initial_users_of_group['users'])

        self.send_request(f'{GROUP_URL}/{group_id}/users/{user_id}', None, 'put', HTTPStatus.NO_CONTENT)

        updated_groups_of_user = self._get_groups_of_user(user_id)
        count_updated_groups_of_user = len(updated_groups_of_user['groups'])

        updated_users_of_group = self._get_users_of_group(group_id)
        count_updated_users_of_group = len(updated_users_of_group['users'])

        assert count_initial_groups_of_user + 1 == count_updated_groups_of_user
        assert count_initial_users_of_group + 1 == count_updated_users_of_group

        self.send_request(f'{GROUP_URL}/{group_id}/users/{user_id}', None, 'delete', HTTPStatus.NO_CONTENT)

        updated_groups_of_user = self._get_groups_of_user(user_id)
        count_updated_groups_of_user = len(updated_groups_of_user['groups'])

        updated_users_of_group = self._get_users_of_group(group_id)
        count_updated_users_of_group = len(updated_users_of_group['users'])

        assert count_initial_groups_of_user == count_updated_groups_of_user
        assert count_initial_users_of_group == count_updated_users_of_group

        # Delete group
        self.send_request(f'{GROUP_URL}/{group_id}', None, 'delete', HTTPStatus.NO_CONTENT)

    def test_convert_trial_users_to_free(self):
        convert_trial_users_to_free()

    def test_login_method(self):
        payload = dict(email='<EMAIL>')
        self.send_request("/auth/login/method", payload, 'post', HTTPStatus.OK)
