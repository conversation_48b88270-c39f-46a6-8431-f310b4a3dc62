from http import HTTPStatus
from tests.integration_tests.base_integration_test import TestIntegration, family_ids


class TestIntegrationMisc(TestIntegration):

    def test_documentation(self):
        self.send_request('/web/docs', '', 'get', HTTPStatus.OK)
        self.send_request('/web/monitor/docs', '', 'get', HTTPStatus.OK)
        self.send_request('/web/landscape/docs', '', 'get', HTTPStatus.OK)

    def test_read_document(self):
        self.send_request('/web/read_document', '', 'delete', HTTPStatus.NO_CONTENT)
        self.send_request('/web/read_document/', '', "get", HTTPStatus.NOT_FOUND)
        self.send_request('/web/read_document/popstar', '', "get", HTTPStatus.NOT_FOUND)
        response = self.send_request('/web/read_document', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0

        self.send_request(f"/web/read_document/{family_ids[0]}", "", "put", HTTPStatus.OK)
        self.send_request(f"/web/read_document/{family_ids[1]}", "", "put", HTTPStatus.OK)

        response = self.send_request("/web/read_document", '', "get", HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 2

        response = self.send_request("/web/read_document/team", '', "get", HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 2

        self.send_request(f'/web/read_document/{family_ids[0]}', '', 'delete', HTTPStatus.NO_CONTENT)

        response = self.send_request('/web/read_document', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 1

        self.send_request(f"/web/read_document/{family_ids[0]}", "", "put", HTTPStatus.OK)
        self.send_request('/web/read_document', '', 'delete', HTTPStatus.NO_CONTENT)

        response = self.send_request('/web/read_document', '', 'get', HTTPStatus.OK)
        assert response['data']['page']['total_hits'] == 0

    def test_notifications(self):
        self.send_request('/web/notifications', '', 'get', HTTPStatus.OK)
        self.send_request('/web/notifications/read', '', 'post', HTTPStatus.OK)
        self.send_request('/web/notifications/read', {'resource_type': 'PATENT', 'resource_id': 1}, 'post',
                          HTTPStatus.OK)

    def test_applicant_aliases(self):
        response = self.send_request('/web/alias/applicant', '', 'get', HTTPStatus.OK)
        number_of_aliases = len(response['data']['applicant_aliases'])
        response = self.send_request('/web/alias/applicant', [{'applicant': 'Test1', 'alias': 'TestA'},
                                                              {'applicant': 'Test2', 'alias': 'TestB'}])
        alias_id = str(response['data'][0]['id'])
        self.send_request('/web/alias/applicant/' + alias_id, '', 'get', HTTPStatus.OK)
        self.send_request('/web/alias/applicant/' + alias_id, '', 'delete', HTTPStatus.NO_CONTENT)
        response = self.send_request('/web/alias/applicant', '', 'get', HTTPStatus.OK)
        number_of_aliases_after = len(response['data']['applicant_aliases'])
        assert number_of_aliases + 1 == number_of_aliases_after
