# Standard lib imports
import os
from http import HTTPStatus

import pytest
from flask import current_app

from octimine_common.exceptions import NotFoundException
from octimine_common.tests.data import access_token
# Local imports
from werkzeug.datastructures import FileStorage

from app.models.user import UserImageModel
from tests.unit_tests.auth_api import data
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.url import USER_IMAGE_ID_URL, USER_IMAGE_URL


@pytest.fixture
def patch_get_user_image(mocker):
    with open(os.path.join(current_app.instance_path, '..', 'templates', 'mail', 'logo.png'), mode='rb') as file:
        user_image = UserImageModel(user_id=data.user_id, image=file.read(), content_type='image/png')
        mocker.patch('app.models.user.UserImageModel.get_one', return_value=user_image)


class TestUserImage:
    @pytest.mark.usefixtures('patch_get_user_image')
    def test_get_user_image_successfully(self, client, mocker):
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(USER_IMAGE_ID_URL % data.user_id, headers=headers)
        assert_status(rv, HTTPStatus.OK)

    def test_get_user_image_not_successfully(self, client, mocker):
        mocker.patch('app.models.user.UserImageModel.get_one', side_effect=NotFoundException())
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(USER_IMAGE_ID_URL % 1, headers=headers)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    file_parameters = [
        ('document/pdf', 1024, HTTPStatus.BAD_REQUEST),
        ('document/pdf', 513 * 1024, HTTPStatus.BAD_REQUEST),
        ('document/pdf', 0, HTTPStatus.BAD_REQUEST),
        ('image/png', 1024 * 1024, HTTPStatus.BAD_REQUEST),
        ('image/png', 513 * 1024, HTTPStatus.BAD_REQUEST),
        ('image/png', 0, HTTPStatus.BAD_REQUEST),
        ('image/jpeg', 1024 * 1024, HTTPStatus.BAD_REQUEST),
        ('image/jpeg', 513 * 1024, HTTPStatus.BAD_REQUEST),
        ('image/jpeg', 0, HTTPStatus.BAD_REQUEST),
    ]

    @pytest.mark.parametrize('content_type, content_length, expected_status', file_parameters)
    def test_update_user_image_with_invalid_image(self, client, mocker, content_type, content_length, expected_status):
        mocker.patch('app.routes.auth.user.image.len', return_value=content_length)
        headers = dict(Authorization="Bearer %s" % access_token, enctype='multipart/form-data')

        with open(os.path.join(current_app.instance_path, '..', 'templates', 'mail', 'logo.png'), mode='rb') as file:
            img = FileStorage(stream=file, filename="logo.png", content_type=content_type)
            rv = client.post(USER_IMAGE_URL, headers=headers, data={'file': img})
            assert_status(rv, expected_status)

    file_parameters = [
        ('image/jpeg', 1024, HTTPStatus.NO_CONTENT),
        ('image/jpeg', 512 * 1024, HTTPStatus.NO_CONTENT),
        ('image/jpeg', 1, HTTPStatus.NO_CONTENT),
        ('image/png', 1024, HTTPStatus.NO_CONTENT),
        ('image/png', 512 * 1024, HTTPStatus.NO_CONTENT),
        ('image/png', 1, HTTPStatus.NO_CONTENT),
    ]

    @pytest.mark.parametrize('content_type, content_length, expected_status', file_parameters)
    def test_add_user_image_successfully(self, client, mocker, content_type, content_length, expected_status):
        mocker.patch('app.routes.auth.user.image.len', return_value=content_length)
        mocker.patch('app.models.user.UserImageModel.get_by_user', return_value=None)
        mocker.patch('app.models.user.UserImageModel.save')
        headers = dict(Authorization="Bearer %s" % access_token, enctype='multipart/form-data')

        with open(os.path.join(current_app.instance_path, '..', 'templates', 'mail', 'logo.png'), mode='rb') as file:
            img = FileStorage(stream=file, filename="logo.png", content_type=content_type)
            rv = client.post(USER_IMAGE_URL, headers=headers, data={'file': img})
            assert_status(rv, expected_status)

    @pytest.mark.parametrize('content_type, content_length, expected_status', file_parameters)
    def test_update_user_image_successfully(self, client, mocker, content_type, content_length, expected_status):
        mocker.patch('app.routes.auth.user.image.len', return_value=content_length)
        mocker.patch('app.models.user.UserImageModel.get_by_user',
                     return_value=UserImageModel(user_id=data.user_id))
        mocker.patch('app.models.user.UserImageModel.save')
        headers = dict(Authorization="Bearer %s" % access_token, enctype='multipart/form-data')

        with open(os.path.join(current_app.instance_path, '..', 'templates', 'mail', 'logo.png'), mode='rb') as file:
            img = FileStorage(stream=file, filename="logo.png", content_type=content_type)
            rv = client.post(USER_IMAGE_URL, headers=headers, data={'file': img})
            assert_status(rv, expected_status)

    delete_parameters = [
        (None, HTTPStatus.NOT_FOUND),
        (UserImageModel(user_id=data.user_id), HTTPStatus.NO_CONTENT),
    ]

    @pytest.mark.parametrize('user_image, expected_status', delete_parameters)
    def test_delete_user_image(self, client, mocker, user_image, expected_status):
        if user_image is None:
            mocker.patch('app.models.user.UserImageModel.get_one', side_effect=NotFoundException())
        else:
            mocker.patch('app.models.user.UserImageModel.get_one', return_value=user_image)
        mocker.patch('app.models.user.UserImageModel.delete')
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.delete(USER_IMAGE_URL, headers=headers)
        assert_status(rv, expected_status)
