from http import HTTPStatus
import pytest
from octimine_common.tests.data import access_token
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.data import app_user
from app.models.company import CompanyModel
from tests.unit_tests.auth_api.mocks.jwt import get_admin_jwt_claims


class TestIPLoungeCompanySynchronization:

    def test_no_auth(self, client):
        rv = client.post('/auth/integrations/ip-lounge/companies/synchronization')
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    @pytest.mark.parametrize('payload, expected_status', [
        ({}, HTTPStatus.BAD_REQUEST),
        ({'company_id': 1}, HTTPStatus.BAD_REQUEST),
        ({'company_id': "Test", 'users': [{'email': "<EMAIL>"}]}, HTTPStatus.BAD_REQUEST),
        ({'company_id': 1, 'users': [{'email': ""}]}, HTTPStatus.BAD_REQUEST),
        ({'users': [{'email': "<EMAIL>"}]}, HTTPStatus.BAD_REQUEST),
        ({'company_name': "Test", 'users': []}, HTTPStatus.OK),
        ({'company_name': "Test", 'users': [{'email': "<EMAIL>"}]}, HTTPStatus.OK),
        ({'company_id': 1, 'users': [{'email': "<EMAIL>"}]}, HTTPStatus.OK),
        ({
             'company_id': 1,
             'users': [{'email': "<EMAIL>"}, {'email': "<EMAIL>"}]
         }, HTTPStatus.OK),
    ])
    def test_synchronization(self, client, mocker, payload, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=app_user)
        mocker.patch('app.models.company.CompanyModel._is_name_taken', return_value=False)
        if payload.get('company_id', None):
            company = CompanyModel(id=payload.get('company_id'))
            mocker.patch('app.models.company.CompanyModel.get_one', return_value=company)
        mocker.patch('app.models.user.AppUserModel.get_by_company_id', return_value=[])
        mocker.patch('app.models.user.AppUserModel.get', return_value=None)
        mocker.patch('app.models.user.AppUserModel.update_features_by_names')
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.post('/auth/integrations/ip-lounge/companies/synchronization', headers=headers, json=payload)
        assert_status(rv, expected_status)
