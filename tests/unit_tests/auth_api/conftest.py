# Third party imports
import pytest
from fakeredis import FakeServer, FakeStrictRedis

from app.app import session_store
# Local imports
from octimine_common.tests.data import access_jti, refresh_jti


@pytest.fixture(autouse=True)
def redis(app, mocker):
    redis_server = FakeServer()
    redis_client = FakeStrictRedis(server=redis_server, decode_responses=True)
    redis_client.set(access_jti, "false")
    redis_client.set(refresh_jti, "false")
    mocker.patch.object(session_store, "_redis_client", redis_client)
    pass


@pytest.fixture
def redis_hash_store(mocker):
    mocker.patch('redis.StrictRedis.hset')
    mocker.patch('redis.StrictRedis.hincrby')
    mocker.patch('redis.StrictRedis.delete')
    mocker.patch('redis.StrictRedis.hgetall', return_value={})
    mocker.patch('redis.StrictRedis.expire', return_value=None)


@pytest.fixture
def redis_value_store(mocker):
    mocker.patch('redis.StrictRedis.set')
    mocker.patch('redis.StrictRedis.delete')
    mocker.patch('redis.StrictRedis.get', return_value=None)
    mocker.patch('redis.StrictRedis.expire', return_value=None)


@pytest.fixture(autouse=True)
def sqlalchemy_mock(app, mocker):
    mocker.patch('sqlalchemy.orm.Session.add')
    mocker.patch('sqlalchemy.orm.Session.delete')
    mocker.patch('sqlalchemy.orm.Session.merge')
    mocker.patch('sqlalchemy.orm.Session.commit')
    mocker.patch('sqlalchemy.orm.Session.flush')
    mocker.patch('sqlalchemy.orm.Session.refresh')


@pytest.fixture(name="blacklisted_token")
def token_in_blacklist(redis):
    session_store.set(access_jti, "true")
    session_store.set(refresh_jti, "true")


@pytest.fixture(autouse=True)
def models(app, mocker):
    mocker.patch('app.models.stats_log.search.fresh_search_log.UserFreshSearchLog.user_monthly_usage',
                 return_value=0)
