from http import HTTPStatus
import pytest
from octimine_common.exceptions import NotFoundException
from octimine_common.enums import SubscriptionType
from octimine_common.tests.data import access_token
from octimine_common.ext.mail.confirmation import ConfirmationToken
from app import messages
from app.models.user import AppUserModel, UserModel, SubscriptionModel
from app.models.company import CompanyModel
from app.models.feature import FeatureModel
from app.models.user_journal import UserJournalModel
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.data import api_user, app_user, build_feature, build_app_user, user_id
from tests.unit_tests.auth_api.mocks.jwt import get_admin_jwt_claims, get_sale_jwt_claims, \
    get_team_manager_jwt_claims, get_customized_jwt_claims
from tests.unit_tests.auth_api.url import MANAGER_USER_URL, MANAGER_SUBSCRIPTION_URL, MANAGER_INVITE_USER_URL


class TestManager:

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_get_users_wrong_user_type(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(MANAGER_USER_URL + '?type=nonsense', headers=headers)
        assert_status(rv, HTTPStatus.BAD_REQUEST, messages.ERROR_WRONG_USER_TYPE)

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_get_users_wrong_subscription_type(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(MANAGER_USER_URL + '?type=app&subscription_type=nonsense', headers=headers)
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_get_users_empty_list(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('sqlalchemy.orm.Query.all', return_value=[])
        mocker.patch('sqlalchemy.orm.Query.count', return_value=0)
        rv = client.get(MANAGER_USER_URL + '?type=app&subscription=enterprise', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.get_json()['data']['users'] == []

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_get_users(self, client, mocker, claims):
        mocker.patch('app.models.feature.FeatureModel.get_all', return_value=[])
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('sqlalchemy.orm.Query.all', return_value=[api_user])
        mocker.patch('sqlalchemy.orm.Query.count', return_value=1)
        rv = client.get(MANAGER_USER_URL + '?type=app&subscription=free', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert len(rv.get_json()['data']['users']) == 1


class TestManagerSubscription:
    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_bad_updates(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.UserModel.get_by_user_id',
                     return_value=(
                         AppUserModel(id=1, subscription=SubscriptionModel(id=1,
                                                                           type=SubscriptionType.PROFESSIONAL))))
        headers = dict(Authorization="Bearer %s" % access_token)
        bad_value = []
        rv = client.patch(MANAGER_SUBSCRIPTION_URL % '1', headers=headers, json={"api_access_throttle": bad_value})
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_update_subscription_not_found(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.UserModel.get_by_user_id', side_effect=NotFoundException)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(MANAGER_SUBSCRIPTION_URL % '1', headers=headers, json={})
        assert_status(rv, HTTPStatus.NOT_FOUND)

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_update_subscription_successful(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.subscription_change_email.SubscriptionChangeEmail.cleanup')
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=app_user)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        body = {
            "type": "ENTERPRISE",
            "api_package": 200000,
            "valid_until": "2020-02-29T02:45:14"
        }
        rv = client.patch(MANAGER_SUBSCRIPTION_URL % '1', headers=headers, json=body)
        assert_status(rv, HTTPStatus.OK)


class TestManagerUser:
    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims(), get_team_manager_jwt_claims()
    ])
    def test_update_user_not_found(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.UserModel.get_by_user_id', side_effect=NotFoundException)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(MANAGER_USER_URL + '/1', headers=headers, json={})
        assert_status(rv, HTTPStatus.NOT_FOUND)

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims(), get_team_manager_jwt_claims()
    ])
    def test_get_user(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=api_user)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(MANAGER_USER_URL + '/1', headers=headers)
        assert_status(rv, HTTPStatus.OK)

    @pytest.mark.parametrize('user_data, claims, expected_status', [
        ({"status": "Blocked", "last_name": "Idiot"}, get_admin_jwt_claims(), HTTPStatus.OK),
        ({"status": "Blocked", "is_admin": True}, get_sale_jwt_claims(), HTTPStatus.BAD_REQUEST),
        ({"status": "Blocked", "last_name": "Idiot"}, get_sale_jwt_claims(), HTTPStatus.OK),
        ({"first_name": "User", "last_name": "Idiot"}, get_team_manager_jwt_claims(), HTTPStatus.OK),
        ({"first_name": "User", "last_name": "Idiot", "is_admin": True}, get_team_manager_jwt_claims(),
         HTTPStatus.BAD_REQUEST),
        ({"status": "Blocked", "last_name": "Idiot"}, get_team_manager_jwt_claims(), HTTPStatus.BAD_REQUEST),
    ])
    def test_update_user(self, client, mocker, user_data, claims, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=app_user)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(MANAGER_USER_URL + '/1', headers=headers, json=user_data)
        assert_status(rv, expected_status)

    @pytest.mark.parametrize('password, is_admin, expected_status', [
        ("bad_password", True, HTTPStatus.BAD_REQUEST),
        ("Abcd+1234", False, HTTPStatus.FORBIDDEN),
        ("Abcd+1234", True, HTTPStatus.OK)
    ])
    def test_update_user_password(self, client, mocker, password, is_admin, expected_status):
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=app_user)
        if is_admin:
            mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims',
                         return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(MANAGER_USER_URL + '/1', headers=headers, json={"password": password})
        assert_status(rv, expected_status)

    @pytest.mark.parametrize('request_data, expected_status', [
        ({"profile": {
            "email": "<EMAIL>", "first_name": "Dau Quoc ", "last_name": "Toan", "country": "Germany",
            "is_admin": True, "is_sales": True, "status": "Active", "company_id": "1"
        }, "subscription": {
            "type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200, "api_max_result": 1400,
            "api_max_monitor_profile": 20}, "feature_ids": [1, 2, 3], "sale_ids": [1]}, HTTPStatus.CREATED),
        ({"profile": {
            "first_name": "Dau Quoc ", "last_name": "Toan", "country": "Germany",
            "is_admin": True, "is_sales": True, "status": "Active", "company_id": "1"
        }, "subscription": {
            "type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200, "api_max_result": 1400,
            "api_max_monitor_profile": 20}, "feature_ids": [1, 2, 3]}, HTTPStatus.BAD_REQUEST),
        ({"subscription": {
            "type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200, "api_max_result": 1400,
            "api_max_monitor_profile": 20}, "feature_ids": [1, 2, 3]}, HTTPStatus.BAD_REQUEST),
        ({}, HTTPStatus.BAD_REQUEST)
    ])
    def test_create_user(self, client, mocker, request_data, expected_status):
        mocker.patch('app.models.user.AppUserModel.get', return_value=None)
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_admin_jwt_claims())
        sales = [UserModel(id=n) for n in request_data['sale_ids']] if 'sale_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_sales_by_ids', return_value=sales)
        features = [FeatureModel(id=n) for n in request_data['feature_ids']] if 'feature_ids' in request_data else []
        mocker.patch('app.models.feature.FeatureModel.get_by_ids', return_value=features)
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=UserModel(id=1))
        mocker.patch('app.models.company.CompanyModel.get_one',
                     return_value=CompanyModel(id=123, name='Company 123'))
        mocker.patch('app.models.user.AppUserModel.get_by_company_id', return_value=[])
        mocker.patch('app.models.group_helper.GroupHelper.add_user_to_everyone_group')

        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.post(MANAGER_USER_URL, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    @pytest.mark.parametrize('request_data, expected_status', [
        ({"profile": {
            "email": "<EMAIL>", "first_name": "Dau Quoc ", "last_name": "Toan", "country": "Germany",
            "is_admin": True, "is_sales": True, "status": "Active", "company_id": "1"
        }, "subscription": {
            "type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200, "api_max_result": 1400,
            "api_max_monitor_profile": 20}, "feature_ids": [1, 2, 3], "sale_ids": [1]}, HTTPStatus.BAD_REQUEST),
        ({"profile": {
            "first_name": "Dau Quoc ", "last_name": "Toan", "country": "Germany",
            "status": "Active", "company_id": "1"
        }, "subscription": {
            "type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200, "api_max_result": 1400,
            "api_max_monitor_profile": 20}, "feature_ids": [1, 2, 3]}, HTTPStatus.BAD_REQUEST),
        ({"profile": {
            "email": "<EMAIL>", "first_name": "Dau Quoc ", "last_name": "Toan", "country": "Germany",
            "status": "Active"
        }, "subscription": {
            "type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200, "api_max_result": 1400,
            "api_max_monitor_profile": 20}, "feature_ids": [1, 2, 3]}, HTTPStatus.CREATED),
        ({"subscription": {
            "type": "PROFESSIONAL", "api_package": 1500, "api_access_throttle": 1200, "api_max_result": 1400,
            "api_max_monitor_profile": 20}, "feature_ids": [1, 2, 3]}, HTTPStatus.BAD_REQUEST),
        ({}, HTTPStatus.BAD_REQUEST)
    ])
    def test_create_user_by_sale(self, client, mocker, request_data, expected_status):
        mocker.patch('app.models.user.AppUserModel.get', return_value=None)
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_sale_jwt_claims())
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_sale_jwt_claims())
        sales = [UserModel(id=n) for n in request_data['sale_ids']] if 'sale_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_sales_by_ids', return_value=sales)
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=UserModel(id=1))
        features = [FeatureModel(id=n) for n in request_data['feature_ids']] if 'feature_ids' in request_data else []
        mocker.patch('app.models.feature.FeatureModel.get_by_ids', return_value=features)
        mocker.patch('app.models.user.AppUserModel.get_by_company_id', return_value=[])
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.post(MANAGER_USER_URL, headers=headers, json=request_data)
        assert_status(rv, expected_status)


class TestUserFeatures:

    @staticmethod
    def _patch(mocker, params=None):
        mocker.patch('app.models.feature.FeatureModel.get_all',
                     return_value=[build_feature(fid) for fid in range(1, 4)])
        if params:
            if params.get('is_admin'):
                mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims',
                             return_value=get_admin_jwt_claims())
            if 'user' in params:
                mocker.patch('app.models.user.UserModel.get_by_user_id',
                             return_value=params.get('user'))
            if params.get('features_by_id'):
                features = [build_feature(fid) for fid in params.get('features_by_id')]
                mocker.patch("app.models.feature.FeatureModel.get_by_ids",
                             return_value=features)

    @pytest.mark.parametrize('is_admin, expected_status', [
        (False, HTTPStatus.FORBIDDEN),
        (True, HTTPStatus.OK)
    ])
    def test_get_features(self, client, mocker, is_admin, expected_status):
        self._patch(mocker, {'is_admin': is_admin})
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get('/auth/features', headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            assert len(rv.json['data']['features']) == 3
            assert rv.json['data']['features'][0]['short_name'] == "F1"

    @pytest.mark.parametrize('user, is_admin, expected_status', [
        (None, False, HTTPStatus.FORBIDDEN),
        (None, True, HTTPStatus.NOT_FOUND),
        (build_app_user(features=[build_feature(1)]), True, HTTPStatus.OK)
    ])
    def test_get_user_features(self, client, mocker, user, is_admin, expected_status):
        self._patch(mocker, {'is_admin': is_admin, 'user': user})
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get('/auth/user/1/features', headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            assert len(rv.json['data']) == 1
            assert rv.json['data'][0]['short_name'] == "F1"

    @pytest.mark.parametrize('user, is_admin, to_enable, expected_status', [
        (None, False, {}, HTTPStatus.FORBIDDEN),
        (build_app_user(features=[build_feature(1)]), True, {'none_sense': []}, HTTPStatus.BAD_REQUEST),
        (None, True, {'feature_ids': [1, 2]}, HTTPStatus.NOT_FOUND),
        (build_app_user(features=[build_feature(1)]), True, {'feature_ids': [1, 2]}, HTTPStatus.OK)
    ])
    def test_enable_user_features(self, client, mocker, user, is_admin,
                                  to_enable, expected_status):
        self._patch(mocker, {'is_admin': is_admin, 'user': user, 'features_by_id': to_enable.get('feature_ids')})
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch('/auth/user/1/features', headers=headers, json=to_enable)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            for fid in to_enable.get('feature_ids'):
                assert build_feature(fid) in user.features


class TestManagerInviteUser:

    @pytest.mark.parametrize('claims, expected_status', [
        (get_admin_jwt_claims(), HTTPStatus.CREATED),
        (get_sale_jwt_claims(), HTTPStatus.FORBIDDEN),
        (get_team_manager_jwt_claims(), HTTPStatus.CREATED),
        (get_customized_jwt_claims(), HTTPStatus.FORBIDDEN)
    ])
    def test_invite_user_by_roles(self, client, mocker, claims, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.routes.auth.manager.manager.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.AppUserModel.get',
                     return_value=build_app_user(id=123, company_id=claims.get('company_id')))
        mocker.patch('app.models.user.AppUserModel.get_one',
                     return_value=build_app_user(company_id=claims.get('company_id')))
        mocker.patch('app.models.company.CompanyModel.get_one', return_value=CompanyModel(free_seats=1))
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.post(MANAGER_INVITE_USER_URL, headers=headers, json={'email': '<EMAIL>'})
        assert_status(rv, expected_status)

    @pytest.mark.parametrize('email, user, expected_status', [
        (None, None, HTTPStatus.BAD_REQUEST),
        (None, build_app_user(), HTTPStatus.BAD_REQUEST),
        ('<EMAIL>', None, HTTPStatus.CREATED),
        ('<EMAIL>', build_app_user(id=user_id, company_id=None), HTTPStatus.BAD_REQUEST),
        ('<EMAIL>', build_app_user(id=123, company_id=1), HTTPStatus.CREATED),
        ('<EMAIL>', build_app_user(id=123, company_id=12), HTTPStatus.OK),
        ('<EMAIL>', build_app_user(id=123, company_id=None), HTTPStatus.CREATED),
    ])
    def test_invite_user(self, client, mocker, email, user, expected_status):
        claims = get_team_manager_jwt_claims()
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.routes.auth.manager.manager.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.AppUserModel.get_one',
                     return_value=build_app_user(company_id=claims.get('company_id')))
        mocker.patch('app.models.user.AppUserModel.get', return_value=user)
        mocker.patch('app.models.company.CompanyModel.get_one',
                     return_value=CompanyModel(id=claims.get('company_id')))
        mocker.patch('app.models.current_user_helper.CurrentUserHelper.is_manager_only',
                     return_value=True)
        mocker.patch.object(AppUserModel, "send_invitation_email")
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.post(MANAGER_INVITE_USER_URL, headers=headers, json={'email': email})
        assert_status(rv, expected_status)

    @pytest.mark.parametrize('user, company, expected_status', [
        (None, None, HTTPStatus.NOT_FOUND),
        (None, CompanyModel(id=1), HTTPStatus.NOT_FOUND),
        (build_app_user(id=user_id), None, HTTPStatus.NOT_FOUND),
        (build_app_user(id=user_id, company_id=1), CompanyModel(id=1), HTTPStatus.OK),
        (build_app_user(id=123, company_id=1), CompanyModel(id=2), HTTPStatus.FORBIDDEN),
    ])
    def test_accept_invitation(self, client, mocker, user, company, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims',
                     return_value=get_customized_jwt_claims())
        mocker.patch('app.models.user.AppUserModel.get_one', return_value=build_app_user(company_id=123))
        mocker.patch('app.models.user.AppUserModel.get', return_value=user)
        mocker.patch('app.models.company.CompanyModel.get_one', return_value=company)
        mocker.patch('app.models.group_helper.GroupHelper.add_user_to_everyone_group')
        mocker.patch.object(AppUserModel, "save")
        mocker.patch.object(ConfirmationToken, "validate", return_value={'email': "<EMAIL>"})
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(MANAGER_INVITE_USER_URL, headers=headers, json={'token': 'abc', 'company_token': 'abc'})
        assert_status(rv, expected_status)


class TestManagerInviteUserId:

    @pytest.mark.parametrize('claims, expected_status', [
        (get_admin_jwt_claims(), HTTPStatus.NO_CONTENT),
        (get_sale_jwt_claims(), HTTPStatus.FORBIDDEN),
        (get_team_manager_jwt_claims(), HTTPStatus.NO_CONTENT),
        (get_customized_jwt_claims(), HTTPStatus.FORBIDDEN)
    ])
    def test_remove_user_by_roles(self, client, mocker, claims, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.routes.auth.manager.manager.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user.UserModel.get_by_user_id',
                     return_value=build_app_user(company_id=1))
        mocker.patch('app.models.company.CompanyModel.get_one',
                     return_value=build_app_user(id=123, company_id=claims.get('company_id')))
        mocker.patch('app.models.group_helper.GroupHelper.remove_user_from_everyone_group')
        mocker.patch('app.models.user.AppUserModel.update_features_by_ids')
        mocker.patch.object(AppUserModel, "save")
        mocker.patch.object(AppUserModel, "get_access_tokens")
        mocker.patch.object(AppUserModel, "send_remove_from_company_mail")
        mocker.patch.object(UserJournalModel, "create_journal")
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.delete(MANAGER_INVITE_USER_URL + '/1', headers=headers)
        assert_status(rv, expected_status)

    @pytest.mark.parametrize('user, company, expected_status', [
        (build_app_user(company_id=None), None, HTTPStatus.BAD_REQUEST),
        (build_app_user(company_id=1), None, HTTPStatus.NOT_FOUND),
        (build_app_user(company_id=1), CompanyModel(id=1), HTTPStatus.NO_CONTENT)
    ])
    def test_remove_user(self, client, mocker, user, company, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims',
                     return_value=get_team_manager_jwt_claims())
        mocker.patch('app.models.user.UserModel.get_by_user_id', return_value=user)
        mocker.patch('app.models.company.CompanyModel.get_one', return_value=company)
        mocker.patch('app.models.group_helper.GroupHelper.remove_user_from_everyone_group')
        mocker.patch('app.models.user.AppUserModel.update_features_by_ids')
        mocker.patch.object(UserJournalModel, "create_journal")
        mocker.patch.object(AppUserModel, "save")
        mocker.patch.object(AppUserModel, "get_access_tokens")
        mocker.patch.object(AppUserModel, "send_remove_from_company_mail")
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.delete(MANAGER_INVITE_USER_URL + '/1', headers=headers)
        assert_status(rv, expected_status)
