import pytest

from app.models.user import AppUserModel
from app.scheduled_tasks.trials_report import send_trials_report_email
from tests.unit_tests.auth_api.mocks.sqlalchemy import QueryMock


class TestTrialsReport:

    params = [
        (
            [
                (AppUserModel(email="<EMAIL>",
                              sales=[AppUserModel(id=1, email="<EMAIL>", first_name="Test BD 1")]),
                 None, 0, 0),
                (AppUserModel(email="<EMAIL>",
                              sales=[AppUserModel(id=1, email="<EMAIL>", first_name="Test BD 1")]),
                 None, 0, 0),
                (AppUserModel(email="<EMAIL>",
                              sales=[AppUserModel(id=1, email="<EMAIL>", first_name="Test BD 1")]),
                 None, 0, 0),
                (AppUserModel(email="<EMAIL>",
                              sales=[AppUserModel(id=2, email="<EMAIL>", first_name="Test BD 2")]),
                 None, 0, 0),
                (AppUserModel(email="<EMAIL>",
                              sales=[AppUserModel(id=2, email="<EMAIL>", first_name="Test BD 2")]),
                 None, 0, 0),
                (AppUserModel(email="<EMAIL>",
                              sales=[AppUserModel(id=3, email="<EMAIL>", first_name="Test BD 3")]),
                 None, 0, 0)
            ],
            3
        )
    ]

    @pytest.mark.parametrize('result, sent_emails', params)
    def test_trials_report(self, mocker, result, sent_emails):
        mocker.patch('app.scheduled_tasks.trials_report._build_trial_report_query',
                     return_value=QueryMock(result))
        send_email = mocker.patch('app.scheduled_tasks.trials_report._send_email')
        send_trials_report_email()
        assert send_email.call_count == sent_emails
