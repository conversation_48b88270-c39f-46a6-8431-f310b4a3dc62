import datetime
from http import HTTPStatus
import pytest
from octimine_common.enums import SearchType
from octimine_common.tests.data import access_token
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.mocks.date import MockDate
from tests.unit_tests.auth_api.mocks.jwt import get_admin_jwt_claims


class TestStatistics:
    def test_search_growth(self, client, mocker):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.routes.auth.manager.statistics.date', MockDate)
        MockDate.today = classmethod(lambda cls: datetime.date(2020, 4, 3))
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.models.stats_log.common_queries.CommonStatsQueries.count_by_day_and_field',
                     return_value=[('2020-04-01', SearchType.SEMANTIC, 4), ('2020-04-02', SearchType.SEMANTIC, 2),
                                   ('2020-04-02', SearchType.BOOLEAN, 1)])
        rv = client.get('/auth/statistics/search_growth?last_days=3', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.get_json()['data'] == {'BOOLEAN': {'2020-04-01': 0, '2020-04-02': 2, '2020-04-03': 0},
                                         'SEMANTIC_TEXT': {'2020-04-01': 4, '2020-04-02': 2, '2020-04-03': 0},
                                         'CITATION': {'2020-04-01': 0, '2020-04-02': 0, '2020-04-03': 0},
                                         'SEMANTIC_NUMBERS': {'2020-04-01': 4, '2020-04-02': 2, '2020-04-03': 0},
                                         'TOTAL': {'2020-04-01': 8, '2020-04-02': 6, '2020-04-03': 0}}

    def test_users_most_trials(self, client, mocker):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.routes.auth.manager.statistics.date', MockDate)
        MockDate.today = classmethod(lambda cls: datetime.date(2020, 4, 3))
        mocker.patch('app.models.stats_log.search.fresh_search_log.UserFreshSearchLog'
                     '.users_with_most_trials', return_value=[])
        rv = client.get('/auth/statistics/most_trials', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert 'users' in rv.get_json()['data']

    def test_user_searches(self, client, mocker):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.models.stats_log.search.fresh_search_log.UserFreshSearchLog.user_searches',
                     return_value=[('2020-11-04', 2), ('2020-11-03', 8),
                                   ('2020-11-02', 4), ('2020-11-01', 5)])
        rv = client.get('/auth/statistics/user_searches/1', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.get_json()['data']['searches'] == \
               [['2020-11-04', 2], ['2020-11-03', 8], ['2020-11-02', 4], ['2020-11-01', 5]]

    def test_recurring_search_count(self, client, mocker):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.models.stats_log.search.recurring_search_log.UserRecurringSearchLog.'
                     'count_by_search_type', return_value=[(SearchType.SEMANTIC,  10), (SearchType.BOOLEAN, 5)])
        rv = client.get('/auth/statistics/recurring_search_count', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.get_json()['data'] == {
            'SEMANTIC': 10,
            'BOOLEAN': 5
        }

    def test_export_count(self, client, mocker):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.models.stats_log.search.search_export_log.UserSearchExportLog.'
                     'count_by_export_format',
                     return_value=[('pdf', 10), ('xlsx', 5), ('csv', 50)])
        rv = client.get('/auth/statistics/export_count', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.get_json()['data'] == {
            'pdf': 10,
            'xlsx': 5,
            'csv': 50
        }

    def test_custom_activity_count(self, client, mocker):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.models.stats_log.custom.activity_log.UserCustomActivityLog.count_by_type',
                     return_value=[('CUSTOM_ACTIVITY_1', 100), ('CUSTOM_ACTIVITY_2', 50)])
        rv = client.get('/auth/statistics/custom_activity_count', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.get_json()['data'] == {
            'CUSTOM_ACTIVITY_1': 100,
            'CUSTOM_ACTIVITY_2': 50
        }

    @pytest.mark.parametrize('from_date, to_date, expected_status', [
        ('bad_date', '2020-01-05', HTTPStatus.BAD_REQUEST),
        ('2020-01-01', 'bad_date', HTTPStatus.BAD_REQUEST),
        ('2020-01-01', '2020-01-05', HTTPStatus.OK)
    ])
    def test_user_stats_history(self, client, mocker, from_date, to_date, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.models.stats_log.custom.activity_log.UserCustomActivityLog.'
                     'count_by_day_and_type_for_user', return_value=[('2020-01-01', 'CUSTOM', 5)])
        mocker.patch('app.models.stats_log.search.fresh_search_log.UserFreshSearchLog.'
                     'count_by_day_and_search_type_for_user',
                     return_value=[('2020-01-01', 'SEMANTIC_TEXT', 1), ('2020-01-02', 'BOOLEAN', 1)])
        mocker.patch('app.models.stats_log.search.recurring_search_log.UserRecurringSearchLog.'
                     'count_by_day_and_search_type_for_user',
                     return_value=[('2020-01-02', 'SEMANTIC', 2), ('2020-01-03', 'BOOLEAN', 4)])
        mocker.patch('app.models.stats_log.search.search_export_log.UserSearchExportLog.'
                     'count_by_day_and_export_format_for_user',
                     return_value=[('2020-01-04', 'pdf', 5), ('2020-01-04', 'csv', 7)])
        mocker.patch('app.models.stats_log.download.download_log.UserDownloadLog.'
                     'count_by_day_and_format_for_user',
                     return_value=[('2020-01-04', 'pdf', 6)])
        rv = client.get(f'/auth/statistics/user_history/1?from_date={from_date}&to_date={to_date}', headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            assert rv.get_json()['data'] == [
                {'2020-01-01': {'others': {'CUSTOM': 5}, 'searches': {'SEMANTIC_TEXT': 2}}},
                {'2020-01-02': {'searches': {'BOOLEAN': 2}, 'recurring_searches': {'SEMANTIC': 2}}},
                {'2020-01-03': {'recurring_searches': {'BOOLEAN': 4}}},
                {'2020-01-04': {'exports': {'pdf': 5, 'csv': 7}, 'downloads': {'pdf': 6}}},
                {'2020-01-05': {}}
            ]
