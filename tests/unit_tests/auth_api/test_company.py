# Standard lib imports
from http import HTTPStatus

import pytest

from app.models.company import CompanyModel
from app.models.user import UserModel, GroupModel
from app.models.group_helper import GroupHelper
# Local imports
from octimine_common.tests.data import access_token
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.mocks.jwt import get_admin_jwt_claims
from tests.unit_tests.auth_api.url import COMPANY_URL


class TestCompany:
    def test_get_companies_successful(self, client, mocker):
        mocker.patch('app.models.company.CompanyModel.get_all', return_value=[])
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)

        rv = client.get(COMPANY_URL, headers=headers)
        response_data = rv.get_json()
        result = response_data['data']
        assert_status(rv, HTTPStatus.OK)
        assert 'companies' in result
        assert 'page' in result

    patch_parameters = [
        ({"name": "", "active": True, "valid_to": "2020-12-20"}, HTTPStatus.BAD_REQUEST),
        ({"name": "Test Company", "active": True, "valid_to": "2020-11-07T00:00:00"}, HTTPStatus.CREATED),
        ({"name": "Test Company", "active": False, "valid_to": "2020-11-07T00:00:00"}, HTTPStatus.CREATED),
        ({"name": "Test Company", "default_feature_ids": None}, HTTPStatus.CREATED),
        ({"name": "Test Company", "default_feature_ids": []}, HTTPStatus.CREATED),
        ({"name": "Test Company", "default_feature_ids": [None]}, HTTPStatus.BAD_REQUEST),
        ({"name": "Test Company", "default_feature_ids": [1]}, HTTPStatus.CREATED),
    ]

    @pytest.mark.parametrize('request_data, expected_status', patch_parameters)
    def test_create_company(self, client, mocker, request_data, expected_status):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.company.FeatureModel.get_by_ids', return_value=[CompanyModel(id=1)])
        mocker.patch('app.models.company.CompanyModel._is_name_taken', return_value=False)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.post(COMPANY_URL, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    patch_parameters = [
        (-1, HTTPStatus.NOT_FOUND),
        (1, HTTPStatus.OK)
    ]

    @pytest.mark.parametrize('company_id, expected_status', patch_parameters)
    def test_get_company(self, client, mocker, company_id, expected_status):
        company = CompanyModel(id=company_id) if company_id > 0 else None
        mocker.patch('app.models.company.CompanyModel.get_one', return_value=company)
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(f'{COMPANY_URL}/{company_id}', headers=headers)
        assert_status(rv, expected_status)

    patch_parameters = [
        (-1, {"name": "", "active": True, "valid_to": "2020-12-20"}, HTTPStatus.NOT_FOUND),
        (1, {"name": "", "active": True, "valid_to": "2020-12-20"}, HTTPStatus.BAD_REQUEST),
        (1, {"name": "Test Company", "active": True, "valid_to": "2020-11-07T00:00:00"}, HTTPStatus.OK),
        (1, {"name": "Test Company", "active": False, "valid_to": "2020-11-07T00:00:00"}, HTTPStatus.OK),
        (1, {"name": "Test Company", "default_feature_ids": None}, HTTPStatus.OK),
        (1, {"name": "Test Company", "default_feature_ids": []}, HTTPStatus.OK),
        (1, {"name": "Test Company", "default_feature_ids": [None]}, HTTPStatus.BAD_REQUEST),
        (1, {"name": "Test Company", "default_feature_ids": [1]}, HTTPStatus.OK),
    ]

    @pytest.mark.parametrize('company_id, request_data, expected_status', patch_parameters)
    def test_update_company(self, client, mocker, company_id, request_data, expected_status):
        company = CompanyModel(id=company_id) if company_id > 0 else None
        mocker.patch('app.models.company.CompanyModel.get_one', return_value=company)
        mocker.patch('app.models.company.CompanyModel._is_name_taken', return_value=False)
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.company.FeatureModel.get_by_ids', return_value=[CompanyModel(id=1)])
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(f'{COMPANY_URL}/{company_id}', headers=headers, json=request_data)
        assert_status(rv, expected_status)

    delete_parameters = [
        (-1, {"name": "", "active": True, "users": []}, HTTPStatus.NOT_FOUND),
        (1, {"name": "", "active": True, "users": [UserModel(id=1)]}, HTTPStatus.BAD_REQUEST),
        (1, {"name": "Test Company", "active": True, "users": []}, HTTPStatus.NO_CONTENT)
    ]

    @pytest.mark.parametrize('company_id, request_data, expected_status', delete_parameters)
    def test_delete_user(self, client, mocker, company_id, request_data, expected_status):
        company = CompanyModel(id=company_id, users=request_data['users']) if company_id > 0 else None
        mocker.patch('app.models.company.CompanyModel.get_one', return_value=company)
        mocker.patch('app.models.company.CompanyModel.delete')
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.delete(f'{COMPANY_URL}/{company_id}', headers=headers)
        assert_status(rv, expected_status)

    def test_get_company_options_successful(self, client, mocker):
        mocker.patch('app.models.company.CompanyModel.get_all', return_value=[])
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(f'{COMPANY_URL}?load_all=1', headers=headers)
        response_data = rv.get_json()
        result = response_data['data']
        assert_status(rv, HTTPStatus.OK)
        assert 'page' not in result

    @pytest.fixture
    def mock_create_everyone_group(self, mocker):
        return mocker.patch('app.models.group_helper.GroupHelper.create_everyone_group')

    @pytest.fixture
    def mock_get_everyone_group(self, mocker):
        return mocker.patch('app.models.group_helper.GroupHelper.get_everyone_group')

    @pytest.fixture
    def mock_db_session(self, mocker):
        return mocker.patch('app.extensions.db.session')

    def test_save_creates_everyone_group_for_new_company(self, mock_create_everyone_group, mocker):
        # Arrange
        company = CompanyModel(id=1, name="Test Company")
        mocker.patch.object(company, '_before_save', return_value=False)
        mocker.patch.object(company, 'is_persisted', return_value=False)

        # Act
        company.save()

        # Assert
        mock_create_everyone_group.assert_called_once_with(1)

    def test_save_does_not_create_everyone_group_for_existing_company(self, mock_create_everyone_group, mocker):
        # Arrange
        company = CompanyModel(id=1, name="Test Company")
        mocker.patch.object(company, '_before_save', return_value=False)
        mocker.patch.object(company, 'is_persisted', return_value=True)

        # Act
        company.save()

        # Assert
        mock_create_everyone_group.assert_not_called()

    def test_delete_removes_everyone_group(self, mock_get_everyone_group, mock_db_session):
        # Arrange
        company = CompanyModel(id=1, name="Test Company")
        everyone_group = GroupModel(id=1, name=GroupHelper.GROUP_NAME)
        mock_get_everyone_group.return_value = everyone_group

        # Act
        company.delete()

        # Assert
        mock_get_everyone_group.assert_called_once_with(company.id)
        mock_db_session.delete.assert_any_call(everyone_group)
        mock_db_session.delete.assert_any_call(company)
        mock_db_session.commit.assert_called_once()

    def test_delete_handles_no_everyone_group(self, mock_get_everyone_group, mock_db_session):
        # Arrange
        company = CompanyModel(id=1, name="Test Company")
        mock_get_everyone_group.return_value = None

        # Act
        company.delete()

        # Assert
        mock_get_everyone_group.assert_called_once_with(company.id)
        mock_db_session.delete.assert_called_once_with(company)
        mock_db_session.commit.assert_called_once()

    def test_delete_rolls_back_on_error(self, mock_get_everyone_group, mock_db_session):
        # Arrange
        company = CompanyModel(id=1, name="Test Company")
        everyone_group = GroupModel(id=1, name=GroupHelper.GROUP_NAME)
        mock_get_everyone_group.return_value = everyone_group
        mock_db_session.commit.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(Exception):
            company.delete()
        mock_db_session.rollback.assert_called_once()
