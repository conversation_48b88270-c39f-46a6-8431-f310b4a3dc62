from copy import deepcopy
from datetime import datetime, timedelta
from http import HTTPStatus
import pytest
from app.models.feature import FeatureModel
from octimine_common.enums import SubscriptionType
from octimine_common.tests.data import access_token, get_app_request
from app.models.user import UserModel, AppUserModel, SubscriptionModel
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.data import app_user, api_user, delegated_user
from tests.unit_tests.auth_api.url import USER_URL, TEAM_URL


class TestUser:
    @pytest.mark.parametrize('user', [api_user, app_user])
    def test_get_user_successful(self, client, mocker, user):
        mocker.patch('app.models.user.UserModel.get_by_id', return_value=user)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(USER_URL, headers=headers)
        response_data = rv.get_json()
        result = response_data['data']
        assert_status(rv, HTTPStatus.OK)
        assert result['user']
        assert result['subscription']
        # two-factor is only for app user
        if 'email' in result['user']:
            assert result['user']['two_factor_authentication_enabled'] is False

    @pytest.mark.parametrize('sub, should_have_features', [
        (SubscriptionModel(type=SubscriptionType.FREE), True),
        (SubscriptionModel(type=SubscriptionType.PROFESSIONAL), True),
        (SubscriptionModel(type=SubscriptionType.ENTERPRISE, valid_until=datetime(2121, 12, 31)), True),
        (SubscriptionModel(type=SubscriptionType.ENTERPRISE, valid_until=datetime(2010, 12, 31)), False),
    ])
    def test_user_subscription_expiry_cancels_features(self, client, mocker, sub, should_have_features):
        user_copy = deepcopy(app_user)
        user_copy.subscription = sub
        user_copy.features = [FeatureModel(id=1, short_name='A'), FeatureModel(id=2, short_name='B')]
        headers = dict(Authorization="Bearer %s" % access_token)
        mocker.patch('app.models.user.UserModel.get_by_id', return_value=user_copy)

        rv = client.get(USER_URL, headers=headers)
        assert bool(rv.get_json()['data']['user']['features']) == should_have_features

    patch_parameters = [
        (app_user, {"phone1": "+4915158242420"}, HTTPStatus.OK),
        (api_user, {"phone1": "+4915158242420"}, HTTPStatus.FORBIDDEN),
        (app_user, {"phone3": "+4915158242420"}, HTTPStatus.BAD_REQUEST),
        (app_user, {"save_history": 153}, HTTPStatus.BAD_REQUEST),
        (app_user, {"email": "<EMAIL>"}, HTTPStatus.BAD_REQUEST),
        (None, {"phone1": "+4915158242420"}, HTTPStatus.FORBIDDEN),
        (app_user, None, HTTPStatus.BAD_REQUEST),
    ]

    @pytest.mark.parametrize('user, request_data, expected_status', patch_parameters)
    def test_patch_user(self, client, mocker, user, request_data, expected_status):
        mocker.patch('app.models.user.UserModel.get_by_id', return_value=user)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(USER_URL, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    delete_parameters = [
        (None, None, HTTPStatus.UNAUTHORIZED),
        (None, access_token, HTTPStatus.NOT_FOUND),
        (delegated_user, access_token, HTTPStatus.FORBIDDEN),
        (app_user, access_token, HTTPStatus.NO_CONTENT),
    ]

    @pytest.mark.parametrize('user, token, expected_status', delete_parameters)
    def test_delete_user(self, client, mocker, user, token, expected_status):
        mocker.patch('app.models.user.UserModel.get_by_id', return_value=user)
        mocker.patch('app.models.user.UserModel.delete')
        headers = dict(Authorization="Bearer %s" % token)
        rv = client.delete(USER_URL, headers=headers)
        assert_status(rv, expected_status)

    patch_parameters = [
        [],
        [UserModel(id=2), UserModel(id=3)]
    ]

    @pytest.mark.parametrize('users', patch_parameters)
    def test_get_team_users(self, client, mocker, users):
        app_req = get_app_request(is_admin=True)
        mocker.patch('app.routes.auth.user.team.AppRequest.from_request', return_value=app_req)
        mocker.patch('app.models.user.UserModel.get_by_id', return_value=UserModel(id=1))
        mocker.patch('app.models.user.AppUserModel.get_by_company_id', return_value=users)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(TEAM_URL, headers=headers)
        data = rv.get_json()['data']
        assert_status(rv, HTTPStatus.OK)
        assert set([u['id'] for u in data['users']]) == set([u.id for u in users])

    def test_get_team_inexistent_user(self, client, mocker):
        app_req = get_app_request(is_sales=True)
        mocker.patch('app.routes.auth.user.team.AppRequest.from_request', return_value=app_req)
        mocker.patch('app.models.user.AppUserModel.get_one', return_value=None)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(TEAM_URL + '/' + str(999999), headers=headers)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    get_parameters = [
        (UserModel(id=2, company_id=2), HTTPStatus.NOT_FOUND),
        (UserModel(id=3, company_id=1), HTTPStatus.OK)
    ]

    @pytest.mark.parametrize('user, expected_status', get_parameters)
    def test_get_team_user(self, client, mocker, user, expected_status):
        app_req = get_app_request(is_sales=True)
        mocker.patch('app.routes.auth.user.team.AppRequest.from_request', return_value=app_req)
        mocker.patch('app.models.user.AppUserModel.get_one', return_value=user)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(TEAM_URL + '/' + str(user.id), headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            data = rv.get_json()['data']
            assert data['id'] == user.id


class TestUserModel:

    @pytest.mark.parametrize('country, local_date, hour_difference', [
        ('Germany', datetime(2020, 1, 1, hour=5), 1),  # Winter
        ('Germany', datetime(2020, 7, 1, hour=5), 2)  # Summer
    ])
    def test_should_convert_local_date_to_utc(self, country, local_date, hour_difference):
        utc_date = AppUserModel(country=country).local_to_utc_time(local_date)
        assert utc_date + timedelta(hours=hour_difference) == local_date


class TestUserModelPatchValues:
    def test_patch_values_company_id_change_removes_from_old_group_and_adds_to_new(self, mocker):
        # Arrange
        user = AppUserModel(id=1, company_id=1, email="<EMAIL>", password="P@ssw0rd!")
        remove_mock = mocker.patch('app.models.group_helper.GroupHelper.remove_user_from_everyone_group')
        add_mock = mocker.patch('app.models.group_helper.GroupHelper.add_user_to_everyone_group')
        updating_data = {'company_id': 2}

        # Act
        user.patch_values(updating_data)

        # Assert
        assert user.company_id == 2
        remove_mock.assert_called_once_with(user)
        add_mock.assert_called_once_with(user)

    def test_patch_values_company_id_change_from_null_to_value_adds_to_group(self, mocker):
        # Arrange
        user = AppUserModel(id=1, company_id=None, email="<EMAIL>", password="P@ssw0rd!")
        remove_mock = mocker.patch('app.models.group_helper.GroupHelper.remove_user_from_everyone_group')
        add_mock = mocker.patch('app.models.group_helper.GroupHelper.add_user_to_everyone_group')
        updating_data = {'company_id': 1}

        # Act
        user.patch_values(updating_data)

        # Assert
        assert user.company_id == 1
        remove_mock.assert_not_called()
        add_mock.assert_called_once_with(user)

    def test_patch_values_company_id_change_from_value_to_null_removes_from_group(self, mocker):
        # Arrange
        user = AppUserModel(id=1, company_id=1, email="<EMAIL>", password="P@ssw0rd!")
        remove_mock = mocker.patch('app.models.group_helper.GroupHelper.remove_user_from_everyone_group')
        add_mock = mocker.patch('app.models.group_helper.GroupHelper.add_user_to_everyone_group')
        updating_data = {'company_id': None}

        # Act
        user.patch_values(updating_data)

        # Assert
        assert user.company_id is None
        remove_mock.assert_called_once_with(user)
        add_mock.assert_not_called()

    def test_patch_values_company_id_no_change_does_not_modify_groups(self, mocker):
        # Arrange
        user = AppUserModel(id=1, company_id=1, email="<EMAIL>", password="P@ssw0rd!")
        remove_mock = mocker.patch('app.models.group_helper.GroupHelper.remove_user_from_everyone_group')
        add_mock = mocker.patch('app.models.group_helper.GroupHelper.add_user_to_everyone_group')
        updating_data = {'company_id': 1}

        # Act
        user.patch_values(updating_data)

        # Assert
        assert user.company_id == 1
        remove_mock.assert_not_called()
        add_mock.assert_not_called()
