# Standard lib imports
import json
from http import HTTPStatus

# Local imports
from app.models.user import User<PERSON>tat<PERSON>, ApiUserModel, AppUserModel
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.data import test_api_key, test_public_key, test_second_signature, \
    test_signature_timestamp
from tests.unit_tests.auth_api.url import DELEGATED_LOGIN_URL as URL


class TestDelegatedLogin:
    def test_delegated_login_new(self, client, mocker):
        mocker.patch('app.models.user.ApiUserModel.get',
                     return_value=ApiUserModel(api_key=test_api_key, public_key=test_public_key,
                                               status=UserStatus.Active, is_admin=1))
        mocker.patch('app.models.user.AppUserModel.get',
                     return_value=None)
        rv = client.post(URL, data=json.dumps(
            dict(api_key=test_api_key, signature=test_second_signature, timestamp=test_signature_timestamp,
                 user_id=2, user_email='<EMAIL>', customer_id=1, customer_name='Test Enterprise')))
        json_response = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        assert 'access_token' in json_response['data']
        assert 'refresh_token' in json_response['data']

    def test_delegated_login_existing(self, client, mocker):
        mocker.patch('app.models.user.ApiUserModel.get',
                     return_value=ApiUserModel(api_key=test_api_key, public_key=test_public_key,
                                               status=UserStatus.Active, is_admin=1))
        mocker.patch('app.models.user.AppUserModel.get',
                     return_value=AppUserModel(delegation_source='DIAMS', email="<EMAIL>",
                                               status=UserStatus.Delegated))
        rv = client.post(URL, data=json.dumps(
            dict(api_key=test_api_key, signature=test_second_signature, timestamp=test_signature_timestamp,
                 user_id=2, user_email='<EMAIL>', user_name_first='Max', user_name_last='Mustermann')))
        json_response = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        assert 'access_token' in json_response['data']
        assert 'refresh_token' in json_response['data']

    def test_delegated_login_inactive(self, client, mocker):
        mocker.patch('app.models.user.ApiUserModel.get',
                     return_value=ApiUserModel(api_key=test_api_key, public_key=test_public_key,
                                               status=UserStatus.Active, is_admin=1))
        mocker.patch('app.models.user.AppUserModel.get',
                     return_value=AppUserModel(delegation_source='DIAMS',
                                               email="<EMAIL>", status=UserStatus.Blocked))
        rv = client.post(URL, data=json.dumps(
            dict(api_key=test_api_key, signature=test_second_signature, timestamp=test_signature_timestamp,
                 user_id=2, user_email='<EMAIL>', user_name_first='Max', user_name_last='Mustermann')))
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    def test_delegated_login_not_an_admin(self, client, mocker):
        mocker.patch('app.models.user.ApiUserModel.get',
                     return_value=ApiUserModel(api_key=test_api_key, public_key=test_public_key,
                                               status=UserStatus.Active, is_admin=0))
        mocker.patch('app.models.user.AppUserModel.get',
                     return_value=AppUserModel(delegation_source='DIAMS',
                                               email="<EMAIL>", status=UserStatus.Active))
        rv = client.post(URL, data=json.dumps(
            dict(api_key=test_api_key, signature=test_second_signature, timestamp=test_signature_timestamp,
                 user_id=2, user_email='<EMAIL>', user_name_first='Max', user_name_last='Mustermann')))
        assert_status(rv, HTTPStatus.UNAUTHORIZED)
