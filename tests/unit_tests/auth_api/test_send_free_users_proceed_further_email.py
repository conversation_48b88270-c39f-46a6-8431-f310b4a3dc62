import pytest

from app.models.user import App<PERSON><PERSON><PERSON>odel
from app.scheduled_tasks.free_user_reminder_to_proceed_further \
    import send_free_users_proceed_further_email
from tests.unit_tests.auth_api.mocks.sqlalchemy import QueryMock


class TestSendFreeUserProceedFurtherEmail:

    params = [
        (
            [
                AppUserModel(email="<EMAIL>", first_name="Test BD 1"),
                AppUserModel(email="<EMAIL>", first_name="Test BD 2"),
                AppUserModel(email="<EMAIL>", first_name="Test BD 3"),
                AppUserModel(email="<EMAIL>", first_name="Test BD 4"),
                AppUserModel(email="<EMAIL>", first_name="Test BD 5")
            ],
            5
        )
    ]

    @pytest.mark.parametrize('result, sent_emails', params)
    def test_send_free_users_proceed_further_email(self, mocker, result, sent_emails):
        mocker.patch('app.scheduled_tasks.free_user_reminder_to_proceed_further._build_query',
                     return_value=QueryMock(result))
        send_email = mocker.patch('app.scheduled_tasks.free_user_reminder_to_proceed_further._send_email')
        send_free_users_proceed_further_email()
        assert send_email.call_count == sent_emails
