import pytest
from app.models.group_helper import GroupHelper
from app.models.user import GroupModel, UserModel, UserGroupModel


class TestGroupHelper:
    @pytest.fixture
    def mock_db_session(self, mocker):
        return mocker.patch('app.extensions.db.session')

    @pytest.fixture
    def mock_group_model(self, mocker):
        return mocker.patch('app.models.user.GroupModel')

    @pytest.fixture
    def mock_user_group_model(self, mocker):
        return mocker.patch('app.models.user.UserGroupModel')

    def test_get_everyone_group(self, mock_group_model):
        # Arrange
        company_id = 1
        expected_group = GroupModel(id=1, name=GroupHelper.GROUP_NAME, company_id=company_id)
        mock_group_model.query.filter_by.return_value.first.return_value = expected_group

        # Act
        result = GroupHelper.get_everyone_group(company_id)

        # Assert
        assert result == expected_group
        mock_group_model.query.filter_by.assert_called_once_with(company_id=company_id, name=GroupHelper.GROUP_NAME)

    def test_create_everyone_group(self, mock_group_model, mock_db_session):
        # Arrange
        company_id = 1
        expected_group = GroupModel(
            name=GroupHelper.GROUP_NAME,
            description="System team containing all users in the company",
            company_id=company_id,
            is_system=True
        )
        mock_group_model.return_value = expected_group

        # Act
        result = GroupHelper.create_everyone_group(company_id)

        # Assert
        assert result == expected_group
        mock_group_model.assert_called_once_with(
            name=GroupHelper.GROUP_NAME,
            description="System team containing all users in the company",
            company_id=company_id,
            is_system=True
        )
        mock_db_session.add.assert_called_once_with(expected_group)
        mock_db_session.commit.assert_called_once()

    def test_add_user_to_everyone_group_when_user_has_no_company(self, mock_group_model):
        # Arrange
        user = UserModel(id=1, company_id=None)

        # Act
        GroupHelper.add_user_to_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_not_called()

    def test_add_user_to_everyone_group_when_group_does_not_exist(self, mock_group_model):
        # Arrange
        user = UserModel(id=1, company_id=1)
        mock_group_model.query.filter_by.return_value.first.return_value = None

        # Act
        GroupHelper.add_user_to_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_called_once_with(company_id=user.company_id,
                                                                 name=GroupHelper.GROUP_NAME)

    def test_add_user_to_everyone_group_when_user_already_in_group(self, mock_group_model):
        # Arrange
        group = GroupModel(id=1, name=GroupHelper.GROUP_NAME)
        user = UserModel(id=1, company_id=1)
        group.users = [user]
        mock_group_model.query.filter_by.return_value.first.return_value = group

        # Act
        GroupHelper.add_user_to_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_called_once_with(company_id=user.company_id,
                                                                 name=GroupHelper.GROUP_NAME)

    def test_add_user_to_everyone_group_success(self, mock_group_model, mock_user_group_model, mock_db_session):
        # Arrange
        group = GroupModel(id=1, name=GroupHelper.GROUP_NAME)
        user = UserModel(id=1, company_id=1)
        group.users = []
        mock_group_model.query.filter_by.return_value.first.return_value = group
        user_group = UserGroupModel(user_id=user.id, group_id=group.id)
        mock_user_group_model.return_value = user_group

        # Act
        GroupHelper.add_user_to_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_called_once_with(company_id=user.company_id,
                                                                 name=GroupHelper.GROUP_NAME)
        mock_user_group_model.assert_called_once_with(user_id=user.id, group_id=group.id)
        mock_db_session.add.assert_called_once_with(user_group)
        mock_db_session.commit.assert_called_once()

    def test_remove_user_from_everyone_group_when_user_has_no_company(self, mock_group_model):
        # Arrange
        user = UserModel(id=1, company_id=None)

        # Act
        GroupHelper.remove_user_from_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_not_called()

    def test_remove_user_from_everyone_group_when_group_does_not_exist(self, mock_group_model):
        # Arrange
        user = UserModel(id=1, company_id=1)
        mock_group_model.query.filter_by.return_value.first.return_value = None

        # Act
        GroupHelper.remove_user_from_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_called_once_with(company_id=user.company_id,
                                                                 name=GroupHelper.GROUP_NAME)

    def test_remove_user_from_everyone_group_when_user_not_in_group(self, mock_group_model):
        # Arrange
        group = GroupModel(id=1, name=GroupHelper.GROUP_NAME)
        user = UserModel(id=1, company_id=1)
        group.users = []
        mock_group_model.query.filter_by.return_value.first.return_value = group

        # Act
        GroupHelper.remove_user_from_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_called_once_with(company_id=user.company_id,
                                                                 name=GroupHelper.GROUP_NAME)

    def test_remove_user_from_everyone_group_success(self, mock_group_model, mock_user_group_model, mock_db_session):
        # Arrange
        group = GroupModel(id=1, name=GroupHelper.GROUP_NAME)
        user = UserModel(id=1, company_id=1)
        group.users = [user]
        mock_group_model.query.filter_by.return_value.first.return_value = group
        mock_user_group_model.query.filter_by.return_value.delete.return_value = None

        # Act
        GroupHelper.remove_user_from_everyone_group(user)

        # Assert
        mock_group_model.query.filter_by.assert_called_once_with(company_id=user.company_id,
                                                                 name=GroupHelper.GROUP_NAME)
        mock_user_group_model.query.filter_by.assert_called_once_with(user_id=user.id, group_id=group.id)
        mock_db_session.commit.assert_called_once()
