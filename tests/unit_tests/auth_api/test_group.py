# Standard lib imports
from http import HTTPStatus

import pytest
# Local imports
from octimine_common.exceptions import NotFoundException
from octimine_common.tests.data import access_token

from app.models.user import GroupModel, UserModel
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.mocks.jwt import get_admin_jwt_claims
from tests.unit_tests.auth_api.url import GROUP_URL, USER_GROUP_URL


class TestGroup:

    def test_failure_without_auth(self, client):
        rv = client.get(GROUP_URL, json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

        rv = client.post(GROUP_URL, json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

        rv = client.patch(f'{GROUP_URL}/1', json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

        rv = client.delete(f'{GROUP_URL}/1', json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

        rv = client.put(f'{GROUP_URL}/1/users/1', json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

        rv = client.delete(f'{GROUP_URL}/1/users/1', json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

        rv = client.get(f'{GROUP_URL}/1/users', json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

        rv = client.get(f'{USER_GROUP_URL}/1/groups', json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    def test_get_groups_successful(self, client, mocker):
        mocker.patch('app.models.user.GroupModel.get_all', return_value=[GroupModel(id=1, name='abc'),
                                                                         GroupModel(id=2, name='xyz')])
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        headers = dict(Authorization="Bearer %s" % access_token)

        rv = client.get(GROUP_URL, headers=headers)
        result = rv.get_json()['data']
        assert_status(rv, HTTPStatus.OK)
        assert 'groups' in result
        assert 'page' in result

    create_parameters = [
        ({}, HTTPStatus.BAD_REQUEST),
        ({"name": ""}, HTTPStatus.BAD_REQUEST),
        ({"name": "", "description": "abc"}, HTTPStatus.BAD_REQUEST),
        ({"id": 1, "name": "abc", "description": "abc"}, HTTPStatus.BAD_REQUEST),
        ({"name": "Test Group", "description": "abc"}, HTTPStatus.CREATED)
    ]

    @pytest.mark.parametrize('request_data, expected_status', create_parameters)
    def test_create_group(self, client, mocker, request_data, expected_status):
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.user.GroupModel.get_all', return_value=[])
        mocker.patch('app.models.user.GroupModel.save')
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.post(GROUP_URL, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    update_parameters = [
        ({"name": ""}, HTTPStatus.BAD_REQUEST),
        ({"name": "", "description": "abc"}, HTTPStatus.BAD_REQUEST),
        ({"id": 1, "name": "abc", "description": "abc"}, HTTPStatus.BAD_REQUEST),
        ({}, HTTPStatus.OK),
        ({"name": "Test Group"}, HTTPStatus.OK),
        ({"description": "abc"}, HTTPStatus.OK),
        ({"name": "Test Group", "description": "abc"}, HTTPStatus.OK)
    ]

    @pytest.mark.parametrize('request_data, expected_status', update_parameters)
    def test_update_group(self, client, mocker, request_data, expected_status):
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.user.GroupModel.get_one', return_value=GroupModel(id=1))
        mocker.patch('app.models.user.GroupModel.save')
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(f'{GROUP_URL}/1', headers=headers, json=request_data)
        assert_status(rv, expected_status)

    add_user_to_group_parameters = [
        (None, None, HTTPStatus.NOT_FOUND),
        (UserModel(id=1), None, HTTPStatus.NOT_FOUND),
        (None, GroupModel(id=1), HTTPStatus.NOT_FOUND),
        (UserModel(id=1), GroupModel(id=1), HTTPStatus.NO_CONTENT)
    ]

    @pytest.mark.parametrize('user, group, expected_status', add_user_to_group_parameters)
    def test_add_user_to_group(self, client, mocker, user, group, expected_status):
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        if user:
            mocker.patch('app.models.user.UserModel.get_one', return_value=user)
        else:
            mocker.patch('app.models.user.UserModel.get_one', side_effect=NotFoundException())

        if group:
            mocker.patch('app.models.user.GroupModel.get_one', return_value=group)
        else:
            mocker.patch('app.models.user.GroupModel.get_one', side_effect=NotFoundException())
        mocker.patch('app.models.user.GroupModel.save')
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.put(f'{GROUP_URL}/1/users/1', headers=headers)
        assert_status(rv, expected_status)

    remove_user_from_group_parameters = [
        (None, None, HTTPStatus.NOT_FOUND),
        (UserModel(id=1), None, HTTPStatus.NOT_FOUND),
        (None, GroupModel(id=1), HTTPStatus.NOT_FOUND),
        (UserModel(id=1), GroupModel(id=1), HTTPStatus.NO_CONTENT)
    ]

    @pytest.mark.parametrize('user, group, expected_status', remove_user_from_group_parameters)
    def test_remove_user_from_group(self, client, mocker, user, group, expected_status):
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        if user:
            mocker.patch('app.models.user.UserModel.get_one', return_value=user)
        else:
            mocker.patch('app.models.user.UserModel.get_one', side_effect=NotFoundException())

        if group:
            group.users.append(user)
            mocker.patch('app.models.user.GroupModel.get_one', return_value=group)
        else:
            mocker.patch('app.models.user.GroupModel.get_one', side_effect=NotFoundException())
        mocker.patch('app.models.user.GroupModel.save')
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.delete(f'{GROUP_URL}/1/users/1', headers=headers)
        assert_status(rv, expected_status)

    users_of_group_parameters = [
        (GroupModel(id=1, users=[UserModel(id=1), UserModel(id=2)]), HTTPStatus.OK)
    ]

    @pytest.mark.parametrize('group, expected_status', users_of_group_parameters)
    def test_get_users_of_group(self, client, mocker, group, expected_status):
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.user.GroupModel.get_one', return_value=group)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(f'{GROUP_URL}/1/users', headers=headers)
        assert_status(rv, expected_status)
        users = rv.get_json()['data']['users']
        assert set([u['id'] for u in users]) == set([u.id for u in group.users])

    groups_of_users_parameters = [
        (UserModel(id=1, groups=[GroupModel(name='abc'), GroupModel(name='xyz')]), HTTPStatus.OK)
    ]

    @pytest.mark.parametrize('user, expected_status', groups_of_users_parameters)
    def test_get_groups_of_user(self, client, mocker, user, expected_status):
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.user.UserModel.get_one', return_value=user)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(f'{USER_GROUP_URL}/1/groups', headers=headers)
        assert_status(rv, expected_status)
        groups = rv.get_json()['data']['groups']
        assert set([g['name'] for g in groups]) == set([g.name for g in user.groups])

    delete_group_parameters = [
        (GroupModel(id=1), HTTPStatus.NO_CONTENT),
        (None, HTTPStatus.NOT_FOUND)
    ]

    @pytest.mark.parametrize('group, expected_status', delete_group_parameters)
    def test_delete_group(self, client, mocker, group, expected_status):
        mocker.patch('app.routes.auth.manager.group.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        if group:
            mocker.patch('app.models.user.GroupModel.get_one', return_value=group)
        else:
            mocker.patch('app.models.user.GroupModel.get_one', side_effect=NotFoundException())
        mocker.patch('app.models.user.GroupModel.delete')
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.delete(f'{GROUP_URL}/1', headers=headers)
        assert_status(rv, expected_status)
