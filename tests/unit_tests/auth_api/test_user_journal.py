# Standard lib imports
from http import HTTPStatus

import pytest

from octimine_common.tests.data import access_token
# Local imports
from tests.unit_tests.auth_api import data
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.mocks.jwt import get_admin_jwt_claims, get_sale_jwt_claims
from tests.unit_tests.auth_api.url import ALL_JOURNAL_URL, USER_ID_JOURNAL_URL


class TestUserJournal:

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_get_all_user_journals_successfully(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user_journal.UserJournalModel.get_all', return_value=[])
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(ALL_JOURNAL_URL, headers=headers)
        assert_status(rv, HTTPStatus.OK)

    def test_get_all_user_journals_not_successfully(self, client, mocker):
        mocker.patch('app.models.user_journal.UserJournalModel.get_all', return_value=[])
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(ALL_JOURNAL_URL, headers=headers)
        assert_status(rv, HTTPStatus.FORBIDDEN)

    @pytest.mark.parametrize('claims', [
        get_admin_jwt_claims(), get_sale_jwt_claims()
    ])
    def test_get_all_journals_by_user_id_successfully(self, client, mocker, claims):
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=claims)
        mocker.patch('app.models.user_journal.UserJournalModel.get_all', return_value=[])
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(USER_ID_JOURNAL_URL % data.user_id, headers=headers)
        assert_status(rv, HTTPStatus.OK)

    def test_get_all_journals_by_user_id_not_successfully(self, client, mocker):
        mocker.patch('app.models.user_journal.UserJournalModel.get_all', return_value=[])
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.get(USER_ID_JOURNAL_URL % data.user_id, headers=headers)
        assert_status(rv, HTTPStatus.FORBIDDEN)
