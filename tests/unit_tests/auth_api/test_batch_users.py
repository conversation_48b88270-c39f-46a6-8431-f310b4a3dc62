# Standard lib imports
from http import HTTPStatus
from datetime import datetime, timedelta
import copy

import pytest

from octimine_common.enums import SubscriptionType

# Local imports
from octimine_common.tests.data import access_token

from app.models.feature import FeatureModel
from app.models.user import UserModel, SubscriptionModel
from app.models.user_journal import UserJournalModel
from tests.unit_tests.auth_api.assertions import assert_status
from tests.unit_tests.auth_api.mocks.jwt import get_admin_jwt_claims, get_sale_jwt_claims
from tests.unit_tests.auth_api.url import BATCH_USERS_UPDATE
from tests.unit_tests.auth_api.data import subscription


class TestBatchUsers:
    patch_batch_users_parameters = [
        ({"user_ids": [10, 11, 12],
          "profile": {"status": "Active", "is_admin": True, "is_sales": False}, "sale_ids": [1]},
         HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "profile": {"status": "Active"}, "sale_ids": [1]}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "profile": {}}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12]}, HTTPStatus.OK),
        ({"user_ids": [-10, 11, 12],
          "profile": {"status": "Active", "is_admin": True, "is_sales": False}},
         HTTPStatus.NOT_FOUND),
        ({"user_ids": [10, 11, 12],
          "profile": {"status": "Active", "is_admin": True, "is_sales": False,
                      "email": "<EMAIL>"}}, HTTPStatus.BAD_REQUEST),
        ({"profile": {"status": "Active", "is_admin": True, "is_sales": False}},
         HTTPStatus.BAD_REQUEST),
    ]

    @pytest.mark.parametrize('request_data, expected_status', patch_batch_users_parameters)
    def test_patch_batch_users_update(self, client, mocker, request_data, expected_status):
        mocker.patch('sqlalchemy.orm.Query.update')
        mocker.patch('sqlalchemy.orm.Query.delete')
        mocker.patch('app.extensions.db.session.bulk_save_objects')
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.user_journal.UserJournalModel.add_user_profile_journal_entry')
        mocker.patch('app.models.user_journal.UserJournalModel.add_user_bd_journal_entry')
        mocker.patch('app.models.user_journal.UserJournalModel.add_user_feature_journal_entry')
        mocker.patch('app.models.user_journal.UserJournalModel.add_subscription_journal_entry')
        mocker.patch('app.models.user_journal.UserJournalModel.post_process')
        users = [UserModel(id=n) for n in request_data['user_ids'] if n > 0] if 'user_ids' in request_data else []
        sales = [UserModel(id=n) for n in request_data['sale_ids']] if 'sale_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_by_ids', return_value=users)
        mocker.patch('app.models.user.UserModel.get_sales_by_ids', return_value=sales)
        journals = [UserJournalModel(user_id=n, modifier_id=2) for n in request_data['user_ids']
                    if n > 0] if 'user_ids' in request_data else []
        mocker.patch('app.models.user_journal.UserJournalModel.create_journals', return_value=journals)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(BATCH_USERS_UPDATE, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    patch_batch_users_parameters = [
        ({"user_ids": [10, 11, 12],
          "profile": {"status": "Active", "is_admin": True, "is_sales": False}, "sale_ids": [1]},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11, 12], "profile": {"status": "Active"}}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12, 1], "profile": {"status": "Active"}}, HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11, 12], "profile": {"status": "Active"}, "sale_ids": [1]}, HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11, 12], "profile": {}}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12, 1], "profile": {}}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12]}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12, 1]}, HTTPStatus.OK),
        ({"user_ids": [-10, 11, 12], "profile": {"status": "Active"}}, HTTPStatus.NOT_FOUND),
        ({"user_ids": [10, 11, 12],
          "profile": {"status": "Active", "email": "<EMAIL>"}}, HTTPStatus.BAD_REQUEST),
        ({"profile": {"status": "Active", "is_admin": True, "is_sales": False}},
         HTTPStatus.BAD_REQUEST),
    ]

    @pytest.mark.parametrize('request_data, expected_status', patch_batch_users_parameters)
    def test_patch_batch_users_update_by_sale(self, client, mocker, request_data, expected_status):
        mocker.patch('sqlalchemy.orm.Query.update')
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_sale_jwt_claims())
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_sale_jwt_claims())
        users = [UserModel(id=n) for n in request_data['user_ids'] if n > 0] if 'user_ids' in request_data else []
        sales = [UserModel(id=n) for n in request_data['sale_ids']] if 'sale_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_by_ids', return_value=users)
        mocker.patch('app.models.user.UserModel.get_sales_by_ids', return_value=sales)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(BATCH_USERS_UPDATE, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    patch_batch_users_subscription_parameters = [
        ({"user_ids": [10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00", "api_package": 1000,
                           "api_access_throttle": 2000, "api_max_result": 3000, "api_max_monitor_profile": 20}},
         HTTPStatus.OK),
        ({"user_ids": [10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00"}}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "subscription": {}}, HTTPStatus.OK),
        ({"user_ids": [-10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00", "api_package": 1000,
                           "api_access_throttle": 2000, "api_max_result": 3000, "api_max_monitor_profile": 20}},
         HTTPStatus.NOT_FOUND),
        ({"user_ids": [10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00", "api_package": 1000,
                           "api_access_throttle": 2000, "api_max_result": 3000, "api_max_monitor_profile": 20,
                           "email": "<EMAIL>"}},
         HTTPStatus.BAD_REQUEST)
    ]

    @pytest.mark.parametrize('request_data, expected_status', patch_batch_users_subscription_parameters)
    def test_patch_batch_users_subscription(self, client, mocker, request_data, expected_status):
        mocker.patch('sqlalchemy.orm.Query.update')
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_admin_jwt_claims())
        users = [UserModel(id=n, subscription=SubscriptionModel(type=SubscriptionType.FREE))
                 for n in request_data['user_ids'] if n > 0] if 'user_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_by_ids', return_value=users)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(BATCH_USERS_UPDATE, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    patch_batch_users_subscription_parameters = [
        ({"user_ids": [10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00", "api_package": 1000,
                           "api_access_throttle": 2000, "api_max_result": 3000, "api_max_monitor_profile": 20}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00"}}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12, 1],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00"}}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "subscription": {}}, HTTPStatus.OK),
        ({"user_ids": [-10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00"}}, HTTPStatus.NOT_FOUND),
        ({"user_ids": [10, 11, 12],
          "subscription": {"type": "FREE", "valid_until": "2020-10-23T00:00:00", "api_package": 1000,
                           "api_access_throttle": 2000, "api_max_result": 3000, "api_max_monitor_profile": 20,
                           "email": "<EMAIL>"}},
         HTTPStatus.BAD_REQUEST)
    ]

    @pytest.mark.parametrize('request_data, expected_status', patch_batch_users_subscription_parameters)
    def test_patch_batch_users_subscription_by_sale(self, client, mocker, request_data, expected_status):
        mocker.patch('sqlalchemy.orm.Query.update')
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_sale_jwt_claims())
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_sale_jwt_claims())
        users = [UserModel(id=n, subscription=SubscriptionModel(type=SubscriptionType.FREE))
                 for n in request_data['user_ids'] if n > 0] if 'user_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_by_ids', return_value=users)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(BATCH_USERS_UPDATE, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    patch_batch_users_features_parameters = [
        ({"user_ids": [10, 11, 12]}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "enabled_feature_ids": [1, 2, 3]}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "enabled_feature_ids": []}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "enabled_feature_ids": [-1, 2, 3]}, HTTPStatus.NOT_FOUND),
        ({"enabled_feature_ids": [-1, 2, 3]}, HTTPStatus.BAD_REQUEST),
        ({"user_ids": [-10, 11, 12], "enabled_feature_ids": [1, 2, 3]}, HTTPStatus.NOT_FOUND),
        ({"user_ids": [10, 11, 12], "disabled_feature_ids": []}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "disabled_feature_ids": [1, 2, 3]}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "disabled_feature_ids": [-1, 2, 3]}, HTTPStatus.NOT_FOUND),
        ({"disabled_feature_ids": [-1, 2, 3]}, HTTPStatus.BAD_REQUEST),
        ({"user_ids": [-10, 11, 12], "disabled_feature_ids": [1, 2, 3]}, HTTPStatus.NOT_FOUND),
        ({"user_ids": [10, 11, 12], "enabled_feature_ids": [1, 2, 3], "disabled_feature_ids": [4, 5]}, HTTPStatus.OK),
        ({"user_ids": [10, 11, 12], "enabled_feature_ids": [1, 2, 4], "disabled_feature_ids": [4, 5]},
         HTTPStatus.BAD_REQUEST)
    ]

    @pytest.mark.parametrize('request_data, expected_status', patch_batch_users_features_parameters)
    def test_patch_batch_users_features(self, client, mocker, request_data, expected_status):
        mocker.patch('sqlalchemy.orm.Query.delete')
        mocker.patch('app.extensions.db.session.bulk_save_objects')
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_admin_jwt_claims())
        users = [UserModel(id=n) for n in request_data['user_ids'] if n > 0] if 'user_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_by_ids', return_value=users)
        enabled_features = [FeatureModel(id=n) for n in request_data['enabled_feature_ids'] if n > 0] \
            if 'enabled_feature_ids' in request_data else []
        disabled_features = [FeatureModel(id=n) for n in request_data['disabled_feature_ids'] if n > 0] \
            if 'disabled_feature_ids' in request_data else []
        features = enabled_features + disabled_features
        mocker.patch('app.models.user.FeatureModel.get_by_ids', return_value=features)
        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(BATCH_USERS_UPDATE, headers=headers, json=request_data)
        assert_status(rv, expected_status)

    yesterday = (datetime.today() - timedelta(days=1)).isoformat(timespec='seconds')
    today = datetime.today().isoformat(timespec='seconds')
    tomorrow = (datetime.today() + timedelta(days=1)).isoformat(timespec='seconds')
    patch_batch_users_trial_subscription_parameters = [
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": today, "valid_until": tomorrow}}, HTTPStatus.OK),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": today, "valid_until": today}},
         HTTPStatus.OK),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": yesterday, "valid_until": today}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": today, "valid_until": yesterday}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": "", "valid_until": today}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": today, "valid_until": ""}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": yesterday, "valid_until": today}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": None, "valid_until": today}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": today, "valid_until": None}},
         HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "valid_until": today}}, HTTPStatus.BAD_REQUEST),
        ({"user_ids": [10, 11],
          "subscription": {"type": "TRIAL", "current_term_started_at": today}}, HTTPStatus.BAD_REQUEST)
    ]

    @pytest.mark.parametrize('request_data, expected_status', patch_batch_users_trial_subscription_parameters)
    def test_patch_batch_users_trial_subscription(self, client, mocker, request_data, expected_status):
        mocker.patch('sqlalchemy.orm.Query.update')
        mocker.patch('app.extensions.db.session.commit')
        mocker.patch('octimine_common.utils.routes.authorization.get_jwt_claims', return_value=get_sale_jwt_claims())
        mocker.patch('app.models.current_user_helper.get_jwt_claims', return_value=get_sale_jwt_claims())

        users = [UserModel(id=n, subscription=copy.deepcopy(subscription))
                 for n in request_data['user_ids'] if n > 0] if 'user_ids' in request_data else []
        mocker.patch('app.models.user.UserModel.get_by_ids', return_value=users)

        journals = [UserJournalModel(user_id=n, modifier_id=2) for n in request_data['user_ids']
                    if n > 0] if 'user_ids' in request_data else []
        mocker.patch('app.models.user_journal.UserJournalModel.create_journals', return_value=journals)
        mocker.patch('app.models.user.UserModel.batch_update_profile')
        mocker.patch('app.models.user.UserModel.batch_update_sales')
        mocker.patch('app.models.user.UserModel.batch_update_features')
        mocker.patch('app.models.user_journal.UserJournalModel.add_subscription_journal_entry')
        mocker.patch('app.models.user_journal.UserJournalModel.post_process')

        headers = dict(Authorization="Bearer %s" % access_token)
        rv = client.patch(BATCH_USERS_UPDATE, headers=headers, json=request_data)
        assert_status(rv, expected_status)
