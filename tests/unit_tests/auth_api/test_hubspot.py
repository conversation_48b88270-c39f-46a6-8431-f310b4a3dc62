from typing import <PERSON><PERSON>
from unittest.mock import Mock
from hubspot.crm.contacts import SimplePublicObject, SimplePublicObjectInputForCreate, SimplePublicObjectInput, \
    PublicObjectSearchRequest, CollectionResponseWithTotalSimplePublicObjectForwardPaging
from app.ext.crm.hubspot_extension import <PERSON><PERSON>potExtension, HubspotContact


class TestHubspot:

    @classmethod
    def _get_hubspot_extension_and_client(cls, app, mocker) -> <PERSON><PERSON>[HubspotExtension, Mock]:
        extension = HubspotExtension(app=app)
        client = mocker.Mock()
        extension._client = client
        return extension, client

    def test_should_create_contact(self, app, mocker):
        hubspot_extension, client = self._get_hubspot_extension_and_client(app, mocker)
        client.crm.contacts.basic_api.create.return_value = SimplePublicObject(id='1x00000b')
        contact = HubspotContact(email='<EMAIL>', firstname="<PERSON>", lastname="<PERSON><PERSON>", country="United States",
                                 company="ACME Inc", zip="00000", state="New York", octimine_client=True)
        created_contact = hubspot_extension.create_contact(contact)
        client.crm.contacts.basic_api.create.assert_called_with(
            simple_public_object_input_for_create=SimplePublicObjectInputForCreate(properties={
                "client_not_client": None,
                "email": '<EMAIL>',
                "company": "ACME Inc",
                "country": "United States",
                "firstname": "John",
                "lastname": "Doe",
                "leadsource": None,
                "source_details": None,
                "contact_source_details": None,
                "id": None,
                "zip": "00000",
                "state": "New York",
                "octimine_client": True
            }), )
        assert created_contact.id == '1x00000b'

    def test_should_update_contact(self, app, mocker):
        hubspot_extension, client = self._get_hubspot_extension_and_client(app, mocker)
        contact_id = '1x00000b'
        client.crm.contacts.basic_api.update.return_value = SimplePublicObject(id=contact_id)
        contact = HubspotContact(email='<EMAIL>', firstname="John", lastname="Doe", country="United States",
                                 company="ACME Inc", zip="00000", state="New York", octimine_client=True)
        updated_contact = hubspot_extension.update_contact(contact_id, contact)
        client.crm.contacts.basic_api.update.assert_called_with(
            contact_id=contact_id,
            simple_public_object_input=SimplePublicObjectInput(properties={
                "client_not_client": None,
                "email": '<EMAIL>',
                "company": "ACME Inc",
                "country": "United States",
                "firstname": "John",
                "lastname": "Doe",
                "leadsource": None,
                "source_details": None,
                "contact_source_details": None,
                "zip": "00000",
                "state": "New York",
                "octimine_client": True
            }), )
        assert updated_contact is not None

    def test_should_find_contact_by_email(self, app, mocker):
        hubspot_extension, client = self._get_hubspot_extension_and_client(app, mocker)
        email = '<EMAIL>'
        client.crm.contacts.search_api.do_search.return_value = \
            CollectionResponseWithTotalSimplePublicObjectForwardPaging(
                total=1,
                results=[
                    SimplePublicObject(id="1x00000b", properties={"email": email})
                ]
            )

        hubspot_extension.find_contact_by_email(email)
        client.crm.contacts.search_api.do_search.assert_called_with(
            public_object_search_request=PublicObjectSearchRequest(
                limit=1,
                properties=HubspotContact.properties(),
                filter_groups=[
                    {
                        "filters": [
                            {"propertyName": "email", "operator": "EQ", "value": email}
                        ]
                    }
                ]
            )
        )
