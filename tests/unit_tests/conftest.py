import pytest
import octimine_common.tests.jwt_mocks as jwt_mocks
from app.app import create_app, message_bus
from app.models.aliases.applicants import ApplicantAliasModel


@pytest.fixture(scope="session")
def app():
    app = create_app('testing')
    return app


@pytest.fixture(autouse=True)
def clean_message_bus():
    message_bus.clean_handlers()


@pytest.fixture(autouse=True)
def jwt_mock(app, mocker):
    mocker.patch('flask_jwt_extended.view_decorators._decode_jwt_from_headers', jwt_mocks.decode_jwt_from_headers)
    mocker.patch('flask_jwt_extended.jwt_manager._decode_jwt', jwt_mocks.decode_jwt)


@pytest.fixture
def patch_config(app, mocker):
    return lambda values: mocker.patch.dict(app.config, values)


@pytest.fixture(autouse=True)
def mock_applicant_aliases(mocker):
    mocker.patch('app.models.aliases.applicants.ApplicantAliasModel.get_all',
                 return_value=[ApplicantAliasModel(applicant="IBM CORP", alias="IBM")])


@pytest.fixture(autouse=True)
def enrich_documents(mocker):
    mocker.patch('app.models.utils.processdocuments.ProcessDocuments.post')


@pytest.fixture(autouse=True)
def mark_document_as_read(mocker):
    mocker.patch('app.models.read_document.document_helper.DocumentHelper.mark_document_as_read')


@pytest.fixture(autouse=True)
def tag_assignments(mocker):
    mocker.patch('app.models.tags.tag.TagModel.get_assignments', return_value=[])
