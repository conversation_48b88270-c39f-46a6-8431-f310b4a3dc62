import pytest
from app.background_tasks.monitor.runs.results.types.utils import get_elbow_cutoff_index


class TestElbowCutoff:
    def test_should_tell_index_of_sorted_desc_list(self):
        assert get_elbow_cutoff_index(range(1000, 0, -1)) == 170

    @pytest.mark.parametrize('y', [
        [2, 1],
        [3, 2, 1],
        [4, 3, 2, 1],
        [5, 4, 3, 2, 1],
    ])
    def test_should_not_tell_index_when_elbow_not_found(self, y):
        assert get_elbow_cutoff_index(y) is None
