# Third party imports
import pytest
from fakeredis import FakeServer, FakeStrictRedis
from octimine_common.exceptions import APIException

# Local imports
from octimine_common.request_ctx import set_ctx
from octimine_common.tests.data import access_jti, refresh_jti, access_token, manager_access_jti

from app.app import session_store, cache_store, rate_limiter
from app.models.aliases.applicants import ApplicantAliasModel
from tests.unit_tests.web_api import data
from app.services import search
from app.services import users


@pytest.fixture(autouse=True)
def redis(app, mocker):
    redis_server = FakeServer()
    redis_client = FakeStrictRedis(server=redis_server, decode_responses=True)
    redis_client.set(access_jti, "false")
    redis_client.set(manager_access_jti, "false")
    redis_client.set(refresh_jti, "false")
    mocker.patch.object(session_store, "_redis_client", redis_client)
    mocker.patch.object(cache_store, "_redis_client", redis_client)
    mocker.patch.object(rate_limiter, "_redis_store", redis_client)


@pytest.fixture(autouse=True)
def sqlalchemy_mock(app, mocker):
    mocker.patch('sqlalchemy.orm.Session.add')
    mocker.patch('sqlalchemy.orm.Session.delete')
    mocker.patch('sqlalchemy.orm.Session.merge')
    mocker.patch('sqlalchemy.orm.Session.commit')
    mocker.patch('sqlalchemy.orm.Session.flush')
    mocker.patch('sqlalchemy.orm.Session.refresh')


@pytest.fixture
def headers():
    return {'Authorization': f'Bearer {access_token}'}


@pytest.fixture(autouse=True)
def mock_applicant_aliases(mocker):
    mocker.patch('app.models.aliases.applicants.ApplicantAliasModel.get_all',
                 return_value=[ApplicantAliasModel(applicant="IBM CORP", alias="IBM")])


@pytest.fixture
def process_documents(mocker):
    mocker.patch('app.models.utils.processdocuments.ProcessDocuments.post')


@pytest.fixture
def mock_read_documents(mocker):
    mocker.patch('app.models.read_document.read_document.ReadDocumentModel.get_user_read_documents_ids',
                 return_value={'9727212': 'now', '1232432': None})


@pytest.fixture
def search_mock(mocker):
    mock = mocker.Mock()
    document_search_response = search.SearchResponse.from_dict(data.document_response['data'])
    semantic_search_response = search.SearchResponse.from_dict(data.semantic_search_success['data'])
    boolean_search_response = search.SearchResponse.from_dict(data.boolean_search_success['data'])
    citations_search_response = search.SearchResponse.from_dict(data.citation_flat_search_success['data'])
    patent_numbers_search_response = search.SearchResponse.from_dict(data.patent_number_search_success['data'])
    cached_search_response = search.SearchResponse.from_dict(data.document_response['data'])
    mock.Family = search.Family
    mock.Publication = search.Publication
    mock.search_by_semantic_query.return_value = semantic_search_response
    mock.search_by_document_ids.return_value = document_search_response
    mock.search_by_boolean_query.return_value = boolean_search_response
    mock.search_for_citations.return_value = citations_search_response
    mock.search_by_patent_numbers.return_value = patent_numbers_search_response
    mock.get_cached_search.return_value = cached_search_response
    mock.get_family.return_value = search.Family(data.single_document_response['data']['document'])
    mock.get_publication_info_for.return_value = data.publication_info_success
    async_result = mocker.Mock()
    async_result.get.return_value = search.SearchResponse.from_dict(data.document_response['data'])
    mock.asynchronous.search_by_document_ids.return_value = async_result
    mock.asynchronous.search_by_boolean_query.return_value = async_result
    mock.asynchronous.search_by_patent_numbers.return_value = async_result
    return mock


@pytest.fixture
def publication_search_mock(search_mock):
    document_search_response = search.SearchResponse.from_dict(data.publication_response['data'])
    boolean_search_response = search.SearchResponse.from_dict(data.boolean_publication_search_success['data'])
    search_mock.search_by_document_ids.return_value = document_search_response
    search_mock.search_by_boolean_query.return_value = boolean_search_response
    return search_mock


@pytest.fixture
def empty_search_mock(mocker, search_mock):
    empty_response = search.SearchResponse.from_dict(data.empty_search_response['data'])
    search_mock.search_by_semantic_query.return_value = empty_response
    search_mock.search_by_document_ids.return_value = empty_response
    search_mock.search_by_boolean_query.return_value = empty_response
    search_mock.search_for_citations.return_value = empty_response
    search_mock.search_by_patent_numbers.return_value = empty_response
    search_mock.get_cached_search.return_value = empty_response
    search_mock.get_family.return_value = search.Family(data.single_document_response['data'])
    async_result = mocker.Mock()
    async_result.get.return_value = empty_response
    search_mock.asynchronous.search_by_document_ids.return_value = async_result
    search_mock.asynchronous.search_by_boolean_query.return_value = async_result
    search_mock.asynchronous.search_by_patent_numbers.return_value = async_result
    return search_mock


@pytest.fixture
def error_search_mock(mocker, search_mock):
    error = APIException(code=500, message='Some error occurred')
    mock = search_mock
    mock.search_by_semantic_query.side_effect = error
    mock.search_by_document_ids.side_effect = error
    mock.search_by_boolean_query.side_effect = error
    mock.search_for_citations.side_effect = error
    mock.get_family.side_effect = error
    mock.get_publication_info_for.return_value = error
    async_result = mocker.Mock()
    async_result.get.side_effect = error
    mock.asynchronous.search_by_document_ids.side_effect = async_result
    mock.asynchronous.search_by_boolean_query.side_effect = async_result
    mock.asynchronous.search_by_patent_numbers.side_effect = async_result
    return mock


@pytest.fixture
def rni_mock(mocker):
    mock = mocker.Mock()
    mock.compute_rich_neighborhood_indicator.return_value = {'status': 'STARTED'}
    return mock


@pytest.fixture
def pma_mock(mocker):
    mock = mocker.Mock()
    mock.map_application_numbers.return_value = data.map_pma_application_numbers_success
    return mock


@pytest.fixture
def legal_status_mock(mocker):
    mock = mocker.Mock()
    mock.get_family_legal_status_details.return_value = data.document_legal_status_success['data']
    return mock


@pytest.fixture
def users_mock(mocker):
    mock = mocker.Mock()
    mock.get_user.return_value = users.UserDetails({
        'user': {
            'id': 1,
            'company_id': 1,
            'first_name': 'Test',
            'last_name': 'Test',
            'groups': [{'id': 1}, {'id': 2}]
        }
    })
    mock.get_users.return_value = [{'id': 1, 'company_id': 1}]
    return mock


@pytest.fixture
def app_request():
    return data.get_app_request()


@pytest.fixture
def request_ctx(app):
    with app.app_context():
        set_ctx(data.get_request_ctx())
        yield
