import pytest
from http import HTTPStatus

from app.services.users import UserDetails
from tests.unit_tests.web_api import data
from app.services.search.responses import Family
from tests.unit_tests.web_api.assertions import assert_status
from app.models.result_collection.collection import CollectionModel
from app.models.landscape.profile import LandscapeProfileModel

from app.models.collaboration.collaboration import CollaborationModel


class TestCollaborations:
    URL = '/web/collaborations'

    def test_should_not_create_when_no_auth_token(self, client):
        rv = client.post(self.URL, json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    @pytest.mark.parametrize('payload', [
        {},
        {'group_ids': [], 'resource_type': 'COLLECTION'},
        {'group_ids': [], 'resource_id': 1},
        {'group_ids': [], 'resource_id': None, 'resource_type': None},
        {'group_ids': [], 'resource_id': 1, 'resource_type': None},
        {'group_ids': [], 'resource_id': None, 'resource_type': 'COLLECTION'},
        {'group_ids': [], 'user_ids': None, 'resource_id': None, 'resource_type': 'PATENT'},
        {'group_ids': [], 'user_ids': [2], 'resource_id': 1, 'resource_type': 'COLLECTION', 'permission': 'READONLY',
         'status': 'NEW'}
    ])
    def test_should_not_collaborate_when_invalid_payload(self, client, mocker, headers, payload):
        mocker.patch.object(CollaborationModel, "get_resource_collaborators", return_value=set())
        mocker.patch.object(CollectionModel, "get_one", return_value=CollectionModel(name='test', user_id=1))
        rv = client.post(self.URL, headers=headers, json=payload)
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    @pytest.mark.parametrize('payload', [
        {'group_ids': [], 'user_ids': [2], 'resource_id': 12, 'resource_type': 'COLLECTION', 'permission': 'READONLY'},
        {'group_ids': [], 'user_ids': [2], 'resource_id': 12, 'resource_type': 'LANDSCAPE', 'permission': 'READONLY'},
    ])
    def test_should_not_collaborate_when_invalid_resource(self, client, mocker, headers, payload):
        mocker.patch.object(CollaborationModel, "get_resource_collaborators", return_value=set())
        mocker.patch('app.services.collection.collection.CollectionPermissions.read', False)
        mocker.patch('app.services.landscape.profile.can_read_profile', return_value=False)
        mocker.patch.object(CollectionModel, "get_one", return_value=CollectionModel(id=12, name='test', user_id=5))
        mocker.patch.object(LandscapeProfileModel, "get_one", return_value=LandscapeProfileModel(id=12, name='test',
                                                                                                 user_id=5))
        rv = client.post(self.URL, headers=headers, json=payload)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    @pytest.mark.parametrize('payload', [
        {'group_ids': [], 'user_ids': [2], 'resource_id': 12, 'resource_type': 'COLLECTION'},
        {'group_ids': [], 'user_ids': [2], 'resource_id': 12, 'resource_type': 'PATENT'},
        {'group_ids': [], 'user_ids': [2], 'resource_id': 12, 'resource_type': 'LANDSCAPE'}
    ])
    def test_should_collaborate(self, client, mocker, headers, payload):
        mocker.patch.object(CollaborationModel, "get_resource_collaborators", return_value=set())
        mocker.patch.object(CollectionModel, "get_one", return_value=CollectionModel(id=12, name='test', user_id=1))
        mocker.patch.object(CollectionModel, "get_results", return_value=[])
        mocker.patch.object(LandscapeProfileModel, "get_one", return_value=LandscapeProfileModel(id=12, name='test',
                                                                                                 user_id=1))
        mocker.patch('app.extensions.db.session.bulk_save_objects')
        mocker.patch('app.services.landscape.profile.notify')
        mocker.patch('app.services.collection.collection.notify')
        mocker.patch('app.services.collection.collection.attach_images_for_documents')
        mocker.patch('app.services.patent.patent.notify')
        mocker.patch('app.services.patent.patent.attach_images_for_documents')
        mocker.patch('app.services.patent.patent.get_patent', return_value=Family(data.test_documents[0]))
        mocker.patch('app.services.users.get_user', return_value=UserDetails({'profile': {'id': 1}}))
        rv = client.post(self.URL, headers=headers, json=payload)
        assert_status(rv, HTTPStatus.NO_CONTENT)

    @pytest.mark.parametrize('payload', [
        {'group_ids': [], 'user_ids': [2], 'resource_id': 12, 'resource_type': 'COLLECTION', 'permission': 'READONLY'},
        {'group_ids': [], 'user_ids': [2], 'resource_id': 12, 'resource_type': 'LANDSCAPE', 'permission': 'READONLY'},
    ])
    def test_collaborator_should_collaborate(self, client, mocker, headers, payload):
        mocker.patch.object(CollaborationModel, "get_resource_collaborators", return_value=set())
        mocker.patch('app.services.collection.collection.CollectionPermissions.read', return_value=True)
        mocker.patch('app.services.collection.collection.CollectionPermissions.write', return_value=True)
        mocker.patch('app.services.landscape.profile.can_read_profile', return_value=True)
        mocker.patch.object(CollectionModel, "get_one", return_value=CollectionModel(id=12, name='test', user_id=5))
        mocker.patch.object(CollectionModel, "get_results", return_value=[])
        mocker.patch('app.extensions.db.session.bulk_save_objects')
        mocker.patch('app.services.landscape.profile.notify')
        mocker.patch('app.services.collection.collection.notify')
        mocker.patch('app.services.collection.collection.attach_images_for_documents')
        profile = LandscapeProfileModel(id=12, name='test', user_id=5)
        mocker.patch.object(LandscapeProfileModel, "get_one", return_value=profile)
        mocker.patch('app.services.landscape.profile._find_profile', return_value=profile)
        mocker.patch('app.services.users.get_user', return_value=UserDetails({'profile': {'id': 0}}))
        rv = client.post(self.URL, headers=headers, json=payload)
        assert_status(rv, HTTPStatus.NO_CONTENT)
