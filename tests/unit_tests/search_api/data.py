# Third party imports
import json
import os
import random

from flask import current_app

# Local imports
from app.ext.elastic_search.field_mappings import FAMILIES, PAPERS

ES_EXTENSION_PATH = 'app.ext.elastic_search.elastic_search_extension.ElasticSearchExtension'
BASE_EXTENSION_PATH = 'app.ext.base_extension.BaseExtension'
DOC_ID_PATH = f'{ES_EXTENSION_PATH}.get_documents_by_id'
PATENT_NUMBER_PATH = f'{ES_EXTENSION_PATH}.get_documents_from_patent_numbers'
NPL_QUERY_PATH = f'{ES_EXTENSION_PATH}.npl_query'
BOOL_QUERY_PATH = f'{ES_EXTENSION_PATH}.boolean_query'
DOC_INFO_PATH = f'{ES_EXTENSION_PATH}.get_documents_info_by_id'
DOC_HIGHLIGHT_PATH = f'{ES_EXTENSION_PATH}.get_document_highlight'
GET_CACHED_SEARCH_PATH = f'{BASE_EXTENSION_PATH}.get_cached_search'

# User
user_id = 1

# Queue
queue_username = 'username'
wrong_queue_username = 'wrong_username'
queue_password = 'password'
wrong_queue_password = 'wrong_password'
queue_host = 'localhost'
wrong_queue_host = 'wrong_host'
queue_port = 5600
wrong_queue_port = 5601
queue_exchange = 'queue_exchange'

# Page
number_of_pages = 5

# Search
search_user_id = 1
search_text = 'Text'
search_boolean_input_lexer_error = "APPLICANTS=TEST \nOR $%$ APPLICANTS=John Doe"
search_boolean_input_parser_error = "SOMETHING=John Doe"
search_boolean_input_semantic_error = "IPC=1"
search_boolean_input = "APPLICANTS=John Doe"
search_boolean_input_proximity_operator = "APPLICANTS=John Doe OR (TEXT=a \"method\"~2 OR TEXT=test \"process\"~3)"
search_publication_date = '2000-01-01'
search_application_date = "APPLICATION_DATE=2000-12-12"
search_application_year = "APPLICATION_DATE=2000"
search_priority_date = '1999-01-01'
search_patent_numbers = ['EP-2049363-A2', 'EP-0002007-A1']
search_document_ids = [1, 2]
search_nodes_ids = [1, 2]
search_citation_direction = "bidirectional"
search_citation_patent_number = 'EP-1-1'
search_max_page = 2
npl_corpus_id = 1
date_filters = {
    "earliest_priority_date": "1990-01-01",
    "latest_priority_date": "2020-12-31",
    "earliest_publication_date": "1999-05-18",
    "latest_publication_date": "2019-05-18",
    "earliest_arrived_at": "1999-05-18",
    "latest_arrived_at": "2019-05-18",
}
search_filters = {
    "earliest_priority_date": "1990-01-01",
    "latest_priority_date": "2020-12-31",
    "earliest_publication_date": "1999-05-18",
    "latest_publication_date": "2019-05-18",
    "earliest_arrived_at": "1999-05-18",
    "latest_arrived_at": "2019-05-18",
    "quantity_cut_off": 50,
    "similarity_cut_off": 500,
    "inclusion_and": ["one", "two", "three"],
    "inclusion_or": ["four", "five"],
    "exclusion_and": ["eins", "zwei", "drei"],
    "exclusion_or": ["vier", "fünf"],
    "applicants_plus": ["Siemens", "Bosch"],
    "applicants_minus": ["IBM", "Deutsche Bahn"],
    "cpc_plus": ["A01C7/18"],
    "cpc_minus": ["B01J2523/00", "C07D403/12"],
    "ipc_plus": ["A01K63/04"],
    "ipc_minus": ["D05B21/00", "E09B19/16"],
    "legal_status": ["valid", "invalid", "unknown"],
    "ma1": True,
    "ma2": False,
    "ma3": True,
    "ma5": True,
    "text_weighting": 0.8,
    "free_text_query": "TITLE=device AND NOT (CPC4=B62K)"
}
es_result_empty = {
    "hits": {
        "total": 0,
        "hits": []
    }
}

es_result_terms = {
    "aggregations": {
        "applicants_ifi": {
            "buckets": [{"key": "SIEMENS"}, {"key": "SIEMENS USA"}]
        }
    }
}

ifi_attachments = {
    "ucid_count": 2,
    "status": "success",
    "time": "0.152457",
    "attachments": [
        {
            "EP-0700000-A2": {
                "count": 7,
                "attachments": [
                    {
                        "path": "/EP/19960306/A2/000000/70/00/00/EP0700000A219960306.pdf",
                        "filename": "EP0700000A219960306.pdf",
                        "pkey": "EP-0700000-A2-19960306",
                        "media": "application/pdf",
                        "size": 710278
                    },
                ]
            }
        },
        {
            "EP-0700000-B1": {
                "count": 6,
                "attachments": [
                    {
                        "path": "/EP/20001004/B1/000000/70/00/00/00360001.tif",
                        "filename": "00360001.tif",
                        "media": "image/tiff",
                        "pkey": "EP-0700000-B1-20001004",
                        "size": 22886
                    },
                    {
                        "path": "/EP/20001004/B1/000000/80/01/01/005541.png",
                        "filename": "005541.png",
                        "media": "image/png",
                        "pkey": "EP-0800101-B1-20001004",
                        "size": 48741
                    },
                ]
            }
        }
    ],
}

ipc_results = {
    "took": 2,
    "timed_out": False,
    "_shards": {
        "total": 1,
        "successful": 1,
        "skipped": 0,
        "failed": 0
    },
    "hits": {
        "total": 3,
        "max_score": None,
        "hits": [
            {
                "_index": "ipc",
                "_id": "4",
                "_score": None,
                "_source": {
                    "hierarchy_id": 4,
                    "symbol": "A01B",
                    "end_symbol": None,
                    "entry_type": "K",
                    "sort_key": "A01B",
                    "classification_symbol": "A01B",
                    "kind": "u",
                    "edition": None,
                    "title": "Soil working in agriculture or forestry; Parts, details, "
                             "or accessories of agricultural machines or implements, in general",
                    "parent_id": 3,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    "A01B"
                ]
            },
            {
                "_index": "ipc",
                "_id": "6",
                "_score": None,
                "_source": {
                    "hierarchy_id": 6,
                    "symbol": "A01B0001000000",
                    "end_symbol": None,
                    "entry_type": "K",
                    "sort_key": "A01B1/00",
                    "classification_symbol": "A01B1/00",
                    "kind": "m",
                    "edition": [
                        "19680901",
                        "20060101"
                    ],
                    "title": "Hand tools",
                    "parent_id": 4,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    "A01B1/00"
                ]
            },
            {
                "_index": "ipc",
                "_id": "9683",
                "_score": None,
                "_source": {
                    "hierarchy_id": 9683,
                    "symbol": "B01B0001000000",
                    "end_symbol": None,
                    "entry_type": "K",
                    "sort_key": "B01B1/00",
                    "classification_symbol": "B01B1/00",
                    "kind": "m",
                    "edition": [
                        "19680901",
                        "19740701",
                        "20060101"
                    ],
                    "title": "Boiling; Boiling apparatus for physical or chemical purposes",
                    "parent_id": 9682,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    "B01B1/00"
                ]
            }
        ]
    }
}

ipc_single_result = {
    "took": 2,
    "timed_out": False,
    "_shards": {
        "total": 1,
        "successful": 1,
        "skipped": 0,
        "failed": 0
    },
    "hits": {
        "total": 3,
        "max_score": None,
        "hits": [
            {
                "_index": "ipc",
                "_id": "4",
                "_score": None,
                "_source": {
                    "hierarchy_id": 4,
                    "symbol": "A01B",
                    "end_symbol": None,
                    "entry_type": "K",
                    "sort_key": "A01B",
                    "classification_symbol": "A01B",
                    "kind": "u",
                    "edition": None,
                    "title": "Soil working in agriculture or forestry; Parts, details, "
                             "or accessories of agricultural machines or implements, in general",
                    "parent_id": None,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    "A01B"
                ]
            }
        ]
    }
}

cpc_results = {
    "took": 35,
    "timed_out": False,
    "_shards": {
        "total": 5,
        "successful": 5,
        "skipped": 0,
        "failed": 0
    },
    "hits": {
        "total": 3,
        "max_score": None,
        "hits": [
            {
                "_index": "cpc",
                "_id": "139985",
                "_score": None,
                "_source": {
                    "hierarchy_id": 139985,
                    "status": "published",
                    "classification_symbol": "F01B17/00",
                    "sort_key": "F01B17/00",
                    "level": 7,
                    "breakdown_code": False,
                    "date_revised": "2013-01-01",
                    "additional_only": False,
                    "not_allocatable": False,
                    "title": "Reciprocating-piston machines or engines characterised by use of uniflow principle",
                    "parent_id": 139835,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    7,
                    "F01B17/00"
                ]
            },
            {
                "_index": "cpc",
                "_id": "140051",
                "_score": None,
                "_source": {
                    "hierarchy_id": 140051,
                    "status": "published",
                    "classification_symbol": "F01B2170/00",
                    "sort_key": "F01B170/00",
                    "level": 7,
                    "breakdown_code": True,
                    "date_revised": "2013-01-01",
                    "additional_only": True,
                    "not_allocatable": False,
                    "title": "Steam engines, e.g. for locomotives or ships",
                    "parent_id": 140050,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    7,
                    "F01B170/00"
                ]
            },
            {
                "_index": "cpc",
                "_id": "139991",
                "_score": None,
                "_source": {
                    "hierarchy_id": 139991,
                    "status": "published",
                    "classification_symbol": "F01B19/00",
                    "sort_key": "F01B19/00",
                    "level": 7,
                    "breakdown_code": False,
                    "date_revised": "2013-01-01",
                    "additional_only": False,
                    "not_allocatable": False,
                    "title": "Positive-displacement machines or engines of flexible-wall type",
                    "parent_id": 139835,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    7,
                    "F01B19/00"
                ]
            }
        ]
    }
}

cpc_single_result = {
    "took": 35,
    "timed_out": False,
    "_shards": {
        "total": 5,
        "successful": 5,
        "skipped": 0,
        "failed": 0
    },
    "hits": {
        "total": 3,
        "max_score": None,
        "hits": [
            {
                "_index": "cpc",
                "_id": "139985",
                "_score": None,
                "_source": {
                    "hierarchy_id": 139985,
                    "status": "published",
                    "classification_symbol": "F01B17/00",
                    "sort_key": "F01B17/00",
                    "level": 7,
                    "breakdown_code": False,
                    "date_revised": "2013-01-01",
                    "additional_only": False,
                    "not_allocatable": False,
                    "title": "Reciprocating-piston machines or engines characterised by use of uniflow principle",
                    "parent_id": None,
                    "descriptions": [],
                    "ancestor_ids": [],
                    "child_ids": []
                },
                "sort": [
                    7,
                    "F01B17/00"
                ]
            }
        ]
    }
}

doc_info_results = {
    "took": 35,
    "timed_out": False,
    "_shards": {
        "total": 5,
        "successful": 5,
        "skipped": 0,
        "failed": 0
    },
    "hits": {
        "total": 2,
        "max_score": None,
        "hits": [
            {
                "_index": "doc_info",
                "_id": "AR-062171-A1",
                "found": True,
                "_score": None,
                "_source": {
                    "docdb_family_id": 12345,
                    "legal_status": "invalid",
                    "publn_date": "1999-10-21",
                    "publication_number": "US-2328353-B",
                    "events": [
                        {
                            "code": "FG",
                            "class": "F",
                            "impact": "+",
                            "date": "2015-07-30",
                            "effective_date": None,
                            "attributes": None
                        },
                        {
                            "code": "FD",
                            "class": "B",
                            "impact": "-",
                            "date": "2019-12-04",
                            "effective_date": None,
                            "attributes": None
                        }
                    ]
                }
            },
            {
                "_index": "doc_info",
                "_id": "AR-062367-A1",
                "_score": None,
                "_source": {
                    "docdb_family_id": 12345,
                    "legal_status": "valid",
                    "publication_type": "application",
                    "publn_date": "1999-10-21",
                    "application_date": "1999-04-14",
                    "application_number": "CA-2328353-A",
                    "publication_number": "CA-2328353-A",
                    "events": [
                        {
                            "code": "FG",
                            "class": "F",
                            "impact": "+",
                            "date": "2010-06-30",
                            "effective_date": None,
                            "attributes": None
                        }
                    ]
                }
            }
        ]
    }
}

app_info_results = {
    "took": 35,
    "timed_out": False,
    "_shards": {
        "total": 5,
        "successful": 5,
        "skipped": 0,
        "failed": 0
    },
    "hits": {
        "total": 2,
        "max_score": None,
        "hits": [{'_index': 'doc_info_v2',
                  '_id': 'CN-102257666-A',
                  '_score': 2.0,
                  '_source': {'application_date': '2009-12-09',
                              'application_number': 'CN-200980150669-A',
                              'docdb_family_id': 42287523},
                  'sort': [42287523]},
                 {'_index': 'doc_info_v2',
                  '_id': 'WO-2015145105-A1',
                  '_score': 2.0,
                  '_source': {'application_date': '2015-03-05',
                              'application_number': 'GB-2015050636-W',
                              'docdb_family_id': 50686832},
                  'sort': [50686832]},
                 {'_index': 'doc_info_v3',
                  '_id': 'EA-025303-B1',
                  '_score': 2.0,
                  '_source': {'application_date': '2010-10-19',
                              'application_number': 'EA-201290161-A',
                              'docdb_family_id': 41263486}},
                 {'_index': 'doc_info_v3',
                  '_id': 'EA-201290161-A1',
                  '_score': 2.0,
                  '_source': {'application_date': '2010-10-19',
                              'application_number': 'EA-201290161-A',
                              'docdb_family_id': 41263486}},
                 {'_index': 'doc_info_v3',
                  '_id': 'EA-025535-B1',
                  '_score': 2.0,
                  '_source': {'application_date': '2010-10-19',
                              'application_number': 'EA-201290160-A',
                              'docdb_family_id': 41263486}},
                 {'_index': 'doc_info_v3',
                  '_id': 'EA-201290160-A1',
                  '_score': 2.0,
                  '_source': {'application_date': '2010-10-19',
                              'application_number': 'EA-201290160-A',
                              'docdb_family_id': 41263486}}

                 ]
    }
}


def create_producer_response(offset, size):
    return [dict(docdb_family_id=offset + idx + 1, similarity_index=offset + idx + 1) for idx in range(size)]


def _get_source(hit, properties):
    source = dict()
    for k in properties:
        source[k] = hit + 1
    source[FAMILIES.es_field_name('owner_ids')] = []
    source[FAMILIES.es_field_name('publication_date')] = search_publication_date
    source[FAMILIES.es_field_name('priority_date')] = search_priority_date
    source[FAMILIES.es_field_name('also_published_as')] = search_patent_numbers
    source[FAMILIES.es_field_name('applicants')] = ['TEST']
    source[FAMILIES.es_field_name('authorities')] = ['EP']
    source[FAMILIES.es_field_name('inventors')] = []
    source[FAMILIES.es_field_name('raw_publication_number')] = search_patent_numbers[0]
    source[FAMILIES.es_field_name('tech_areas')] = ['Other Fields', 'Electrical Engineering']
    source[FAMILIES.es_field_name('ipc')] = ['G01R31/319', 'G06F11/26']
    source[FAMILIES.es_field_name('cpc')] = ['G01R31/319', 'G06F11/26']

    legal_statuses = {
        0: ['valid'],
        1: ['valid', 'unknown'],
        2: ['valid', 'invalid'],
        3: ['valid', 'invalid', 'unknown'],
        4: ['invalid'],
        5: ['invalid', 'unknown'],
        6: ['unknown'],
        7: None
    }

    source[FAMILIES.es_field_name('legal_status')] = legal_statuses.get(hit % len(legal_statuses))
    return source


def _get_npl_source(hit, properties):
    source = dict()
    for k in properties:
        source[k] = hit + 1
    source[PAPERS.es_field_name('abstract')] = ['TEST']
    return source


def get_es_search_result(page=1, found=True):
    properties = ['docdb_family_id']
    page_size = current_app.config['DEFAULT_PAGE_SIZE']
    hits = list()
    if page <= search_max_page:
        total = page_size * number_of_pages
        for hit in range(page_size * number_of_pages):
            hits.append(dict(_id=hit + 1, _score=random.uniform(0.0, 100.0), _source=_get_source(hit, properties),
                             found=found, highlight=dict(also_published_as=['EP-1-' + str(hit + 1)])))
        from_hit, to_hit = (page - 1) * page_size, page * page_size
        hits = hits[from_hit:to_hit]
    else:
        total = 0

    return {
        "hits": {
            "total": total,
            "hits": hits
        }
    }


def get_npl_search_result(page=1, found=True):
    properties = ['corpus_id']
    page_size = current_app.config['DEFAULT_PAGE_SIZE']
    hits = list()
    if page <= search_max_page:
        total = page_size * number_of_pages
        for hit in range(page_size * number_of_pages):
            hits.append(dict(_id=hit + 1, _score=random.uniform(0.0, 100.0),
                             _source=_get_npl_source(hit, properties), found=found))
    else:
        total = 0
    return {
        "hits": {
            "total": {
                "value": total,
                "relation": "eq"
            },
            "hits": hits
        }
    }


def get_cached_data(search_type, query=None, value=None, uid=None):
    value["results"] = [{**r, **_get_source(r["docdb_family_id"] - 1, [])} for r in value["results"]] if value else []
    return {
        "user_id": uid or user_id,
        "type": search_type.value,
        "query": query or {},
        "value": value or {}
    }


def as_multiget_result(es_result):
    return {"docs": es_result["hits"]["hits"]}


def get_neo4j_nodes(size):
    return [{"text": "Lorem ipsum"} for _ in range(size)]


def get_single_full_doc_from_es(found=False):
    docs = []
    if found:
        docs.append({'_source': _get_source(1, FAMILIES.all_field_names())})
    return {
        'hits': {
            'hits': docs
        }
    }


def get_citation_es_results(total, offset=0):
    docs = list()
    properties = ['also_published_as', 'docdb_family_id']
    for hit in range(offset, total + offset):
        doc = {
            '_id': str(1000 + hit + 1),
            '_source': _get_source(1000 + hit, properties),
            'highlight': {
                'also_published_as': ['EP-1-' + str(hit + 1)]
            }
        }
        doc['_source'][FAMILIES.es_field_name('raw_publication_number')] = 'EP-1-' + str(hit + 1)
        docs.append(doc)

    return {
        'hits': {
            'hits': docs,
            'total': len(docs)
        }
    }


def form_empty_docs(docs_ids):
    formed_docs = list()
    for idx in docs_ids:
        formed_docs.append({
            'document': {
                'general': {
                    'docdb_family_id': idx
                }
            }
        })
    return formed_docs


def get_json_file(file_name: str):
    file_name = os.path.join(os.path.dirname(os.path.realpath(__file__)), "mocks", file_name)
    with open(file_name) as f:
        return json.load(f)


class SearchSemanticResultMock:
    def __init__(self):
        self.user_id = search_user_id
        self.search_input = search_text
        self.max_results = 1000
        self.patent_numbers = search_patent_numbers


class SearchBooleanResultMock:
    def __init__(self):
        self.user_id = search_user_id
        self.search_input = search_text


class SearchCitationResultMock:
    def __init__(self):
        self.user_id = search_user_id
        self.patent_numbers = search_patent_numbers
        self.first_level_direction = search_citation_direction

    def serialize(self):
        return self
