# Third party imports
import pytest
from marshmallow import ValidationError

# Standard lib imports
from http import HTTPStatus

# Local imports
import tests.unit_tests.search_api.url as endpoints
from octimine_common.tests.data import access_token, refresh_token
from tests.unit_tests.search_api.assertions import assert_status

# Token tests

token_get_endpoints = [
    # Document Search
    (endpoints.DOCUMENT_SEARCH_URL + '/1', 'get'),
    (endpoints.TERM_SEARCH_URL + '?field=applicants&value=abc', 'get'),
]

token_post_body_endpoints = [
    (endpoints.BOOLEAN_SEARCH_URL, 'post'),
    (endpoints.SEMANTIC_SEARCH_URL, 'post'),
    (endpoints.CITATION_SEARCH_URL, 'post'),
    (endpoints.PATENT_NUMBER_SEARCH_URL, 'post'),
    (endpoints.NODE_SEARCH_URL, 'post'),
]

token_endpoints = token_get_endpoints + token_post_body_endpoints


@pytest.mark.parametrize('url, method', token_endpoints)
def test_missing_token(client, url, method):
    method = getattr(client, method)
    rv = method(url)
    assert_status(rv, HTTPStatus.UNAUTHORIZED)


@pytest.mark.parametrize('url, method', token_endpoints)
def test_wrong_token(client, url, method):
    wrong_token = refresh_token
    headers = dict(Authorization='Bearer %s' % wrong_token)
    method = getattr(client, method)
    rv = method(url, headers=headers)
    assert_status(rv, HTTPStatus.UNAUTHORIZED)


# Body tests
required_body_endpoints = [list(tup)[0] for tup in token_post_body_endpoints]


@pytest.mark.parametrize('url', required_body_endpoints)
def test_no_body(client, mocker, url):
    mocker.patch('flask_marshmallow.Schema.load', side_effect=ValidationError(""))
    headers = dict(Authorization="Bearer %s" % access_token)
    rv = client.post(url, headers=headers)
    assert_status(rv, HTTPStatus.BAD_REQUEST)


@pytest.mark.parametrize('url', required_body_endpoints)
def test_wrong_body(client, url):
    headers = dict(Authorization="Bearer %s" % access_token)
    body = {
        "field": "nonsense"
    }
    rv = client.post(url, headers=headers, json=body)
    assert_status(rv, HTTPStatus.BAD_REQUEST)
