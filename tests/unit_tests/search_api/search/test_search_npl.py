# Third party imports
import pytest

# Standard lib imports
from http import HTTPStatus


from app.models.npl import NplSearchSchema
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.data import NPL_QUERY_PATH
from tests.unit_tests.search_api.mocks.consumer import ConsumerMock
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.url import NPL_SEARCH_URL as SEARCH_URL

search_body = {
    "search_input": data.search_text
}

# Error params
publish_error_mock = ConsumerMock(publish_error=True)
consumer_error_mock = ConsumerMock(producer_error=True)
error_params = [(SEARCH_URL, search_body, publish_error_mock),
                (SEARCH_URL, search_body, consumer_error_mock)]


class TestNpl:
    URL = SEARCH_URL

    @pytest.mark.parametrize('url, body, consumer_mock', error_params)
    def test_npl_search_basic_publish_error(self, client, mocker, url, body, consumer_mock):
        mocker.patch(NPL_QUERY_PATH, return_value=consumer_mock)
        mocker.patch('app.services.word_embeddings.embeddings.calculate_embeddings_vector', return_value=[])
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(url, headers=headers, json=body)
        assert_status(rv, HTTPStatus.INTERNAL_SERVER_ERROR)

    @pytest.mark.parametrize('page', [1, 2, None])
    def test_npl_search_success_full_query(self, client, mocker, page):
        npl_result = data.get_npl_search_result(page=page or 1)
        mocker.patch(NPL_QUERY_PATH, return_value=npl_result)
        mocker.patch('app.services.word_embeddings.embeddings.calculate_embeddings_vector', return_value=[])
        headers = dict(Authorization='Bearer %s' % access_token)
        url = self.URL
        if page:
            url += '?page=%s' % page
        rv = client.post(url, headers=headers, json=search_body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = ((page or 1) - 1) * response_data['data']['page']['page_size']
        for idx, paper in enumerate(response_data['data']['papers']):
            assert paper['corpus_id'] == offset + idx + 1

    def test_npl_search_full_query_nonexistent_page(self, client, mocker):
        page = 10
        npl_result = data.get_npl_search_result(page=page or 1)
        mocker.patch(NPL_QUERY_PATH, return_value=npl_result)
        mocker.patch('app.services.word_embeddings.embeddings.calculate_embeddings_vector', return_value=[])
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '?page=' + str(page), headers=headers, json=search_body)
        assert_status(rv, HTTPStatus.OK)
        assert len(rv.get_json()['data']['papers']) == 0


class TestNplSearchSchemaValidation:

    @pytest.mark.parametrize('query_a, query_b', [
        (
                {'search_input': 'test', 'cache': True},
                {
                    'search_input': ' test ',
                    'cache': True
                }
        )
    ])
    def test_search_hash_should_change(self, query_a, query_b):
        fake_user_id = 1
        hash_a = NplSearchSchema.get_search_hash(fake_user_id, query_a)
        hash_b = NplSearchSchema.get_search_hash(fake_user_id, query_b)
        assert hash_a != hash_b
