import pytest
from datetime import date

from octimine_common.enums import ESDocumentType

from app.models.filter_evaluator import SearchFilterEvaluator


class TestSearchFiltersEvaluation:

    DATA = [
        {
            "priority_date": "2012-05-10",
            "applicants_ifi": ["A"],
            "title": "A",
            "cpc": ["A"],
            "similarity_index": 500,
            "authorities": ["DE", "EP"],
            "legal_statuses": ["granted"],
            "tech_fields": ["ABC", "Xyz", "qwr"],
        },
        {
            "priority_date": "2012-05-08",
            "applicants_ifi": ["Y", "B"],
            "title": "D",
            "similarity_index": 480,
            "legal_statuses": ["unknown"],
            "tech_fields": ["abc"],
        },
        {
            "priority_date": "1988-07-01",
            "applicants_ifi": ["AB", "B", "C"],
            "title": "B",
            "similarity_index": 100,
            "authorities": ["DE", "US"],
            "legal_statuses": ["active_reinstated", "unknown"],
            "tech_fields": ["aBC", "xYZ", "qwr"],
        },
        {
            "priority_date": "2004-05-10",
            "applicants_ifi": ["D", "B", "C"],
            "title": "C",
            "similarity_index": 50,
            "authorities": ["CN"],
            "legal_statuses": ["expired_fee_related"],
            "tech_fields": ["a", "x", "q"],
        },
        {
            "priority_date": None,
            "applicants_ifi": None,
            "title": "Z",
            "similarity_index": None,
            "authorities": None,
            "tech_fields": ["BC", "xZ", "qwr"],
        },
        {
            "priority_date": "2000-03-25",
            "applicants_ifi": ["Z"],
            "title": None,
            "authorities": ["WO", "US"],
            "legal_statuses": ["expired", "unknown"],
            "tech_fields": ["aBC", "xZ", "q"],
        },
        {
            "priority_date": None,
            "applicants_ifi": None,
            "title": "",
            "similarity_index": 300,
            "authorities": ["CN", "US"],
            "legal_statuses": ["pending", "withdrawn", "unknown"],
            "tech_fields": [],
        },
    ]

    @pytest.mark.parametrize('search_filters, expected_results_count', [
        ({
            'earliest_priority_date': date(2004, 5, 8),
            'latest_priority_date': date(2012, 5, 10)
        }, 3),
        ({
            'applicants_plus': ['A']
        }, 2),
        ({
             'applicants_plus': ['a']  # Case sensitivity should not matter here
         }, 2),
        ({
             'applicants_minus': ['A']
        }, 5),
        ({
             'applicants_minus': ['a']  # Case sensitivity should not matter here
         }, 5),
        ({
            'similarity_index': 350
        }, 2),
        ({
            'min_applicant_count': 2
        }, 3),
        ({
             'top_applicants': 2
         }, 3),
        ({
             'similarity_cut_off': 350
         }, 2),
        ({
             'quantity_cut_off': 3
         }, 3),
        ({
             'authorities_minus': ["CN"]
         }, 6),
        ({
             'authorities_minus': ["CN", "US"]
         }, 6),
        ({
             'authorities_minus': ["US", "CN"]  # Order should not matter
         }, 6),
        ({
             'authorities_plus': ["CN", "US"]
         }, 4),
        ({
             'authorities_plus': ["de"]  # case sensitivity should not matter here
         }, 2),
        ({
             'legal_status': ["valid"]
         }, 3),
        ({
             'legal_status': ["invalid"]
         }, 2),
        ({
             'legal_status': ["unknown"]
         }, 3),
        ({
             'legal_status': ["valid", "unknown"]
         }, 6),
        ({
             'free_text_query': "TECH_FIELDS=ABC"
         }, 4),
        ({
             'free_text_query': "TECH_FIELDS=Xyz"
         }, 2),
        ({
             'free_text_query': "TECH_FIELDS=a"
         }, 1),
        ({
             'free_text_query': "TECH_FIELDS=BC"
         }, 1),
    ])
    def test_search_filters_evaluation(self, flask_boolean_parser, search_filters, expected_results_count):
        evaluator = SearchFilterEvaluator(self.DATA, search_filters, flask_boolean_parser.parse, ESDocumentType.FAMILY)
        results = evaluator.apply_filters()
        assert len(results) == expected_results_count
