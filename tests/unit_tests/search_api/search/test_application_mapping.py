# Standard lib imports
from http import HTTPStatus
import json
import pytest

# Local imports
from tests.unit_tests.search_api.assertions import assert_status
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.url import APPLICATION_MAPPING_URL
from tests.unit_tests.search_api.data import app_info_results

from app.extensions import elastic_search
from app.ext.application_number_reformat.mapping_utils import get_full_year


class TestApplicationMapping:
    URL = APPLICATION_MAPPING_URL

    def test_application_mapping_invalid_request(self, client):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(documents_ids="Nonsense")
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_application_mapping_with_invalid_application_info(self, client):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {'application_numbers': ['200980150669'],
                'application_country_code': ['CN', 'DE', 'PC', 'PC'],
                'application_dates': ['2009-12-09', '2001-01-01', ],
                'application_ip_origin': ['national']}

        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_application_mapping_success(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        mocker.patch.object(elastic_search.client, 'search', side_effect=[app_info_results, {'hits': {'hits': []}}])
        body = {'application_numbers': ['200980150669', '12345', 'PCT/GB2015/050636', 'PCT/GB2015/050636'],
                'application_country_code': ['CN', 'DE', 'PC', 'PC'],
                'application_dates': ['2009-12-09', '2001-01-01', '2015-03-05', '2015-03-05'],
                'application_ip_origin': ['national', 'non-existing-origin', 'national', 'national']}

        rv = client.post(self.URL, headers=headers, data=json.dumps(body)).get_json()

        assert (rv['docdb_family_id'] == [42287523, None, 50686832, 50686832])
        assert (rv['publication_number'] == ['CN-102257666-A', None, 'WO-2015145105-A1', 'WO-2015145105-A1'])
        assert (rv['hits'] == 3)

    def test_input_with_same_docdb_family_id(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        mocker.patch.object(elastic_search.client, 'search', side_effect=[app_info_results, {'hits': {'hits': []}}])
        body = {'application_country_code': ['EA', 'EA'],
                'application_dates': ['2010-10-19', '2010-10-19'],
                'application_ip_origin': ['Eur-asia', 'Eur-asia'],
                'application_numbers': ['201290161', '201290160']}

        rv = client.post(self.URL, headers=headers, data=json.dumps(body)).get_json()

        assert (rv['docdb_family_id'] == [41263486, 41263486])

    def test_one_app_number_matches_two_docs(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        two_hits = {
            "hits": {
                "total": {
                    "value": 2,
                    "relation": "eq"
                },
                "hits": [
                    {
                        "_id": "WO-2013111588-A1",
                        "_score": 2.0,
                        "_source": {
                            "docdb_family_id": 48873315,
                            "application_date": "2013-01-24",
                            "application_number": "JP-2013000336-W",
                            "publication_number": "WO-2013111588-A1"
                        }
                    },
                    {
                        "_id": "**********-U",
                        "_score": 3.0,
                        "_source": {
                            "application_number": "JP-2013000336-U",
                            "docdb_family_id": 47603246,
                            "application_date": "2013-01-24",
                            "publication_number": "**********-U"
                        }
                    },
                ]
            }
        }
        mocker.patch.object(elastic_search.client, 'search', side_effect=[two_hits, {'hits': {'hits': []}}])
        body = {
                "application_numbers": ["2013-000336"],
                "application_country_code": ["JP"],
                "application_dates": ["2013-01-24"],
                "application_ip_origin": ["national"]
        }
        rv = client.post(self.URL, headers=headers, data=json.dumps(body)).get_json()
        # Make sure Japanese document is picked up, not the WO one
        assert (rv['docdb_family_id'] == [47603246])
        assert (rv['publication_number'] == ['**********-U'])

    @pytest.mark.parametrize('last_2_digits, application_date_year, expected_full_year', [
        ('21', '2021', '2021'),  # Should be same as application year
        ('21', '2019', '2021'),  # Even though they are different, century should match
        ('99', '2001', '1999'),  # Also handle edge case when dates happen to be in different centuries
        ('91', '2005', '1991'),
    ])
    def test_get_full_year(self, last_2_digits, application_date_year, expected_full_year):
        full_year = get_full_year(last_2_digits, application_date_year)
        assert full_year == expected_full_year
