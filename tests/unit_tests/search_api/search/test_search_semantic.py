# Third party imports
import json
import pytest

# Standard lib imports
from http import HTT<PERSON>tatus
from copy import deepcopy

# Local imports
from deepl import TextResult, DeepLException
from marshmallow import ValidationError

from app.models.semantic import SemanticSearchSchema
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.data import PATENT_NUMBER_PATH, DOC_ID_PATH, DOC_INFO_PATH
from tests.unit_tests.search_api.mocks.consumer import ConsumerMock
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.url import SEMANTIC_SEARCH_URL as SEARCH_URL


perform_search = 'app.ext.semantic_search.semantic_search_extension.Consumer'

documents = [1]
publish_error_mock = ConsumerMock(publish_error=True)
consumer_error_mock = ConsumerMock(producer_error=True)

# Error params
search_body = {
    "search_input": data.search_text,
    "patent_numbers": data.search_patent_numbers,
    "search_filters": data.search_filters,
}

snapshot_body = deepcopy(search_body)
snapshot_body['patent_documents_ids'] = documents
error_params = [(SEARCH_URL, search_body, publish_error_mock),
                (SEARCH_URL, search_body, consumer_error_mock),
                ]


class TestSemantic:
    URL = SEARCH_URL

    @pytest.mark.usefixtures("blacklisted_token")
    def test_semantic_search_fails_for_blacklisted_token(self, client):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL, headers=headers, json=search_body)
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    @pytest.mark.parametrize('url, body, consumer_mock', error_params)
    def test_semantic_search_basic_publish_error(self, client, mocker, url, body,
                                                 consumer_mock):
        mocker.patch(perform_search, return_value=consumer_mock)
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result())
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(url, headers=headers, json=body)
        assert_status(rv, HTTPStatus.INTERNAL_SERVER_ERROR)

    @pytest.mark.parametrize('page, sort_by', [(1, None), (2, 'priority_date'), (None, None)])
    def test_semantic_search_success_full_query(self, client, mocker, page, sort_by):
        es_result = data.get_es_search_result(page=page or 1)
        mocker.patch(perform_search, return_value=ConsumerMock(result_size=10))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        headers = dict(Authorization='Bearer %s' % access_token)
        url = self.URL + '?show_analytics=1&show_general=1'
        if page:
            url += '&page=%s' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=search_body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = ((page or 1) - 1) * response_data['data']['page']['page_size']
        for idx, doc in enumerate(response_data['data']['documents']):
            assert doc['general']['docdb_family_id'] == offset + idx + 1

    def test_semantic_search_success_with_applicant_alias(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        mocker.patch(perform_search, return_value=ConsumerMock(result_size=10))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        headers = dict(Authorization='Bearer %s' % access_token)
        url = self.URL + '?show_analytics=1&show_general=1'
        body = {
            **search_body,
            "applicant_aliases": [{"applicant": "TEST", "alias": "TEST alias"}]
        }
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        for idx, doc in enumerate(response_data['data']['documents']):
            assert "TEST alias" in doc['bibliographic']['applicants']

    def test_semantic_search_full_query_nonexistent_page(self, client, mocker):
        page = 10
        es_result = data.get_es_search_result(page=page)
        mocker.patch(perform_search, return_value=ConsumerMock())
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '?page=' + str(page), headers=headers, json=search_body)
        assert_status(rv, HTTPStatus.OK)
        assert len(rv.get_json()['data']['documents']) == 0

    def test_semantic_search_main_areas(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        mock_consumer = ConsumerMock(result_size=10)
        mocker.patch(perform_search, return_value=mock_consumer)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        url = self.URL + '?show_analytics=1&show_general=1'
        rv = client.post(url, headers=headers, json={"patent_numbers": ["EP216554A1"]})
        assert_status(rv, HTTPStatus.OK)
        assert json.loads(mock_consumer.message)['ma1']
        assert not json.loads(mock_consumer.message)['ma2']
        assert not json.loads(mock_consumer.message)['ma3']
        assert not json.loads(mock_consumer.message)['ma4']
        assert json.loads(mock_consumer.message)['ma5']

    @pytest.mark.parametrize('query, result, status_code', [
        ({"search_input": "Test test", "translation": {"source_language": "EN"}}, None, HTTPStatus.OK),
        ({"search_input": "Test test", "translation": {"source_language": "EN-GB"}}, None, HTTPStatus.OK),
        ({"search_input": "Test test", "translation": None}, None, HTTPStatus.OK),
        ({"search_input": "Test test"}, TextResult(text="Test test", detected_source_lang="DE", billed_characters=14),
         HTTPStatus.OK),
        ({"search_input": "Test test", "translation": {"source_language": "DE"}},
         TextResult(text="Test test", detected_source_lang="DE", billed_characters=10), HTTPStatus.OK),
        ({"search_input": "Test test", "translation": {"source_language": "DE"}}, DeepLException("Error"),
         HTTPStatus.BAD_REQUEST),
        ({"patent_numbers": ["EP5454"]}, DeepLException("Error"), HTTPStatus.OK),
        ({"search_input": "Test test"}, DeepLException("Error"), HTTPStatus.OK),
        ({"search_input": "Test test", "translation": None}, DeepLException("Error"), HTTPStatus.OK),
    ])
    def test_semantic_search_translation(self, client, mocker, query, result, status_code):
        es_result = data.get_es_search_result(page=1)
        mock_consumer = ConsumerMock(result_size=10)
        mocker.patch(perform_search, return_value=mock_consumer)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        mocker.patch('deepl.Translator.translate_text', side_effect=[result])
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL, headers=headers, json=query)
        assert_status(rv, status_code)


class TestSemanticSearchSchemaValidation:

    @staticmethod
    def _get_doc_ids(search_hash: str) -> list[int]:
        return {'b1': [1, 2], 'b2': [3, 4]}.get(search_hash, [])

    @pytest.mark.parametrize('query_a, query_b', [
        (
                {'search_input': 'test'},
                {
                    'search_input': 'test',
                    'applicant_aliases': [{'applicant': "Test", 'alias': 'TEST'}]
                }
        ),
        (
                {
                    'search_input': 'test',
                    'search_filters': {
                        'relevant_search_hash': "b1"
                    }
                },
                {
                    'search_input': 'test',
                    'search_filters': {
                        'relevant_search_hash': "b2"
                    }
                }
        ),
        (
                {
                    'search_input': 'test',
                    'search_filters': {
                        'relevant_docdb_family_ids': [1, 2]
                    }
                },
                {
                    'search_input': 'test',
                    'search_filters': {
                        'relevant_docdb_family_ids': [3, 4]
                    }
                }
        )
    ])
    def test_search_hash_should_change(self, query_a, query_b):
        fake_user_id = 1
        query_a = SemanticSearchSchema(get_doc_ids_by_hash=self._get_doc_ids).load(query_a)
        query_b = SemanticSearchSchema(get_doc_ids_by_hash=self._get_doc_ids).load(query_b)
        hash_a = SemanticSearchSchema.get_search_hash(fake_user_id, query_a)
        hash_b = SemanticSearchSchema.get_search_hash(fake_user_id, query_b)
        assert hash_a != hash_b

    def test_should_ignore_relevant_search_hash_when_relevant_docdb_family_ids_present(self):
        query = SemanticSearchSchema(get_doc_ids_by_hash=self._get_doc_ids).load({
            'search_input': 'test',
            'search_filters': {
                'relevant_docdb_family_ids': [1, 3],
                'relevant_search_hash': "b1"
            }
        })
        assert 'relevant_search_hash' not in query['search_filters']
        assert query['search_filters']['relevant_docdb_family_ids'] == [1, 3]

    @pytest.mark.parametrize('payload', [
        {
            'search_input': 'test',
            'search_filters': {'relevant_docdb_family_ids': [1, 3]},
            'include_term_weights': True
        },
        {
            'search_input': 'test',
            'include_term_weights': True
        },
        {
            'search_input': 'test',
            'include_term_weights': True,
            'search_filters': {'relevant_docdb_family_ids': None},
        }
    ])
    def test_should_require_single_relevant_docdb_family_ids_when_term_weights_requested(self, payload):
        with pytest.raises(ValidationError):
            SemanticSearchSchema(get_doc_ids_by_hash=self._get_doc_ids).load(payload)
