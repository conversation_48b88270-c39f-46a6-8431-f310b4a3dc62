from http import HTTPStatus
import json
import random
import pytest

from app.models.document_id import DocumentIdSearchSchema
from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.assertions import assert_status
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.data import DOC_ID_PATH, DOC_INFO_PATH
from tests.unit_tests.search_api.url import DOCUMENT_SEARCH_URL as SEARCH_URL


class TestDocumentId:
    URL = SEARCH_URL

    def test_document_search_invalid_request(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(documents_ids="Nonsense")
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_document_search_with_invalid_additional_info(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(
            documents_ids=data.search_document_ids,
            additional_info={
                "similarity_index": [random.randint(0, 100) for i, _ in enumerate(data.search_document_ids) if i % 2]
            }
        )
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def _test_document_success(self, client, mocker, after_url, es_results,
                               additional_info=None):
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_results))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(documents_ids=data.search_document_ids)
        if additional_info:
            body["additional_info"] = {
                "similarity_index": [additional_info.get(doc_id) for doc_id in data.search_document_ids],
                "rank": [additional_info.get(doc_id) for doc_id in data.search_document_ids]
            }
        rv = client.post(self.URL + after_url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        return response_data

    def test_document_success_empty_results(self, client, mocker):
        response_data = self._test_document_success(client, mocker, '',
                                                    data.es_result_empty)
        assert len(response_data['data']['documents']) == 0

    def test_document_search_success(self, client, mocker):
        response_data = self._test_document_success(client, mocker,
                                                    '?show_general=1',
                                                    data.get_es_search_result())
        for idx, doc in enumerate(response_data['data']['documents']):
            assert doc['general']['docdb_family_id'] == idx + 1
            assert 'analytics' not in doc

    def test_document_search_success_with_applicant_alias(self, client, mocker):
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result()))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "documents_ids": data.search_document_ids,
            "applicant_aliases": [{"applicant": "TEST", "alias": "test alias for doc"}]
        }
        rv = client.post(self.URL + '?show_general=1', headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        for idx, doc in enumerate(response_data['data']['documents']):
            assert doc['bibliographic']['applicants'][0] == "test alias for doc"

    def test_document_search_with_additional_info_success(self, client, mocker):
        additional_info = {doc_id: random.randint(1, 100) for doc_id in data.search_document_ids}
        response_data = self._test_document_success(client, mocker,
                                                    '?show_general=1',
                                                    data.get_es_search_result(), additional_info)
        for doc in response_data['data']['documents']:
            doc_id = doc['general']['docdb_family_id']
            if additional_info.get(doc_id):
                assert 'similarity_index' in doc['general']
                assert doc['general']['similarity_index'] == additional_info.get(doc_id)
                assert 'rank' in doc['general']
                assert doc['general']['rank'] == additional_info.get(doc_id)

    def test_document_search_extra_params(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(documents_ids=data.search_document_ids)
        rv = client.post(self.URL + "?show_general=1&show_bibliographic=1&show_analytics=1&page=1&"
                                    "sort_by=publication_date&sort_order=desc", headers=headers,
                         data=json.dumps(body))
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        for doc in response_data['data']['documents']:
            assert 'abstract' in doc['bibliographic']
            assert 'impact' in doc['analytics']
            assert 'publication_number' in doc['general']

    def test_document_search_with_fulltext_filters(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        filter_doc_ids = mocker.patch('app.services.search.helpers.documents.elastic_search.filter_document_ids',
                                      return_value={data.search_document_ids[0]})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "documents_ids": data.search_document_ids,
            "search_filters": {
                "free_text_query": "CLAIMS=test"
            }
        }
        rv = client.post(self.URL, headers=headers, json=body)
        assert_status(rv, HTTPStatus.OK)
        filter_doc_ids.assert_called()
        response_data = rv.get_json()
        assert len(response_data["data"]["documents"]) == 1


class TestDocumentIdSingle:
    URL = SEARCH_URL

    def test_document_single_not_found(self, client, mocker):
        mocker.patch(DOC_ID_PATH,
                     return_value=data.as_multiget_result(data.get_single_full_doc_from_es(found=False)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(self.URL + '/1', headers=headers)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    def test_document_single_success(self, client, mocker):
        mocker.patch(DOC_INFO_PATH, return_value={'docs': [r for r in data.doc_info_results['hits']['hits']]})
        mocker.patch(DOC_ID_PATH,
                     return_value=data.as_multiget_result(data.get_single_full_doc_from_es(found=True)))
        mocker.patch('app.services.search.functions.elastic_search.get_documents_info',
                     return_value=data.doc_info_results)
        mocker.patch('app.services.search.functions.elastic_search.get_corporate_entities',
                     return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(self.URL + '/1', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        document = rv.get_json()['data']['document']
        assert document['analytics']
        assert document['bibliographic']
        assert document['general']
        assert document['fulltext']['claims']
        assert document['fulltext']['description']
        assert document['general']['raw_publication_number'] == 'EP2049363A2'  # Make sure hyphens have been removed


class TestDocumentIdSearchSchemaValidation:

    @pytest.mark.parametrize('query_a, query_b', [
        (
                {'documents_ids': [1234, 3245, 564325], 'cache': True},
                {
                    'documents_ids': [1234, 3245, 564325],
                    'applicant_aliases': [{'applicant': "Test", 'alias': 'TEST'}],
                    'cache': True
                }
        )
    ])
    def test_search_hash_should_change(self, query_a, query_b):
        fake_user_id = 1
        hash_a = DocumentIdSearchSchema.get_search_hash(fake_user_id, query_a)
        hash_b = DocumentIdSearchSchema.get_search_hash(fake_user_id, query_b)
        assert hash_a != hash_b
