from app.models.sorter import SortOrder, DocumentsSorter


class TestSorting:

    DATA = [
        {"priority_date": "2012-05-10", "applicants": ["A"], "title": "a"},
        {"priority_date": "2012-05-08", "applicants": ["Y", "B"], "title": " D"},
        {"priority_date": "1988-07-01", "applicants": ["A", "B", "C"], "title": "B"},
        {"priority_date": "2004-05-10", "applicants": ["D", "B", "C"], "title": "\"C\""},
        {"priority_date": None, "applicants": None, "title": "Z"},
        {"priority_date": "2000-03-25", "applicants": ["Z"], "title": None},
        {"priority_date": None, "applicants": None, "title": ""},
    ]

    def test_sort_by_applicants_ascending(self):
        sorter = DocumentsSorter("applicants", SortOrder.ASCENDING)
        results = sorter.sort(self.DATA)
        assert results[0]["applicants"] == ["A"]
        assert self._get_last_non_empty_value("applicants", results) == ["Z"]
        assert results[-1]["applicants"] is None

    def test_sort_by_priority_date_descending(self):
        sorter = DocumentsSorter("priority_date", SortOrder.DESCENDING)
        results = sorter.sort(self.DATA)
        assert results[0]["priority_date"] == "2012-05-10"
        assert self._get_last_non_empty_value("priority_date", results) == "1988-07-01"
        assert results[-1]["priority_date"] is None

    def test_sort_by_title_descending(self):
        sorter = DocumentsSorter("title", SortOrder.DESCENDING)
        results = sorter.sort(self.DATA)
        assert results[0]["title"] == "Z"
        assert self._get_last_non_empty_value("title", results) == "a"
        assert results[-1]["title"] in (None, "")

    def test_sort_by_title_characters(self):
        sorter = DocumentsSorter("title", SortOrder.DESCENDING)
        results = sorter.sort(self.DATA)
        assert results[0]["title"] == "Z"
        assert self._get_last_non_empty_value("title", results) == "a"
        assert results[-1]["title"] in (None, "")

    @staticmethod
    def _get_last_non_empty_value(field, results):
        for r in reversed(results):
            if r[field] is not None and r[field] != "":
                return r[field]
