# Standard lib imports
import urllib
from http import HTTPStatus
import json

# Third party lib imports
import pytest

# Local imports
from octimine_common.enums import SearchType
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.url import SEARCH_HASH_URL as SEARCH_URL


class TestSearchHash:
    URL = SEARCH_URL

    def test_semantic_hash(self, client, mocker):
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                                 {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                             ]})
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '/shash', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.json['data']['documents']
        assert 'similarity_index' in rv.json['data']['documents'][0]['general']

    def test_filters_post(self, client, mocker):
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                                 {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                             ]})
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        filters = {"similarity_index": 3}
        rv = client.post(self.URL + '/shash', headers=headers, json={"search_filters": filters})
        assert_status(rv, HTTPStatus.OK)
        assert rv.json['data']['documents']

    def test_invalid_filters(self, client, mocker):
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                                 {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                             ]})
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        filters = {"earliest_publication_date": "2010-01-31", "blergh": "SIEMENS"}
        rv = client.post(self.URL + '/shash', headers=headers, json={"search_filters": filters})
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_filters_get(self, client, mocker):
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                                 {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                             ]})
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        filters = "APPLICANTS=Test"  # TODO: More complex filters when fixed
        rv = client.get(self.URL + '/shash?filters=' + urllib.parse.quote_plus(filters), headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.json['data']['documents']

    def test_boolean_hash(self, client, mocker):
        boolean_data = {"user_id": 1, "type": SearchType.BOOLEAN.value, "value": {"results": [
                            {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                            {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                        ], "hits": 250}}
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(boolean_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '/bhash', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.json['data']['documents']

    def test_citation_hash(self, client, mocker):
        citations = {'1001': {'1002': []}}
        citation_data = data.get_cached_data(
            search_type=SearchType.CITATION,
            value={"results": [{"docdb_family_id": 1001}], "citations": citations}
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(citation_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '/chash', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.json['data']['documents']

    def test_patent_number_hash(self, app, client, mocker):
        number_data = {"user_id": 1, "type": SearchType.PATENT_NUMBER.value, "value": {"results": [
                            {"docdb_family_id": 1, "rank": 1, "original_number": "EP123",
                             "original_number_normalized": "EP-123-A1"},
                            {"docdb_family_id": 2, "rank": 2, "original_number": "JP878645"}
                        ], "hits": 2}}
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(number_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '/phash', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.json['data']['documents']
        assert 'original_number' in rv.json['data']['documents'][0]['general']
        assert 'original_number_normalized' in rv.json['data']['documents'][0]['general']

    @pytest.mark.parametrize('query, value', [
        (
            {"documents_ids": [1, 2, 3]},
            [
                {"docdb_family_id": 1},
                {"docdb_family_id": 2},
                {"docdb_family_id": 3}
            ]
        ),
        (
            {
                "documents_ids": [1, 2, 3],
                "additional_info": {
                    "similarity_index": [1, 2, 3]
                }
            },
            [
                {"docdb_family_id": 1, "similarity_index": 1},
                {"docdb_family_id": 2, "similarity_index": 2},
                {"docdb_family_id": 3, "similarity_index": 3}
            ]
        )
    ])
    def test_document_id_hash(self, client, mocker, query, value):
        cached_data = data.get_cached_data(
            search_type=SearchType.DOCUMENT_ID,
            value={'results': value}
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(cached_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '/dhash', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert rv.json['data']['documents']

    def test_another_user(self, client, mocker):
        semantic_data = data.get_cached_data(
            search_type=SearchType.SEMANTIC,
            value={'results': [{"docdb_family_id": 1, "similarity_index": 2}]},
            uid=2
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '/shash', headers=headers)
        assert_status(rv, HTTPStatus.GONE)

    def test_no_cache(self, client, mocker):
        mocker.patch('redis.StrictRedis.get', return_value=None)
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL + '/shash', headers=headers)
        assert_status(rv, HTTPStatus.GONE)

    def test_unauthorized(self, client, mocker):
        semantic_data = {"user_id": 1, "type": SearchType.SEMANTIC.value,
                         "value": [{"docdb_family_id": 1, "similarity_index": 2}]}
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        rv = client.post(self.URL + '/shash')
        assert_status(rv, HTTPStatus.UNAUTHORIZED)
