from http import HTTPStatus

import pytest
from octimine_common.tests.data import access_token

from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import DOC_HIGHLIGHT_PATH, GET_CACHED_SEARCH_PATH
from tests.unit_tests.search_api.url import DOCUMENT_HIGHLIGHT_URL

family_id_request_body_boolean_search = {
    'search_hash': 'b1234',
    'document_id': 123
}

publication_number_request_body_boolean_search = {
    'search_hash': 'b1234',
    'publication_number': 'WO2010075343A2'
}

family_id_request_body_other_searches = {
    'search_hash': 's1234',
    'document_id': 123,
    'boolean_query': 'TITLE=test'
}

publication_number_request_body_other_searches = {
    'search_hash': 's1234',
    'publication_number': 'WO2010075343A2',
    'boolean_query': 'TITLE=test'
}

family_id_cached_boolean_search = {
    "value": {
        "results": [{'docdb_family_id': 123}]
    },
    'query': {'parsed_query': 'TITLE=test'}
}

publication_number_cached_boolean_search = {
    "value": {
        "results": [{'docdb_family_id': 123, 'publication_number': 'WO2010075343A2'}]
    },
    'query': {'parsed_query': 'TITLE=test'}
}

family_id_cached_other_searches = {
    "value": {
        "results": [{'docdb_family_id': 123}]
    }
}

publication_number_cached_other_searches = {
    "value": {
        "results": [{'docdb_family_id': 123, 'publication_number': 'WO2010075343A2'}]
    }
}

test_params = [
    (None, True, {"value": {"results": []}}, HTTPStatus.BAD_REQUEST),
    (None, True, None, HTTPStatus.BAD_REQUEST),
    (None, False, None, HTTPStatus.BAD_REQUEST),
    (family_id_request_body_boolean_search, False, None, HTTPStatus.GONE),
    (family_id_request_body_boolean_search, True, None, HTTPStatus.GONE),
    (family_id_request_body_boolean_search, False, family_id_cached_boolean_search, HTTPStatus.NOT_FOUND),
    (family_id_request_body_boolean_search, True, family_id_cached_boolean_search, HTTPStatus.OK),
    (publication_number_request_body_boolean_search, False, None, HTTPStatus.GONE),
    (publication_number_request_body_boolean_search, True, None, HTTPStatus.GONE),
    (publication_number_request_body_boolean_search, False, publication_number_cached_boolean_search,
     HTTPStatus.NOT_FOUND),
    (publication_number_request_body_boolean_search, True, publication_number_cached_boolean_search, HTTPStatus.OK),
    (family_id_request_body_other_searches, False, None, HTTPStatus.GONE),
    (family_id_request_body_other_searches, True, None, HTTPStatus.GONE),
    (family_id_request_body_other_searches, False, family_id_cached_other_searches, HTTPStatus.NOT_FOUND),
    (family_id_request_body_other_searches, True, family_id_cached_other_searches, HTTPStatus.OK),
    (publication_number_request_body_other_searches, False, None, HTTPStatus.GONE),
    (publication_number_request_body_other_searches, True, None, HTTPStatus.GONE),
    (publication_number_request_body_other_searches, False, publication_number_cached_other_searches,
     HTTPStatus.NOT_FOUND),
    (publication_number_request_body_other_searches, True, publication_number_cached_other_searches, HTTPStatus.OK),
]


class TestDocumentHighlightSingle:
    URL = DOCUMENT_HIGHLIGHT_URL

    @pytest.mark.parametrize('payload, es_found, cached_search, expected_status', test_params)
    def test_document_highlight(self, client, mocker, payload, es_found, cached_search, expected_status):
        es_response = data.get_single_full_doc_from_es(found=es_found)
        es_docs = es_response['hits']['hits'] if es_found else []
        mocker.patch(DOC_HIGHLIGHT_PATH, return_value=es_docs)
        mocker.patch(GET_CACHED_SEARCH_PATH, return_value=cached_search)
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(self.URL, headers=headers, json=payload)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            assert 'highlight' in rv.get_json()['data']
