# Third party imports
import json

import pytest

# Standard lib imports
from http import HTTPStatus

# Local imports
from octimine_common.enums import SearchType
from app import messages
from app.validations import TERM_SEARCH_MIN_LENGTH as MIN_LENGTH, \
    TERM_SEARCH_MAX_LENGTH as MAX_LENGTH
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.url import TERM_SEARCH_URL as SEARCH_URL


class TestTerm:
    URL = SEARCH_URL

    parameters = [('?field=nonsense&value=abc', messages.ERROR_TERM_INVALID),
                  ('?field=applicants&value=abc*', messages.ERROR_TERM_INVALID_VALUE),
                  ('?value=abc', messages.ERROR_TERM_MISSING_PARAMETER),
                  ('?search_hash=abc', messages.ERROR_TERM_MISSING_PARAMETER),
                  ('?field=applicants&value=a', messages.ERROR_TERM_SEARCH_LENGTH % (MIN_LENGTH, MAX_LENGTH)),
                  ('?field=authorities&value=DEN', messages.ERROR_TERM_SEARCH_LENGTH % (1, 2))]

    @pytest.mark.parametrize('after_url, expected_message', parameters)
    def test_term_search_invalid_request(self, client, mocker, after_url,
                                         expected_message):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(self.URL + after_url, headers=headers)
        assert_status(rv, HTTPStatus.BAD_REQUEST, expected_message)

    def test_term_search_success(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        mocker.patch('app.ext.elastic_search.elastic_search_extension.ElasticSearchExtension.get_terms',
                     return_value=data.es_result_terms)
        rv = client.get(self.URL + '?field=applicants&value=abc', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        assert response_data['data']['field'] == 'applicants'
        assert response_data['data']['value'] == 'abc'
        assert len(response_data['data']['results']) == 2

    def test_term_search_success_with_hash(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        mocker.patch('app.ext.elastic_search.elastic_search_extension.ElasticSearchExtension.get_terms',
                     return_value=data.es_result_terms)
        semantic_data = {"user_id": 1, "type": SearchType.SEMANTIC.value,
                         "value": [{"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                   {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}]}
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        rv = client.get(self.URL + '?field=applicants&search_hash=s12345', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        assert response_data['data']['field'] == 'applicants'
        assert response_data['data']['value'] is None
        assert len(response_data['data']['results']) == 3
