import pytest
from http import HTTPStatus
from app.models.citation import CitationSearchSchema
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import PATENT_NUMBER_PATH, DOC_ID_PATH, DOC_INFO_PATH
from tests.unit_tests.search_api.mocks.neo4j import Neo4jSessionMock

neo4j_session = 'app.ext.citation_search.citation_search_extension.CitationSearchExtension._session'

search_body = {
    "patent_numbers": data.search_patent_numbers,
    "level": 1,
    "direction": data.search_citation_direction,
    "search_filters": {
        "earliest_arrived_at": "1990-06-30"
    }
}
snapshot_body = {
    "patent_numbers": data.search_patent_numbers,
    "level": 1,
    "direction": data.search_citation_direction,
    "patent_documents_ids": data.search_document_ids,
    "npl_documents_ids": data.search_nodes_ids
}


class TestSearchCitationFlat:
    params = [
        (Neo4jSessionMock(level=1), '', HTTPStatus.OK,
         {"patent_numbers": data.search_patent_numbers, "level": 1, "document_type": "NPL", "citation_phase": "SEA"}),
        (Neo4jSessionMock(level=1), '', HTTPStatus.BAD_REQUEST,
         {"patent_numbers": data.search_patent_numbers, "level": 1, "document_type": "NPL",
          "citation_phase": "bond"}),
        (Neo4jSessionMock(level=1), '', HTTPStatus.BAD_REQUEST,
         {"patent_numbers": data.search_patent_numbers, "level": 1, "document_type": "NPL",
          "citation_category": "JamesBond007"}),
        (Neo4jSessionMock(level=1), '?show_citations=1', HTTPStatus.OK,
         {"patent_numbers": data.search_patent_numbers, "level": 1, "citation_category": "X"}),
        (Neo4jSessionMock(level=1), '?show_citations=0&include_focal=1', HTTPStatus.OK,
         {"patent_numbers": data.search_patent_numbers, "level": 1, "search_filters": {
             "earliest_arrived_at": "1990-06-30", "free_text_query": "APPLICANTS=Test AND AUTHORITIES=EP"}}),
        (Neo4jSessionMock(level=2), '?show_general=1&show_bibliographic=0&page_size=25', HTTPStatus.OK,
         {"patent_numbers": data.search_patent_numbers, "level": 2}),
        (Neo4jSessionMock(level=2), '', HTTPStatus.BAD_REQUEST,
         {"patent_numbers": data.search_patent_numbers, "level": 3}),
        (Neo4jSessionMock(level=1), '', HTTPStatus.BAD_REQUEST,
         {"patent_numbers": data.search_patent_numbers, "level": 1, "search_filters": {"wrong": "filter"}}),
    ]

    @pytest.mark.parametrize('mock_session, query_string, expected_status, search_request', params)
    def test_citation_search_flat(self, client, mocker, mock_session, query_string,
                                  expected_status, search_request):
        mocker.patch(neo4j_session, return_value=mock_session)
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_citation_es_results(1))
        es_results = data.get_citation_es_results(len(mock_session.results) + 1)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_results))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/citation_flat' + query_string, headers=headers, json=search_request)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            expected_total = len(mock_session.results)
            if 'include_focal=1' in query_string:
                expected_total += 1
            assert rv.json['data']['page']['total_hits'] == expected_total
            assert len(rv.json['data']['documents']) == expected_total
            if 'show_citations=0' not in query_string:
                assert all([d for d in rv.json['data']['documents'] if d['citations']])


class TestCitationSearchSchemaValidation:

    @pytest.mark.parametrize('query_a, query_b', [
        (
                {'patent_numbers': ['EP-2049363-A2', 'EP-0002007-A1'], 'cache': True},
                {
                    'patent_numbers': ['EP-2049363-A2', 'EP-0002007-A1'],
                    'applicant_aliases': [{'applicant': "Test", 'alias': 'TEST'}],
                    'cache': True
                }
        )
    ])
    def test_search_hash_should_change(self, query_a, query_b):
        fake_user_id = 1
        hash_a = CitationSearchSchema.get_search_hash(fake_user_id, query_a)
        hash_b = CitationSearchSchema.get_search_hash(fake_user_id, query_b)
        assert hash_a != hash_b
