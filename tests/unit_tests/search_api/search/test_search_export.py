from http import HTTPStatus
import json
import base64
import pytest
from lorem.text import TextLorem
from octimine_common.enums import SearchType
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import DOC_ID_PATH, PATENT_NUMBER_PATH

_LAYOUTS = ["default", "patentfit", "monitor", "landscape"]


class TestExport:
    def test_export_csv_semantic(self, client, mocker):
        cached_filters = {"ma1": True, 'text_weighting': 1.}
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             query={"search_input": "Test", "search_filters": cached_filters},
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                                 {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                             ]})
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token, Accept='text/csv')
        search_filters = {"applicants_plus": ["Test"]}
        rv = client.post('/search/export/shash', headers=headers, json={'search_filters': search_filters})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('text/csv')

    def test_export_excel_boolean(self, client, mocker):
        boolean_data = data.get_cached_data(
            search_type=SearchType.BOOLEAN,
            query={"search_input": "Test", "search_filters": {"applicants_plus": ["test"]}},
            value={
                "results": [
                    {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                    {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                ],
                "hits": 250
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(boolean_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/bhash?format=xls', headers=headers, json={})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/vnd.openxmlformats-officedocument')

    def test_export_csv_patent_numbers(self, app, client, mocker):
        patent_number_data = data.get_cached_data(
            search_type=SearchType.PATENT_NUMBER,
            query={"patent_numbers": ["DE878", "EP123", "JP878645"]},
            value={
                "results": [
                    {"docdb_family_id": 1, "rank": 1, "original_number": "EP123"},
                    {"docdb_family_id": 2, "rank": 2, "original_number": "JP878645"}
                ],
                "hits": 2
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(patent_number_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/phash?format=csv', headers=headers, json={})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('text/csv')

    @pytest.mark.parametrize('layout', _LAYOUTS)
    def test_export_pdf_charts(self, client, mocker, layout):
        boolean_data = data.get_cached_data(
            search_type=SearchType.BOOLEAN,
            query={"search_input": "Test", "search_filters": {"applicants_plus": ["test"]}},
            value={
                "results": [
                    {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                    {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                ],
                "hits": 250
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(boolean_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        payload = data.get_json_file("chart_data.json")
        payload["layout"] = layout
        rv = client.post('/search/export/bhash?format=pdf', headers=headers, json=payload)
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')

    @pytest.mark.parametrize('img_base64', [
        'bad_encoding',
        str(base64.b64encode(" ".encode("utf8"))),
        str(base64.b64encode("<root>bad PNG data</root>".encode("utf8"))),
    ])
    def test_export_pdf_charts_handles_incorrect_chart_img_base64(self, client, mocker, img_base64):
        boolean_data = data.get_cached_data(
            search_type=SearchType.BOOLEAN,
            query={"search_input": "Test", "search_filters": {"applicants_plus": ["test"]}},
            value={
                "results": [
                    {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                    {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                ],
                "hits": 250
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(boolean_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        payload = {
            'charts': [{
                'key': 'basic_top_applicants',
                'title': "Top Applicants",
                'img_base64': img_base64
            }]
        }
        rv = client.post('/search/export/bhash?format=pdf', headers=headers, json=payload)
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')

    @pytest.mark.parametrize('layout', _LAYOUTS)
    def test_export_pdf_with_markup_data(self, client, mocker, layout):
        boolean_data = data.get_cached_data(
            search_type=SearchType.BOOLEAN,
            query={"search_input": "Test", "search_filters": {"applicants_plus": ["test"]}},
            value={
                "results": [
                    {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                    {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                ],
                "hits": 250
            }
        )
        boolean_data["value"]["results"] = [
            {
                **r,
                "title": "Title <b>with</b> markup < & >",
                "abstract": "My abstract <"
            } for r in boolean_data["value"]["results"]
        ]
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(boolean_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        payload = {
            'title': "< My title & >",
            'subtitle': "<b>My subtitle",
            'layout': layout
        }
        rv = client.post('/search/export/bhash?format=pdf', headers=headers, json=payload)
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')

    @pytest.mark.parametrize('layout', _LAYOUTS)
    def test_export_pdf_with_large_data(self, client, mocker, layout):
        boolean_data = data.get_cached_data(
            search_type=SearchType.BOOLEAN,
            query={"search_input": "Test", "search_filters": {"applicants_plus": ["test"]}},
            value={
                "results": [
                    {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                    {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                ],
                "hits": 250
            }
        )
        boolean_data["value"]["results"] = [
            {
                **r,
                "abstract": TextLorem(srange=(1000, 1000)).sentence(),
            } for r in boolean_data["value"]["results"]
        ]
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(boolean_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/bhash?format=pdf', headers=headers, json={'layout': layout})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')

    @pytest.mark.parametrize('layout', _LAYOUTS)
    def test_export_citation_pdf(self, app, client, mocker, layout):
        citations = {'1001': {'1002': []}}
        citation_data = data.get_cached_data(
            search_type=SearchType.CITATION,
            query={'direction': None, 'patent_numbers': ['EP-0376380-A1', 'US-6409187-B1'], 'level': 1},
            value={
                "results": [
                    {"docdb_family_id": 1001}
                ],
                "citations": citations
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(citation_data))
        citations = {

        }
        citation_data = data.get_cached_data(
            search_type=SearchType.CITATION,
            query={'direction': None, 'patent_numbers': ['EP-0376380-A1', 'US-6409187-B1'], 'level': 1},
            value={
                "results": {},
                "citations": citations
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(citation_data))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=1))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/chash?format=pdf', headers=headers, json={"layout": layout})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')

    @pytest.mark.parametrize('layout', _LAYOUTS)
    def test_export_pdf_semantic(self, client, mocker, layout):
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             query={
                                                 "search_input": "Test",
                                                 "search_filters": {"earliest_priority_date": "1990-01-01"},
                                                 "patent_numbers": data.search_patent_numbers
                                             },
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                                 {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                             ]})
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=1))
        headers = dict(Authorization='Bearer %s' % access_token, Accept='application/pdf')
        rv = client.post('/search/export/shash', headers=headers,
                         json={'patent_documents_ids': [1, 2], 'layout': layout})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')

    def test_another_user(self, client, mocker):
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                                 {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                             ]},
                                             uid=2)
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/shash?format=csv', headers=headers, json={})
        assert_status(rv, HTTPStatus.GONE)

    def test_unacceptable(self, client, mocker):
        semantic_data = data.get_cached_data(search_type=SearchType.SEMANTIC,
                                             query={"search_input": "Test"},
                                             value={"results": [
                                                 {"docdb_family_id": 1, "similarity_index": 2}
                                             ]})
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/shash?format=JPG', headers=headers, json={})
        assert_status(rv, HTTPStatus.NOT_ACCEPTABLE)

    def test_no_cache(self, client, mocker):
        mocker.patch('redis.StrictRedis.get', return_value=None)
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/bhash?format=csv', headers=headers, json={})
        assert_status(rv, HTTPStatus.GONE)

    def test_unauthorized(self, client, mocker):
        semantic_data = {"user_id": 1, "type": SearchType.SEMANTIC.value, "query": {"search_input": "Test"},
                         "value": [{"docdb_family_id": 1, "similarity_index": 2}]}
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(semantic_data))
        rv = client.post('/search/export/shash?format=CSV', json={})
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    def test_export_ad_hoc_documents(self, client, mocker):
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export?format=pdf', headers=headers, json=data.get_json_file("monitor_data.json"))
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')

    def test_export_custom_fields(self, client, mocker):
        boolean_data = data.get_cached_data(
            search_type=SearchType.BOOLEAN,
            query={"search_input": "Test", "search_filters": {"applicants_plus": ["test"]}},
            value={
                "results": [
                    {"docdb_family_id": 1, "similarity_index": 2, "rank": 1, "title": 'name 1'},
                    {"docdb_family_id": 2, "similarity_index": 3, "rank": 2, "title": 'name 2'}
                ],
                "hits": 250
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(boolean_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post('/search/export/bhash?format=xls', headers=headers, json={
            "custom_fields": ["title", "similarity_index"]})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/vnd.openxmlformats-officedocument')

        rv = client.post('/search/export/bhash?format=csv', headers=headers, json={
            "custom_fields": ["title", "docdb_family_id", "rank"]})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('text/csv')
        assert "Title,DocDB ID,Rank" in rv.get_data(True)
        assert "similarity_index" not in rv.get_data(True)

        rv = client.post('/search/export/bhash?format=pdf', headers=headers, json={"custom_fields": ["rank", "title"]})
        assert_status(rv, HTTPStatus.OK)
        assert rv.headers['Content-Type'].startswith('application/pdf')
