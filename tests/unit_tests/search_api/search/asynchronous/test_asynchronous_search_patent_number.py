import json
from http import H<PERSON><PERSON>tatus
import pytest
from celery import states
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.mocks.async_result import AsyncResultMock
from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.url import ASYNC_PATENT_NUMBER_SEARCH_URL


class TestAsyncPatentNumber:

    @pytest.mark.parametrize('request_body', [
        {},
        {"none_sense": []},
        {"patent_numbers": ["ASDFB", "INVALID"]}
    ])
    def test_patent_number_search_invalid_request(self, client, request_body):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(ASYNC_PATENT_NUMBER_SEARCH_URL, headers=headers, data=json.dumps(request_body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    @pytest.mark.parametrize('async_result, cached', [
        (AsyncResultMock("phash", states.PENDING), False),
        (AsyncResultMock("phash", states.FAILURE), False),
    ])
    def test_patent_number_search_starts(self, client, mocker, async_result, cached):
        mocker.patch('app.extensions.celery.AsyncResult',
                     return_value=async_result)
        mocker.patch('app.extensions.patent_number_search.is_search_cached', return_value=cached)
        mocker.patch('app.extensions.patent_number_search.cache_search')
        mocker.patch('app.background_tasks.search.get_documents_by_patent_number.apply_async',
                     return_value=AsyncResultMock("phash", states.PENDING))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers)
        rv = client.post(ASYNC_PATENT_NUMBER_SEARCH_URL, headers=headers, data=json.dumps(body))
        assert rv.status_code == HTTPStatus.ACCEPTED
        response_data = rv.get_json()
        assert response_data['search_hash'] == async_result.task_id
        assert response_data['status'] == states.PENDING

    @pytest.mark.parametrize('async_result', [
        AsyncResultMock("phash", states.SUCCESS),
        AsyncResultMock("phash", states.PENDING),
    ])
    def test_patent_number_search_already_started(self, client, mocker, async_result):
        mocker.patch('app.extensions.celery.AsyncResult', return_value=async_result)
        mocker.patch('app.extensions.patent_number_search.get_cached_search',
                     return_value={"value": {"results": []}})
        mocker.patch('app.extensions.patent_number_search.cache_search')
        mocker.patch('app.extensions.patent_number_search.is_search_cached', return_value=True)
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers)
        rv = client.post(ASYNC_PATENT_NUMBER_SEARCH_URL, headers=headers, data=json.dumps(body))
        assert rv.status_code == HTTPStatus.SEE_OTHER
        assert rv.headers["Location"]

    def test_should_trigger_already_expired_searches(self, client, mocker):
        mocker.patch('app.extensions.celery.AsyncResult',
                     return_value=AsyncResultMock("phash", states.SUCCESS))
        mocker.patch('app.extensions.patent_number_search.is_search_cached', return_value=False)
        mocker.patch('app.extensions.patent_number_search.cache_search')
        mocker.patch('app.background_tasks.search.get_documents_by_patent_number.apply_async',
                     return_value=AsyncResultMock("phash", states.PENDING))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers)
        rv = client.post(ASYNC_PATENT_NUMBER_SEARCH_URL, headers=headers, data=json.dumps(body))
        assert rv.status_code == HTTPStatus.ACCEPTED
        assert rv.get_json()['status'] == 'PENDING'
        assert rv.get_json()['search_hash'] == 'phash'
        assert rv.headers["Location"]
