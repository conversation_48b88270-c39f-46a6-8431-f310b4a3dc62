import datetime
import json
from http import HTTPStatus

import pytest
from celery import states
from octimine_common.exceptions import BadRequestException
from octimine_common.tests.data import access_token

from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.mocks.async_result import AsyncResultMock
from tests.unit_tests.search_api.url import ASYNC_RICH_NEIGHBORHOOD_URL


class TestAsyncRichNeighborhood:

    @pytest.mark.parametrize('request_body', [
        {},
        {"none_sense": []},
        {"patent_numbers": ["ASDFB", "INVALID"], "skip_invalid": 0},
        {"docdb_family_ids": []},
        {"docdb_family_ids": [123]},
        {"patent_numbers": ["CN-104403499-A", "12312xxx", "12312312"], "skip_invalid": 1},
        {"patent_numbers": ["US20090094134A1"], "quantiles": [0, 0.25, 0.75]},
        {"patent_numbers": ["US20090094134A1"], "quantiles": [0, 0.25, 0.75, 1, 2]},
        {"docdb_family_ids": [], "quantiles": [0, 0.25, 0.75]},
        {"docdb_family_ids": [123], "quantiles": [0, 0.25, 0.75, 1, 2]},
    ])
    def test_rich_neighborhood_invalid_request(self, client, request_body):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(ASYNC_RICH_NEIGHBORHOOD_URL, headers=headers, data=json.dumps(request_body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    @pytest.mark.parametrize('request_body', [
        {"patent_numbers": ["CN-104403499-A", "US20090094134A1"], "skip_invalid": 0},
        {"docdb_family_ids": data.search_document_ids, "skip_invalid": 0},
        {"patent_numbers": ["CN-104403499-A", "US20090094134A1", "12312xxx", "12312312"], "skip_invalid": 1},
        {"patent_numbers": ["CN-104403499-A", "US20090094134A1"], "quantiles": [0, 0.25, 0.5, 0.75]},
        {"docdb_family_ids": data.search_document_ids, "quantiles": [0, 0.25, 0.5, 0.75]}
    ])
    def test_rich_neighborhood_valid_request(self, client, mocker, request_body):
        mocker.patch('app.extensions.celery.AsyncResult',
                     return_value=AsyncResultMock("phash", states.PENDING, None))
        mocker.patch('app.background_tasks.rich_neighborhood.calculate_rich_neighborhood.apply_async',
                     return_value=AsyncResultMock("phash", states.PENDING))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(ASYNC_RICH_NEIGHBORHOOD_URL, headers=headers, data=json.dumps(request_body))
        assert rv.status_code == HTTPStatus.ACCEPTED

    @pytest.mark.parametrize('async_result, expected_status', [
        (AsyncResultMock("phash", states.PENDING, None), HTTPStatus.ACCEPTED),
        (AsyncResultMock("phash", states.STARTED, None), HTTPStatus.ACCEPTED),
        (AsyncResultMock("phash", states.SUCCESS, datetime.datetime.now(datetime.UTC)), HTTPStatus.ACCEPTED),
        (AsyncResultMock("phash", states.FAILURE, datetime.datetime.now(datetime.UTC), BadRequestException()),
         HTTPStatus.BAD_REQUEST)
    ])
    def test_rich_neighborhood_submit(self, client, mocker, async_result, expected_status):
        mocker.patch('app.extensions.celery.AsyncResult', return_value=async_result)
        mocker.patch('app.background_tasks.rich_neighborhood.calculate_rich_neighborhood.apply_async',
                     return_value=AsyncResultMock("phash", states.PENDING))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers)
        rv = client.post(ASYNC_RICH_NEIGHBORHOOD_URL, headers=headers, data=json.dumps(body))
        assert rv.status_code == expected_status
        response_data = rv.get_json()
        if expected_status == HTTPStatus.ACCEPTED:
            assert response_data['task_id'] == async_result.task_id
            assert response_data['status'] == async_result.status

    @pytest.mark.parametrize('async_result, expected_status', [
        (AsyncResultMock("phash", states.PENDING, None), HTTPStatus.ACCEPTED),
        (AsyncResultMock("phash", states.STARTED, None), HTTPStatus.ACCEPTED),
        (AsyncResultMock("phash", states.SUCCESS, None), HTTPStatus.OK),
        (AsyncResultMock("phash", states.RETRY, None), HTTPStatus.ACCEPTED),
        (AsyncResultMock("phash", states.FAILURE, None, BadRequestException()), HTTPStatus.BAD_REQUEST)
    ])
    def test_rich_neighborhood_get_status(self, client, mocker, async_result, expected_status):
        mocker.patch('app.extensions.celery.AsyncResult', return_value=async_result)
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(f'{ASYNC_RICH_NEIGHBORHOOD_URL}/{async_result.task_id}', headers=headers)
        assert rv.status_code == expected_status
