from http import HTTPStatus
import pytest
import json
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from octimine_common.enums import SearchType
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.url import ASYNC_RESULTS_URL


class TestAsyncSearchResults:

    @pytest.mark.parametrize('cached_data, resp_status', [
        (
                data.get_cached_data(search_type=SearchType.BOOLEAN,
                                     query={"search_input": "TITLE=Test"},
                                     value={"results": [], "pending": True}),
                HTTPStatus.NOT_FOUND
        ),
        (
                data.get_cached_data(search_type=SearchType.BOOLEAN,
                                     query={"search_input": "TITLE=Test"},
                                     value={"results": []}),
                HTTPStatus.OK
        ),
        (
                data.get_cached_data(search_type=SearchType.BOOLEAN,
                                     query={"search_input": "TITLE=Test"},
                                     value={"results": [
                                         {"docdb_family_id": 1, "similarity_index": 2, "rank": 1},
                                         {"docdb_family_id": 2, "similarity_index": 3, "rank": 2}
                                     ]}),
                HTTPStatus.OK
        ),
    ])
    def test_results(self, client, mocker, cached_data, resp_status):
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(cached_data))
        mocker.patch(data.DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(ASYNC_RESULTS_URL + 'bhash', headers=headers)
        response_data = rv.get_json()
        assert_status(rv, resp_status)
        if resp_status == HTTPStatus.OK:
            assert response_data['data']['search_info']['search_hash'] == 'bhash'
