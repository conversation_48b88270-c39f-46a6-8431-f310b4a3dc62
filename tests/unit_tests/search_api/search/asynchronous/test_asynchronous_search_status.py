import pytest
import json
from http import HTTPStatus
from celery import states
from octimine_common.enums import SearchType
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.url import ASYNC_STATUS_URL
from tests.unit_tests.search_api.mocks.async_result import AsyncResultMock


class TestAsyncSearchStatus:

    @pytest.mark.parametrize('search_hash, status, cached_data, expected_http_status', [
        (
            "dhash",
            states.PENDING,
            data.get_cached_data(search_type=SearchType.DOCUMENT_ID,
                                 value={"results": []}),
            HTTPStatus.SEE_OTHER
        ),
        (
                "dhash",
                states.PENDING,
                data.get_cached_data(search_type=SearchType.DOCUMENT_ID,
                                     value={"results": [], "pending": True}),
                HTTPStatus.OK
        ),
        (
                "dhash",
                states.PENDING,
                None,
                HTTPStatus.NOT_FOUND
        ),
        (
                "dhash",
                states.<PERSON>UCCESS,
                None,
                HTTPStatus.NOT_FOUND
        ),
        (
                "dhash",
                states.FAILURE,
                data.get_cached_data(search_type=SearchType.DOCUMENT_ID,
                                     value={"results": []}),
                HTTPStatus.OK
        )
    ])
    def test_status_reported_correctly(self, client, mocker, search_hash, status, cached_data,
                                       expected_http_status):
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(cached_data) if cached_data else None)
        mocker.patch('app.extensions.celery.AsyncResult',
                     return_value=AsyncResultMock(search_hash, status))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(f"{ASYNC_STATUS_URL}{search_hash}", headers=headers)
        assert rv.status_code == expected_http_status
        if expected_http_status == HTTPStatus.SEE_OTHER:
            assert rv.get_json()['status'] == 'SUCCESS'
            assert rv.headers["Location"]
