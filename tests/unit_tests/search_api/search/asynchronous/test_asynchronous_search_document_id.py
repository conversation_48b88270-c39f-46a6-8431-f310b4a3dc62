import pytest
import json
from http import HTTPStatus
from celery import states
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.mocks.async_result import AsyncResultMock
from tests.unit_tests.search_api.url import ASYNC_DOCUMENT_SEARCH_URL


class TestAsyncDocumentId:

    @pytest.mark.parametrize('request_body', [
        {},
        {"documents_ids": None},
        {'documents_ids': 'invalid_array'},
        {'documents_ids': [None]},
        {'documents_ids': list(range(150001))}
    ])
    def test_document_search_invalid_request(self, client, request_body):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(ASYNC_DOCUMENT_SEARCH_URL, headers=headers, data=json.dumps(request_body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    @pytest.mark.parametrize('async_result, cached', [
        (AsyncResultMock("dhash", states.PENDING), False),
        (AsyncResultMock("dhash", states.FAILURE), False),
    ])
    def test_document_search_starts(self, client, mocker, async_result, cached):
        mocker.patch('app.extensions.celery.AsyncResult', return_value=async_result)
        mocker.patch('app.extensions.document_id_search.is_search_cached', return_value=cached)
        mocker.patch('app.extensions.document_id_search.cache_search')
        mocker.patch('app.background_tasks.search.get_documents_by_id.apply_async',
                     return_value=AsyncResultMock("dhash", states.PENDING))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(documents_ids=[1, 2, 3])
        rv = client.post(ASYNC_DOCUMENT_SEARCH_URL, headers=headers, data=json.dumps(body))
        assert rv.status_code == HTTPStatus.ACCEPTED
        response_data = rv.get_json()
        assert response_data['search_hash'] == async_result.task_id
        assert response_data['status'] == states.PENDING

    @pytest.mark.parametrize('async_result', [
        AsyncResultMock("dhash", states.SUCCESS),
        AsyncResultMock("dhash", states.PENDING),
    ])
    def test_document_search_already_started(self, client, mocker, async_result):
        mocker.patch('app.extensions.celery.AsyncResult', return_value=async_result)
        mocker.patch('app.extensions.document_id_search.get_cached_search',
                     return_value={"value": {"results": []}})
        mocker.patch('app.extensions.document_id_search.cache_search')
        mocker.patch('app.extensions.document_id_search.is_search_cached', return_value=True)
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(documents_ids=[1, 2, 3])
        rv = client.post(ASYNC_DOCUMENT_SEARCH_URL, headers=headers, data=json.dumps(body))
        assert rv.status_code == HTTPStatus.SEE_OTHER
        assert rv.headers["Location"]

    def test_should_trigger_already_expired_searches(self, client, mocker):
        mocker.patch('app.extensions.celery.AsyncResult',
                     return_value=AsyncResultMock("dhash", states.SUCCESS))
        mocker.patch('app.extensions.document_id_search.is_search_cached', return_value=False)
        mocker.patch('app.extensions.document_id_search.cache_search')
        mocker.patch('app.background_tasks.search.get_documents_by_id.apply_async',
                     return_value=AsyncResultMock("dhash", states.PENDING))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(documents_ids=[1, 2, 3])
        rv = client.post(ASYNC_DOCUMENT_SEARCH_URL, headers=headers, data=json.dumps(body))
        assert rv.status_code == HTTPStatus.ACCEPTED
        assert rv.get_json()['status'] == 'PENDING'
        assert rv.get_json()['search_hash'] == 'dhash'
        assert rv.headers["Location"]
