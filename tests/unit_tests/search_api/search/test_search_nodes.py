# Standard lib imports
import json
import pytest
from http import HTTPStatus

from octimine_common.enums import SearchType
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.url import NODE_SEARCH_URL as SEARCH_URL


# Local imports


class TestNode:
    URL = SEARCH_URL

    def test_node_search_invalid_request(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(nodes_ids="Nonsense")
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_nodes_success_empty_results(self, client, mocker):
        mocker.patch('app.ext.citation_search.citation_search_extension.CitationSearchExtension.'
                     'get_nodes', return_value=[])
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(nodes_ids=data.search_nodes_ids)
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.OK)
        assert len(rv.get_json()['data']['nodes']) == 0

    def test_nodes_search_success(self, client, mocker):
        mocker.patch('app.ext.citation_search.citation_search_extension.CitationSearchExtension.'
                     'get_nodes', return_value=data.get_neo4j_nodes(len(data.search_nodes_ids)))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(nodes_ids=data.search_nodes_ids)
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        response_data = rv.get_json()["data"]
        assert_status(rv, HTTPStatus.OK)
        assert len(response_data["nodes"]) == 2
        assert "text" in response_data["nodes"][0]


class TestNodesHash:

    @pytest.mark.parametrize('search_hash, expected_status', [
        ('chash', HTTPStatus.GONE),
        ('bhash', HTTPStatus.BAD_REQUEST)
    ])
    def test_nodes_search_invalid_hash(self, client, mocker, search_hash, expected_status):
        citation_data = {"user_id": 1, "type": SearchType.BOOLEAN.value, "value": {"results": [], "hits": 0}}
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(citation_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(f'/search/node/{search_hash}', headers=headers)
        assert_status(rv, expected_status)

    def test_nodes_search_success(self, client, mocker):
        citations = {
            '4288347': [
                {'type': 'NPL', 'level': 1, 'text': 'Machine Design, vol, 31', 'npl_id': '04de'},
                {'type': 'NPL', 'level': 1, 'text': 'Built-in transducer', 'npl_id': '0048'}
            ],
            '04de': [{'type': 'NPL', 'level': 0, 'text': None, 'npl_id': None, 'reverse': True}],
            '0048': [{'type': 'NPL', 'level': 0, 'text': None, 'npl_id': None, 'reverse': True}]
        }
        citation_data = data.get_cached_data(
            search_type=SearchType.CITATION,
            value={
                "results": [{"docdb_family_id": 4288347}],
                "citations": citations
            }
        )
        mocker.patch('redis.StrictRedis.get', return_value=json.dumps(citation_data))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/node/chash', headers=headers)
        response_data = rv.get_json()["data"]
        assert_status(rv, HTTPStatus.OK)
        assert len(response_data["nodes"]) == 2
        assert "text" in response_data["nodes"][0]
