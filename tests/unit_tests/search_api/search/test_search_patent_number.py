import json
import pytest
from http import HTTPStatus
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import PATENT_NUMBER_PATH, DOC_ID_PATH, doc_info_results
from tests.unit_tests.search_api.url import PATENT_NUMBER_SEARCH_URL as SEARCH_URL
from app.extensions import elastic_search

documents = [1]


class TestPatentNumber:
    URL = SEARCH_URL

    def test_patent_number_search_success(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers)
        rv = client.post(self.URL + "?show_general=1", headers=headers, data=json.dumps(body))
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        for idx, doc in enumerate(response_data['data']['documents']):
            assert doc['general']['docdb_family_id'] == idx + 1
            assert 'analytics' not in doc

    def test_patent_number_skip_invalid(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers + ['invalid'], skip_invalid=False)
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)
        body['skip_invalid'] = True
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.OK)

    def test_patent_number_search_extra_params(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers)
        rv = client.post(self.URL + "?show_general=1&show_bibliographic=1&show_analytics=1&show_fulltext=1",
                         headers=headers, data=json.dumps(body))
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        for doc in response_data['data']['documents']:
            assert 'abstract' in doc['bibliographic']
            assert 'impact' in doc['analytics']
            assert 'publication_number' in doc['general']
            assert 'claims' in doc['fulltext']

    def test_patent_number_search_publications(self, client, mocker):
        mocker.patch(PATENT_NUMBER_PATH, return_value=doc_info_results)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(doc_info_results))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers, search_type='PUBLICATION')
        rv = client.post(self.URL + "?show_general=1&show_bibliographic=1&show_analytics=1&show_fulltext=1",
                         headers=headers, data=json.dumps(body))
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        assert 'publications' in response_data['data']

    @pytest.mark.parametrize('body', [
        dict(patent_numbers="Some nonsense"),
        dict(patent_numbers=["DE-12345678-B1"], patent_numbers_type="nonsense"),
        dict(patent_numbers=["DE-123456789101112-B1"], patent_numbers_type="publication_number"),
        dict(patent_numbers=["DE-ABCDE12345678910-B2"], patent_numbers_type="application_number"),
    ])
    def test_patent_number_search_invalid_request(self, client, body):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers="Some nonsense")
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_patent_number_es_error(self, client, mocker):
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.es_result_empty)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.es_result_empty))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=data.search_patent_numbers)
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        assert len(response_data['data']['documents']) == 0

    def test_patent_number_search_sorting(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        es_result['hits']['hits'][1]['matched_queries'] = ['EP-2007']
        es_result['hits']['hits'][2]['matched_queries'] = ['EP-2049363']
        del es_result['hits']['hits'][3]
        del es_result['hits']['hits'][3]
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=['ep-2049363', 'EP-2007'])
        rv = client.post(self.URL + "?show_general=0", headers=headers, data=json.dumps(body))
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        response_documents = response_data['data']['documents']
        assert response_documents[0]['general'].get('original_number') is None
        assert response_documents[1]['general']['original_number'] == 'ep-2049363'
        assert response_documents[2]['general']['original_number'] == 'EP-2007'

    def test_patent_number_big_search(self, app, client, mocker):
        chunk_number = 2
        es_result = data.get_es_search_result(page=1)
        search = mocker.patch.object(elastic_search.client, 'search', return_value=es_result)
        chunk_size = app.config['ELASTIC_SEARCH_BOOLEAN_QUERY_CHUNKS']
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=['EP-' + str(i).zfill(7) for i in range(0, chunk_size * chunk_number)])
        rv = client.post(self.URL + "?show_general=1", headers=headers, data=json.dumps(body))
        assert_status(rv, HTTPStatus.OK)
        assert search.call_count == chunk_number

    def test_patent_number_original_numbers(self, client, mocker):
        es_result = data.get_es_search_result(page=1)
        es_result['hits']['hits'] = es_result['hits']['hits'][:2]
        for i, num in enumerate(data.search_patent_numbers):
            es_result['hits']['hits'][i]['matched_queries'] = [num]
        mocker.patch(PATENT_NUMBER_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(patent_numbers=[data.search_patent_numbers[0], 'invalid', data.search_patent_numbers[1]],
                    skip_invalid=True)
        rv = client.post(self.URL, headers=headers, data=json.dumps(body))
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        response_documents = response_data['data']['documents']
        assert response_documents[0]['general']['original_number'] == data.search_patent_numbers[0]
        assert response_documents[1]['general']['original_number'] == data.search_patent_numbers[1]
