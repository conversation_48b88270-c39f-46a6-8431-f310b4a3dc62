import pytest
from http import HTTPStatus
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import doc_info_results
from octimine_common.tests.data import access_token


@pytest.fixture
def patch_document_info(mocker):
    mocker.patch('app.services.search.functions.elastic_search.get_documents_info',
                 return_value=doc_info_results)


class TestDocumentInfo:

    def test_forbid_unauthorized_access(self, client):
        rv = client.get('/search/document_info/12345')
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    @pytest.mark.usefixtures('patch_document_info')
    def test_should_retrieve_document_info_for_family_id(self, client):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/document_info/12345', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        assert response_data['data']['document_info'][0]['legal_status'] is not None
        assert response_data['data']['document_info'][0]['application_number'] is None
        assert response_data['data']['document_info'][0]['application_number'] is None
        assert response_data['data']['document_info'][1]['legal_status'] is not None
        assert response_data['data']['document_info'][1]['application_number'] is not None
        assert response_data['data']['document_info'][1]['application_number'] is not None
