# Third party imports
import pytest

# Standard lib imports
from http import HTT<PERSON>tatus
from copy import deepcopy

# Local imports
from app.models.boolean import BooleanSearchSchema
from tests.unit_tests.search_api import data
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import DOC_ID_PATH, BOOL_QUERY_PATH, DOC_INFO_PATH
from tests.unit_tests.search_api.url import BOOLEAN_SEARCH_URL as SEARCH_URL, BOOLEAN_PARSE_URL as PARSE_URL

search_jwt = 'app.routes.search.boolean.boolean.get_jwt_claims'
snapshot_jwt = 'app.routes.search.boolean.snapshot.get_jwt_claims'
documents = [1]

snapshot_body_update = {
    'patent_documents_ids': documents
}

# Lexer params
lexer_error_search_body = {
    "search_input": data.search_boolean_input_lexer_error
}
lexer_error_snapshot_body = deepcopy(lexer_error_search_body)
lexer_error_snapshot_body.update(snapshot_body_update)

# Parser params
parser_error_search_body = {
    "search_input": data.search_boolean_input_parser_error
}
parser_error_snapshot_body = deepcopy(parser_error_search_body)
parser_error_snapshot_body.update(snapshot_body_update)
parser_error_end_of_expression_search_body = {
    "search_input": '(TITLE=LAPTOP'
}

# Semantic params
semantic_error_search_body = {
    "search_input": data.search_boolean_input_semantic_error
}
semantic_error_snapshot_body = deepcopy(semantic_error_search_body)
semantic_error_snapshot_body.update(snapshot_body_update)

error_params = [
    (SEARCH_URL, lexer_error_search_body, 'lexer_error', 2, 3),
    (SEARCH_URL, parser_error_search_body, 'parser_error', 1, 0),
    (SEARCH_URL, parser_error_end_of_expression_search_body, 'parser_error', 1, 12),
    (SEARCH_URL, semantic_error_search_body, 'semantic_errors', 1, 0),
]


class TestBoolean:
    URL = SEARCH_URL

    @pytest.mark.parametrize('url, body, expected_key_error, line_number, line_position', error_params)
    def test_boolean_search_error(self, client, mocker, url, body, expected_key_error, line_number, line_position):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.BAD_REQUEST)
        assert response_data[expected_key_error]
        if expected_key_error in {'lexer_error', 'parser_error'}:
            self._assert_boolean_error(response_data[expected_key_error], line_number, line_position)
        elif expected_key_error == 'semantic_error':
            for err in response_data[expected_key_error]:
                self._assert_boolean_error(err, line_number, line_position)

    @staticmethod
    def _assert_boolean_error(err, expected_line_number, expected_line_position):
        assert 'line_number' in err
        assert 'line_position' in err
        assert err['line_number'] == expected_line_number
        assert err['line_position'] == expected_line_position

    def test_boolean_search_nonexistent_page(self, client, mocker):
        page = 10
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        headers = dict(Authorization='Bearer %s' % access_token)
        body = dict(search_input=data.search_boolean_input)
        rv = client.post(self.URL + '?page=' + str(page), headers=headers, json=body)
        assert_status(rv, HTTPStatus.OK)
        assert rv.get_json()['data']['documents'] == []

    @pytest.mark.parametrize('page, sort_by', [(1, None), (2, 'priority_date')])
    def test_boolean_search_success(self, client, mocker, page, sort_by):
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_boolean_input,
            "search_filters": data.date_filters
        }
        url = self.URL + '?page=%d&show_analytics=1&show_general=1' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = (page - 1) * response_data['data']['page']['page_size']
        for idx, doc in enumerate(response_data['data']['documents']):
            assert doc['general']['docdb_family_id'] == offset + idx + 1

    @pytest.mark.parametrize('page, sort_by', [(1, None), (2, 'priority_date')])
    def test_boolean_search_with_applicants_alias(self, client, mocker, page, sort_by):
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_boolean_input,
            "search_filters": data.date_filters,
            "applicant_aliases": [{"applicant": "TEST", "alias": "test alias"}]
        }
        url = self.URL + '?page=%d&show_analytics=1&show_general=1' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        for idx, doc in enumerate(response_data['data']['documents']):
            assert doc['bibliographic']['applicants'][0] == "test alias"

    @pytest.mark.parametrize('page, sort_by', [(1, None), (2, 'priority_date')])
    def test_boolean_search_with_proximity_operator(self, client, mocker, page, sort_by):
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(es_result))
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_boolean_input_proximity_operator,
            "search_filters": data.date_filters
        }
        url = self.URL + '?page=%d&show_analytics=1&show_general=1' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = (page - 1) * response_data['data']['page']['page_size']
        for idx, doc in enumerate(response_data['data']['documents']):
            assert doc['general']['docdb_family_id'] == offset + idx + 1

    @pytest.mark.parametrize('page, sort_by', [(1, None), (2, 'publication_date')])
    def test_boolean_search_publication(self, client, mocker, page, sort_by):
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=es_result)
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_boolean_input,
            "search_type": "PUBLICATION"
        }
        url = self.URL + '?page=%d&show_bibliographic=1&show_general=1' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = (page - 1) * response_data['data']['page']['page_size']
        for idx, doc in enumerate(response_data['data']['publications']):
            assert doc['general']['docdb_family_id'] == offset + idx + 1

    @pytest.mark.parametrize('page, sort_by', [(1, None), (2, 'publication_date')])
    def test_boolean_search_application_date(self, client, mocker, page, sort_by):
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=es_result)
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_application_date,
            "search_type": "PUBLICATION"
        }
        url = self.URL + '?page=%d&show_bibliographic=1&show_general=1' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = (page - 1) * response_data['data']['page']['page_size']
        for idx, doc in enumerate(response_data['data']['publications']):
            assert doc['general']['docdb_family_id'] == offset + idx + 1

    @pytest.mark.parametrize('page, sort_by, operator_query', [(1, None, 'APPLICATION_DATE<=2000-12-12'),
                                                               (2, 'publication_date', 'APPLICATION_DATE>2000-12-12'),
                                                               (3, 'publication_date', 'APPLICATION_DATE<>2000-12-12')])
    def test_boolean_search_application_date_comparison(self, client, mocker, page, sort_by, operator_query):
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=es_result)
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": operator_query,
            "search_type": "PUBLICATION"
        }
        url = self.URL + '?page=%d&show_bibliographic=1&show_general=1' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = (page - 1) * response_data['data']['page']['page_size']
        for idx, doc in enumerate(response_data['data']['publications']):
            assert doc['general']['docdb_family_id'] == offset + idx + 1

    @pytest.mark.parametrize('page, sort_by', [(1, None), (2, 'publication_date')])
    def test_boolean_search_application_date_year(self, client, mocker, page, sort_by):
        es_result = data.get_es_search_result(page=page)
        mocker.patch(BOOL_QUERY_PATH, return_value=es_result)
        mocker.patch(DOC_ID_PATH, return_value=es_result)
        mocker.patch(DOC_INFO_PATH, return_value={'docs': []})
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_application_year,
            "search_type": "PUBLICATION"
        }
        url = self.URL + '?page=%d&show_bibliographic=1&show_general=1' % page
        if sort_by:
            url += '&sort_by=%s&sort_order=asc' % sort_by
        rv = client.post(url, headers=headers, json=body)
        response_data = rv.get_json()
        assert_status(rv, HTTPStatus.OK)
        offset = (page - 1) * response_data['data']['page']['page_size']
        for idx, doc in enumerate(response_data['data']['publications']):
            assert doc['general']['docdb_family_id'] == offset + idx + 1


class TestBooleanParsing:
    def test_boolean_parse_success(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_boolean_input
        }
        rv = client.post(PARSE_URL, headers=headers, json=body)
        assert_status(rv, HTTPStatus.OK)

    def test_boolean_parse_error(self, client, mocker):
        headers = dict(Authorization='Bearer %s' % access_token)
        body = {
            "search_input": data.search_boolean_input_parser_error
        }
        rv = client.post(PARSE_URL, headers=headers, json=body)
        assert_status(rv, HTTPStatus.BAD_REQUEST)


class TestBooleanSearchSchemaValidation:

    @pytest.mark.parametrize('query_a, query_b', [
        (
            {'search_input': 'TITLE=test', 'cache': True},
            {
                'search_input': 'TITLE=test',
                'applicant_aliases': [{'applicant': "Test", 'alias': 'TEST'}],
                'cache': True
            }
        )
    ])
    def test_search_hash_should_change(self, query_a, query_b):
        fake_user_id = 1
        hash_a = BooleanSearchSchema.get_search_hash(fake_user_id, query_a)
        hash_b = BooleanSearchSchema.get_search_hash(fake_user_id, query_b)
        assert hash_a != hash_b
