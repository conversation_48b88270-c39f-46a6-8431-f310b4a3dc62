# Standard lib imports

# Local imports
from app.app import create_app


class TestApp:
    def test_app_default_config(self):
        app = create_app(config_env='default')
        assert app.config['DEBUG']
        assert not app.config['TESTING']
        assert app.config['RABBITMQ_EXCHANGE']

    def test_app_testing(self):
        app = create_app(config_env='testing')
        assert app.config['TESTING']
        assert app.config['DEBUG']
