# Standard lib imports
from itertools import chain

# Local imports
from app.ext.semantic_search.exceptions import ProducerResponseError, QueueConnectionError, \
    QueueMessageError, ProducerTimeoutError
from app.ext.semantic_search.messages import ERROR_PRODUCER_RESPONSE, ERROR_PRODUCER_TIMEOUT
from tests.unit_tests.search_api import data


class ModelMock:
    def __init__(self):
        pass


class ChannelMock:

    def exchange_declare(self, exchange, exchange_type):
        pass

    def basic_publish(self, *args, **kwargs):
        pass

    def basic_consume(self, on_message_callback, auto_ack, queue):
        pass


class BlockingConnectionMock:
    def __init__(self, properties=None):
        self._properties = properties
        self._channel = ChannelMock()

    def channel(self):
        return self._channel

    def call_later(self, delay, callback):
        pass

    def process_data_events(self):
        pass


class PropertiesMock:
    def __init__(self, corr_id):
        self.correlation_id = corr_id


class ConsumerMock:
    def __init__(self, password=data.queue_password, username=data.queue_username, host=data.queue_host,
                 port=data.queue_port, producer_error=False, publish_error=False, offset=0, result_size=5):
        self.username = username
        self.password = password
        self.host = host
        self.port = port
        self.producer_error = producer_error
        self.publish_error = publish_error
        self.response = list()
        self.exchange = None
        self.callback_queue = None
        self.response_timeout = 1000
        self.max_subscribers = 1
        self.corr_id = None
        self.error = None
        self.timer = None
        self.offset = offset
        self.result_size = result_size
        self.message = None

    def _connect(self):
        if self.password != data.queue_password or \
           self.username != data.queue_username or \
           self.host != data.queue_host or \
           self.port != data.queue_port:
            raise QueueConnectionError("")
        self.connection = BlockingConnectionMock()
        self.channel = self.connection.channel()

    def perform_search(self, priority, message):
        self._connect()
        self.message = message
        if self.publish_error:
            self.error = QueueMessageError("A problem occurred when publishing the message to RabbitMQ")
        else:
            self.process_data_events()
        if self.error:
            raise self.error
        return list(sorted(chain(*self.response), key=lambda doc: doc['similarity_index'], reverse=True))

    def on_timeout(self):
        self.error = ProducerTimeoutError(ERROR_PRODUCER_TIMEOUT)

    def on_response(self, ch, method, props, body):
        if self.corr_id != props.correlation_id:
            return
        try:
            if isinstance(body, list):
                self.response.append(body)
            else:
                self.error = ProducerResponseError(body['error'])
        except TypeError:
            self.on_producer_error()

    def on_producer_error(self):
        self.error = ProducerResponseError(ERROR_PRODUCER_RESPONSE)

    def process_data_events(self):
        if self.producer_error:
            self.on_producer_error()
        else:
            self.on_response(self.channel, "", PropertiesMock(self.corr_id),
                             data.create_producer_response(self.offset, self.result_size))
