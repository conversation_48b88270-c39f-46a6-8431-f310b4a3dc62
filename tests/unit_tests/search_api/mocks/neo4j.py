# Local imports
from tests.unit_tests.search_api import data


class Neo4JResultsMock:
    def __init__(self, results):
        self._results = results
        self._dict = dict(enumerate(self._results))

    def peek(self):
        return self._dict.get(0)

    def __iter__(self):
        return iter(self._results)

    def __len__(self):
        return len(self._results)


class NodeMock(object):
    def __init__(self, labels, idx):
        self.labels = labels
        self.id = idx

    def get(self, param):
        return self.__getattribute__(param)


class RelationMock(object):
    def __init__(self, category, cited_phase):
        self.category = category
        self.cited_phase = cited_phase

    def get(self, param):
        return self.__getattribute__(param)


class PublicationNodeMock(NodeMock):
    def __init__(self, ucid, document_id):
        NodeMock.__init__(self, ('Publication',), ucid)
        self.ucid = ucid
        self.family_id = document_id


class NPLNodeMock(NodeMock):
    def __init__(self, idx):
        NodeMock.__init__(self, ('NPL',), idx)


class Neo4jSessionMock:
    def __init__(self, level=1, force_empty=False):
        root = data.search_citation_patent_number
        self.results = []
        self.first_level_nodes = 0
        self.second_level_nodes_factor = 0

        root_node = PublicationNodeMock(root, '1001')
        if not force_empty:
            if level == 1:
                self.results = [(root_node, RelationMock('A', 'ISR'), PublicationNodeMock('EP-1-2', '1002'), True, 1),
                                (root_node, RelationMock(None, 'ISR'), PublicationNodeMock('EP-1-3', '1003'), True, 1),
                                (root_node, RelationMock('A', 'EXA'), PublicationNodeMock('EP-1-4', '1004'), False, 1)]
                self.first_level_nodes = 3
            else:
                self.results = [(root_node, RelationMock('A', 'ISR'), PublicationNodeMock('EP-1-2', '1002'), True, 1),
                                (PublicationNodeMock('EP-1-2', '1002'), RelationMock('D', 'EXA'),
                                 PublicationNodeMock('EP-1-5', '1005'), False, 2),
                                (PublicationNodeMock('EP-1-2', '1002'), RelationMock('D', 'SEA'),
                                 PublicationNodeMock('EP-1-6', '1006'), False, 2),
                                (root_node, RelationMock('C', 'ISR'), PublicationNodeMock('EP-1-3', '1003'), True, 1),
                                (PublicationNodeMock('EP-1-3', '1003'), RelationMock(None, 'EXA'),
                                 PublicationNodeMock('EP-1-2', '1002'), False, 2),
                                (PublicationNodeMock('EP-1-3', '1003'), RelationMock('A', 'SEA'),
                                 PublicationNodeMock('EP-1-7', '1007'), True, 2),
                                (root_node, RelationMock('A', None), PublicationNodeMock('EP-1-4', '1004'), True, 1),
                                (PublicationNodeMock('EP-1-4', '1004'), RelationMock('A', 'SEA'),
                                 PublicationNodeMock('EP-1-8', '1008'), False, 2),
                                (PublicationNodeMock('EP-1-4', '1004'), RelationMock('A', 'SEA'),
                                 PublicationNodeMock('EP-1-9', '1009'), False, 2)]
                self.first_level_nodes = 2
                self.second_level_nodes_factor = 3

    def run(self, _):
        return Neo4JResultsMock(self.results)

    def read_transaction(self, *args):
        return Neo4JResultsMock(self.results)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
