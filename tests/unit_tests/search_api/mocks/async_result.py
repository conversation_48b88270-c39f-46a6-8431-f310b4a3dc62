import datetime

from celery import states


class AsyncResultMock:

    def __init__(self, task_id: str, state, date_done: datetime.datetime = None, result=None):
        self.id = task_id
        self.task_id = task_id
        self.status = state
        self.date_done = date_done
        self.result = result
        self.retries = 0

    def forget(self):
        pass

    def successful(self):
        return self.status == states.SUCCESS

    def failed(self):
        return self.status == states.FAILURE
