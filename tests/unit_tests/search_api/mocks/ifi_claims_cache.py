from http import HTTPStatus
from app.ext.ifi_claims.ifi_claims_response import IFIClaimsResponse


class FakeIFIClaimsCache:

    def __init__(self, *args, **kwargs):
        self.entries = {}

    def check(self, ucid: str, attachment_type: str, path: str = None, thumbnail: bool = False):
        key = self._get_key(ucid, attachment_type, path, thumbnail)
        return self.entries.get(key)

    def store(self, ucid: str, attachment_type: str, ifi_response: IFIClaimsResponse, path: str = None,
              thumbnail: bool = False):
        key = self._get_key(ucid, attachment_type, path, thumbnail)
        if ifi_response.status_code in [HTTPStatus.OK, HTTPStatus.NOT_FOUND, HTTPStatus.NO_CONTENT]:
            self.entries[key] = ifi_response

    @staticmethod
    def _get_key(ucid: str, attachment_type: str, path: str = None, thumbnail: bool = False):
        return f"{ucid}-{attachment_type}-{path}-{thumbnail}"
