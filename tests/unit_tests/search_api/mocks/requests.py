import json


class MockResponse:
    def __init__(self, json_data, status_code, content=b''):
        self.json_data = json_data
        self.status_code = status_code
        self.headers = {}
        if json_data:
            self.headers['content-type'] = 'application/json'
            self.content = json.dumps(json_data).encode()
        else:
            self.headers['content-type'] = 'application/binary'
            self.content = content

    def json(self):
        return self.json_data
