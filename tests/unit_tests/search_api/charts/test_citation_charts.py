from app.services.search.helpers.charts.citation_charts import references, phases, pl_npl, types, graph


class TestCitationReferenceChart:
    DATA = [
        {},
        {"citations": []},
        {"citations": [{"direction": "Citation", "docdb_family_id": 1},
                       {"direction": "Reference", "docdb_family_id": 2},
                       {"direction": "Citation", "docdb_family_id": 3}, {}]},
        {"citations": [{"direction": "Reference", "docdb_family_id": 1},
                       {"direction": "Citation", "docdb_family_id": 2},
                       {"direction": "Reference", "docdb_family_id": 3}, None]},
        {"citations": [{"direction": "Citation", "docdb_family_id": 1},
                       {"direction": "Reference", "docdb_family_id": 2},
                       {"direction": "Citation", "docdb_family_id": 4}]},
        {"citations": [{"reverse": False, "direction": "Citation", "docdb_family_id": 1},
                       {"direction": "Reference", "docdb_family_id": 2}]},
        {"citations": [{"reverse": True, "direction": "Reference", "docdb_family_id": 3}]},
        {"citations": [{"direction": "Bad_Citations", "docdb_family_id": 1}]},
        {"citations": None}
    ]

    def test_citation_reference_chart(self):
        results = references(self.DATA, {})
        assert len(results['dataSource']) == 2
        assert results['dataSource'][0]["name"] == "Citations"
        assert results['dataSource'][0]["y"] == 6
        assert results['dataSource'][0]["families_amount"] == 4
        assert results['dataSource'][1]["name"] == "References"
        assert results['dataSource'][1]["y"] == 5
        assert results['dataSource'][1]["families_amount"] == 3


class TestCitationPhasesChart:
    DATA = [
        {},
        {"citations": []},
        {"citations": [{"cited_phase": "115"}, {"cited_phase": "CH2"}, {"cited_phase": "SEA"}, {}]},
        {"citations": [{"cited_phase": "CH2"}, {"cited_phase": "PRS"}, {"cited_phase": "SEA"}, None]},
        {"citations": [{"cited_phase": "SUP"}, {"cited_phase": "CH2"}, {"cited_phase": "SEA"}]},
        {"citations": [{"reverse": False, "cited_phase": "115"}, {"cited_phase": "SUP"}]},
        {"citations": [{"reverse": True, "cited_phase": "CH2"}]},
        {"citations": [{"cited_phase": "Bad_Citations"}]},
        {"citations": None}
    ]

    def test_citation_phases(self):
        results = phases(self.DATA, {})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0]["value"] == "115"
        assert results['dataSource'][0]["y"] == 2
        assert results['dataSource'][1]["value"] == "CH2"
        assert results['dataSource'][1]["y"] == 3


class TestCitationPlNplChart:
    DATA = [
        {},
        {"citations": []},
        {"citations": [{"type": "Publication"}, {"type": "Publication"}, {"type": "NPL"}, {}]},
        {"citations": [{"type": "NPL"}, {"type": "Publication"}, {"type": "NPL"}, None]},
        {"citations": [{"type": "Publication"}, {"type": "Publication"}, {"type": "NPL"}]},
        {"citations": [{"reverse": False, "type": "Publication"}, {"type": "Publication"}]},
        {"citations": [{"reverse": True, "type": "NPL"}]},
        {"citations": [{"reverse": True, "type": "NPL"}, {"type": "NPL"}]},
        {"citations": [{"type": "Bad_Citations"}]},
        {"citations": None}
    ]

    def test_citation_reference_chart(self):
        results = pl_npl(self.DATA, {})
        assert len(results['datasource']) == 2
        assert results['datasource'][0] == {'name': 'Patents', 'value': 'Publication', 'y': 7}
        assert results['datasource'][1] == {'name': 'Non-patent literature', 'value': 'NPL', 'y': 5}

    def test_citation_reference_chart_without_documents(self):
        results = pl_npl([], {})
        assert len(results['datasource']) == 2
        assert results['datasource'][0] == {'name': 'Patents', 'value': 'Publication', 'y': 0}
        assert results['datasource'][1] == {'name': 'Non-patent literature', 'value': 'NPL', 'y': 0}


class TestCitationTypesChart:
    DATA = [
        {},
        {"citations": []},
        {"citations": [{"category": "Y"}, {"category": "P"}, {"category": "Y"}, {}]},
        {"citations": [{"category": "Y"}, {"category": "P"}, {"category": "X"}, None]},
        {"citations": [{"reverse": False, "category": "Y"}, {"category": "P"}]},
        {"citations": [{"reverse": False, "category": "X"}]},
        {"citations": [{"reverse": True, "category": "Y"}]},
        {"citations": [{"reverse": True, "category": "P"}, {"category": "A"}]},
        {"citations": [{"reverse": False}, {"category": "A"}]},
        {"citations": None}
    ]

    def test_citation_reference_chart(self):
        results = types(self.DATA, {})
        assert len(results['datasource']) == 5
        assert results['datasource'][0] == {'name': 'Y', 'value': 'Y', 'title': 'Particularly relevant documents if '
                                                                                'combined with one or more other '
                                                                                'documents of the same category',
                                            'y': 4}
        assert results['datasource'][-1] == {'name': 'No type defined', 'value': 'NULL', 'title': 'No type defined',
                                             'y': 1}

    def test_citation_reference_chart_without_documents(self):
        results = types([], {})
        assert len(results['datasource']) == 0


class TestCitationGraphChart:
    DATA = [
        {"docdb_family_id": 1, "publn_no_rep": 'CN102942471A', "title": 'Publication 1A', "priority_date": "2013-02-27",
         "publn_date": "2013-02-27"},
        {"docdb_family_id": 2, "publn_no_rep": 'CN102942472A', "title": 'Publication 2A', "priority_date": "2013-02-27",
         "publn_date": "2013-02-27", "citations": []},
        {"docdb_family_id": 3, "publn_no_rep": 'CN102942473A', "title": 'Publication 3A', "priority_date": "2013-02-27",
         "publn_date": "2013-02-27",
         "citations": [
             {"docdb_family_id": 1, "node_id": "n01", "text": "Citation 10", "type": "Publication",
              "direction": "Citation", "level": 1},
             {"docdb_family_id": 2, "node_id": "n02", "text": "Citation 11", "type": "Publication",
              "direction": "Reference", "level": 0},
             {"docdb_family_id": 4, "node_id": "n03", "text": "Citation 12", "type": "NPL", "direction": "Reference",
              "level": 1}],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""],
         'applicants_ifi': ['APPLE', 'FUJITSU']},
        {"docdb_family_id": 4, "publn_no_rep": 'CN102942474A', "title": 'Publication 4A', "priority_date": "2013-02-27",
         "publn_date": "2013-02-27",
         "citations": [
             {"docdb_family_id": 3, "node_id": "n04", "text": "Citation 13", "type": "NPL", "direction": "Citation",
              "level": 0},
             {"docdb_family_id": 2, "node_id": "n05", "text": "Citation 14", "type": "Publication",
              "direction": "Reference", "level": 0},
             {"docdb_family_id": 5, "node_id": "n06", "text": "Citation 15", "type": "NPL", "direction": "Reference",
              "level": 0},
             None],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", None], 'applicants_ifi': ['ZONE LABS', 'IBM']},
        {"docdb_family_id": 5, "publn_no_rep": 'CN102942472A', "title": 'Publication 2A', "priority_date": "2013-02-27",
         "publn_date": "2013-02-27", "citations": None},
    ]

    def test_citation_graph_chart(self):
        results = graph(self.DATA, {})
        assert len(results['datasource']) == 5
        assert len(results['datasource']['edges']) == 6
        assert sorted(results['datasource']['focals']) == ["2", "3", "5"]
        assert results['datasource']['ids'] == ["2", "3", "NPLn03", "4", "NPLn04", "NPLn06"]
        assert results['datasource']['id_texts'] == ["CN102942472A", "CN102942473A", "Citation 12", "CN102942474A",
                                                     "Citation 13", "Citation 15"]
        assert len(results['datasource']['id_tooltip_info']) == 3

    def test_citation_graph_chart_without_data(self):
        results = graph([], {})
        assert len(results['datasource']) == 5
        assert len(results['datasource']['edges']) == 0
        assert len(results['datasource']['focals']) == 0
        assert len(results['datasource']['ids']) == 0
        assert len(results['datasource']['id_texts']) == 0
        assert len(results['datasource']['id_tooltip_info']) == 0
