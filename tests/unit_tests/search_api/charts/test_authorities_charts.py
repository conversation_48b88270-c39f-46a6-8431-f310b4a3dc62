from app.services.search.helpers.charts.authorities_charts import authorities_map, main_authorities


class TestAuthoritiesMap:
    DATA = [
        {},
        {"authorities": []},
        {"authorities": ["EP"]},
        {"authorities": ["EP", "DE"]},
        {"authorities": ["EP", "DE", "FR"]},
        {"authorities": ["EP", "DE", "FR", "NL"]},
        {"authorities": ["EP", "DE", "FR", "NL", ""]},
        {"authorities": ["EP", "DE", "FR", "NL", None]},
        {"authorities": ["EP", "EP", "BE", "IT"]},
        {"authorities": ["PL", "AT"]},
    ]

    DATA_PUBLICATION = [
        {},
        {"publication_number": None},
        {"publication_number": "EP-123456-A1"},
        {"publication_number": "DE-123456-A1"},
        {"publication_number": "FR-123456-A1"},
        {"publication_number": "NL-123456-A1"},
        {"publication_number": "IT-123456-A1"},
        {"publication_number": "BE-123456-A1"},
        {"publication_number": "AT-123456-A1"},
        {"publication_number": "EP-201001-A1"},
    ]

    def test_simple_chart(self):
        results = authorities_map(self.DATA, {})
        assert len(results['dataSource']) == 8
        assert results['dataSource'][0] == ['EP', 7]

    def test_simple_chart_without_documents(self):
        results = authorities_map([], {})
        assert len(results['dataSource']) == 0

    def test_publication_search(self):
        results = authorities_map(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['dataSource']) == 7
        assert results['dataSource'][0] == ['EP', 2]


class TestMainAuthorities:
    DATA = [
        {},
        {"authorities": []},
        {"authorities": ["EP"]},
        {"authorities": ["EP", "DE"]},
        {"authorities": ["EP", "DE", "FR"]},
        {"authorities": ["EP", "DE", "FR", "NL"]},
        {"authorities": ["EP", "DE", "FR", "NL", ""]},
        {"authorities": ["EP", "DE", "FR", "NL", None]},
        {"authorities": ["EP", "EP", "BE", "IT"]},
        {"authorities": ["PL", "AT"]},
    ]

    DATA_PUBLICATION = [
        {},
        {"publication_number": None},
        {"publication_number": "EP-123456-A1"},
        {"publication_number": "FR-123456-A1"},
        {"publication_number": "NL-123456-A1"},
        {"publication_number": "IT-123456-A1"},
        {"publication_number": "AT-123456-A1"},
        {"publication_number": "EP-201001-A1"},
    ]

    def test_simple_chart(self):
        results = main_authorities(self.DATA, {})
        assert len(results['dataSource']) == 8
        assert results['dataSource'][0] == {'name': 'AT', 'value': 1, 'total': 10}
        assert results['dataSource'][3] == {'name': 'EP', 'value': 7, 'total': 10}
        assert results['dataSource'][7] == {'name': 'PL', 'value': 1, 'total': 10}

    def test_simple_chart_without_documents(self):
        results = main_authorities([], {})
        assert len(results['dataSource']) == 0

    def test_publication_number(self):
        results = main_authorities(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0] == {'name': 'AT', 'value': 1, 'total': 8}
        assert results['dataSource'][1] == {'name': 'EP', 'value': 2, 'total': 8}
