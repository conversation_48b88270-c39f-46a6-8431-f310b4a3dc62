from app.services.search.helpers.charts.classification_charts import top_ipc, ipc_time, top_cpc, cpc_time, \
    top_ipc_combination, top_cpc_combination, top_ipc_network, main_ipc_codes, main_cpc_codes


class TestTopIpcCodeChart:
    DATA = [
        {},
        {"ipc": []},
        {"similarity_index": 0, "ipc": []},
        {"similarity_index": 24,
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"similarity_index": 26,
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"similarity_index": 21,
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"similarity_index": 27,
         "ipc": ["G07C11/00", "G06K11/18"]},
        {"similarity_index": 1,
         "ipc": ["G07C11/00"]},
        {"similarity_index": 3,
         "ipc": []},
        {"similarity_index": 99,
         "ipc": ["Bad_IPC_CODE"]},
        {"similarity_index": None,
         "ipc": None}
    ]

    def test_top_ipc_chart(self):
        results = top_ipc(self.DATA, {})
        assert len(results['dataSource']['categories']) == 5
        assert len(results['dataSource']['value']) == 5
        assert results['dataSource']['value'][0]["name"] == results['dataSource']['categories'][0]
        assert results['dataSource']['value'][0]["y"] == 5
        assert results['dataSource']['value'][0]["name"] == "G07C11/00"
        assert results['dataSource']['value'][2]["y"] == 3
        assert results['dataSource']['value'][2]["name"] == "G06F3/023"


class TestIpcTimeHeatmapChart:
    DATA = [
        {},
        {"priority_date": "1986-12-19", "ipc": [], "publn_no_rep": "US5857028A"},
        {"priority_date": "", "ipc": [], "publn_no_rep": "US5889028A"},
        {"priority_date": "2015-12-19", "publn_no_rep": "DE5857028A",
            "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"priority_date": "2015-07-10", "publn_no_rep": "DE2404276A1",
            "ipc": ["G06K11/18", ""]},
        {"priority_date": "2003-12-19", "publn_no_rep": "GG5857028A",
            "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"priority_date": "2003-12-19", "publn_no_rep": "IN5857028A",
            "ipc": ["G07C11/00", "G06K12/18", "G06F3/023"]},
        {"priority_date": "1986-12-19", "publn_no_rep": "CN5857028A",
            "ipc": ["G07C11/00", "G06K13/18"]},
        {"priority_date": "1986-12-19", "publn_no_rep": "US5637028A", "ipc": ["G07C11/00"]},
        {"priority_date": "1986-12-19", "publn_no_rep": "US5858428A", "ipc": ["Bad_IPC_CODE"]},
        {"priority_date": "", "ipc": [], "publn_no_rep": "US5858428A"},
        {"priority_date": "", "ipc": None, "publn_no_rep": None}
    ]

    DATA_PUBLICATION = [
        {},
        {"publn_date": "1986-12-19", "ipc": [], "publn_no_rep": "US5857028A"},
        {"publn_date": "", "ipc": [], "publn_no_rep": "US5889028A"},
        {"publn_date": "2015-12-19", "publn_no_rep": "DE5857028A",
            "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"publn_date": "2015-07-10", "publn_no_rep": "DE2404276A1",
            "ipc": ["G06K11/18", ""]},
        {"publn_date": "2003-12-19", "publn_no_rep": "GG5857028A",
            "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"publn_date": "2003-12-19", "publn_no_rep": "IN5857028A",
            "ipc": ["G07C11/00", "G06K12/18", "G06F3/023"]},
        {"publn_date": "1986-12-19", "publn_no_rep": "CN5857028A",
            "ipc": ["G07C11/00", "G06K13/18"]},
        {"publn_date": "1986-12-19", "publn_no_rep": "US5637028A", "ipc": ["G07C11/00"]},
        {"publn_date": "1986-12-19", "publn_no_rep": "US5858428A", "ipc": ["Bad_IPC_CODE"]},
        {"publn_date": "", "ipc": [], "publn_no_rep": "US5858428A"},
        {"publn_date": "", "ipc": None, "publn_no_rep": None}
    ]

    def test_ipc_time_heatmap_chart(self):
        results = ipc_time(self.DATA, {})
        assert len(results['dataSource']) == 3
        assert results['dataSource'][1]['value'] == 2
        assert results['labelsY'] == [{'class': 'G'}]
        assert results['letters'] == ['G']
        assert results['years'] == ['1986', '2003', '2015']

    def test_ipc_time_heatmap_chart_with_parameters(self):
        results = ipc_time(self.DATA, {"classification_ipc_time": {"level": "G"}})
        assert len(results['dataSource']) == 6
        assert results['dataSource'][1]['value'] == 2
        assert results['labelsY'] == [{'class': 'G07'}, {'class': 'G06'}]
        assert results['letters'] == ['G07', 'G06']
        assert results['years'] == ['1986', '2003', '2015']

        results = ipc_time(self.DATA, {"classification_ipc_time": {"level": "G06"}})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0]['value'] == 2
        assert results['labelsY'] == [{'class': 'G06K'}, {'class': 'G06F'}]
        assert results['letters'] == ['G06K', 'G06F']
        assert results['years'] == ['1986', '2003', '2015']

        results = ipc_time(self.DATA, {"classification_ipc_time": {"level": "G06K"}})
        assert len(results['dataSource']) == 4
        assert results['dataSource'][0]['value'] == 2
        assert results['labelsY'] == [{'class': 'G06K13/00'}, {'class': 'G06K12/00'}, {'class': 'G06K11/00'}]
        assert results['letters'] == ['G06K13/00', 'G06K12/00', 'G06K11/00']
        assert results['years'] == ['1986', '2003', '2015']

        results = ipc_time(self.DATA, {"classification_ipc_time": {"level": "G06K11/00"}})
        assert len(results['dataSource']) == 2
        assert results['dataSource'][0]['value'] == 2
        assert results['labelsY'] == [{'class': 'G06K11/18'}]
        assert results['letters'] == ['G06K11/18']
        assert results['years'] == ['2003', '2015']

        results = ipc_time(self.DATA, {"classification_ipc_time": {"year_level": "1986"}})
        assert results['dataSource'][0]['value'] == 2
        assert results['labelsY'] == [{'class': 'G'}]
        assert results['letters'] == ['G']
        assert results['years'] == ['1986']

        results = ipc_time(self.DATA, {"classification_ipc_time": {"year_level": "1986", "level": "G"}})
        assert results['dataSource'][0]['value'] == 2
        assert results['labelsY'] == [{'class': 'G07'}, {'class': 'G06'}]
        assert results['letters'] == ['G07', 'G06']
        assert results['years'] == ['1986']

    def test_publication_search(self):
        results = ipc_time(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['dataSource']) == 3
        assert results['dataSource'][1]['value'] == 1
        assert results['labelsY'] == [{'class': 'G'}]
        assert results['letters'] == ['G']
        assert results['years'] == ['1986', '2003', '2015']


class TestIpcCombinationChart:
    DATA = [
        {},
        {"similarity_index": 23, "ipc": [], "publn_no_rep": "US5857028A"},
        {"similarity_index": "", "ipc": [], "publn_no_rep": "US5889028A"},
        {"similarity_index": 2, "publn_no_rep": "DE5857028A",
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"similarity_index": 34, "publn_no_rep": "GG5857028A",
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"similarity_index": 32, "publn_no_rep": "IN5857028A",
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"similarity_index": 20, "publn_no_rep": "CN5857028A",
         "ipc": ["G07C11/00", "G06K11/18"]},
        {"similarity_index": 35, "publn_no_rep": "US5637028A", "ipc": ["G07C11/00"]},
        {"similarity_index": "", "ipc": [], "publn_no_rep": "US5858428A"},
        {"similarity_index": 25, "ipc": ["Bad_IPC_CODE"], "publn_no_rep": ""},
        {"similarity_index": "", "ipc": None, "publn_no_rep": None}
    ]

    def test_ipc_combination_chart(self):
        results = top_ipc_combination(self.DATA, {})
        assert len(results['dataSource']["categories"]) == 4
        assert len(results['dataSource']["value"]) == 4
        assert results['dataSource']["categories"][1] == results['dataSource']["value"][1]['name']
        assert results['dataSource']["value"][1]['y'] == 3
        assert results['dataSource']["average"][1] == 22


class TestIpcNetworkChart:
    DATA = [
        {},
        {"ipc": []},
        {"ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"ipc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"ipc": ["G07C11/00", "G06K11/18"]},
        {"ipc": ["G06K11/18", "G07C11/00"]},
        {"ipc": ["G07C11/00"]},
        {"ipc": ["Bad_IPC_CODE", "G06K11/18", "G07C11/00", "G07C11/00"]},
        {"ipc": ["Bad_IPC_CODE", "G06K11/18", "G07C11/00", "G07C12/01"]},
        {"ipc": ["Bad_IPC_CODE", "G07C11/00", "G07C12/01", "G06K11/18"]},
        {"ipc": ["Bad_IPC_CODE", "G06K11/19", "G07C11/01", "G07C12/02"]},
        {"ipc": ["Bad_IPC_CODE", "G06K11/20", "G07C11/02", "G07C12/02"]},
        {"ipc": None}
    ]

    def test_ipc_network_chart(self):
        results = top_ipc_network(self.DATA, {})
        assert len(results['dataSource']) == 2
        assert len(results['dataSource']["edges"]) == 1
        assert len(results['dataSource']["nodes"]) == 2
        assert [8, "G06K11/18", "G07C11/00"] == sorted(results['dataSource']["edges"][0], key=lambda x: str(x))

        assert "G07C11/00" == results['dataSource']["nodes"][0]["id"]
        assert 9 == results['dataSource']["nodes"][0]["frequency"]
        assert "G06K11/18" == results['dataSource']["nodes"][1]["id"]
        assert 8 == results['dataSource']["nodes"][1]["frequency"]

    def test_ipc_network_chart_with_parameters(self):
        results = top_ipc_network(self.DATA, {'classification_top_ipc_network_quantity': 5,
                                              'classification_top_ipc_network_min_connections': 3})
        assert len(results['dataSource']) == 2
        assert len(results['dataSource']["edges"]) == 3
        assert len(results['dataSource']["nodes"]) == 3
        assert [8, 'G06K11/18', 'G07C11/00'] == sorted(results['dataSource']["edges"][0], key=lambda x: str(x))

        assert "G07C11/00" == results['dataSource']["nodes"][0]["id"]
        assert 9 == results['dataSource']["nodes"][0]["frequency"]
        assert "G06K11/18" == results['dataSource']["nodes"][1]["id"]
        assert 8 == results['dataSource']["nodes"][1]["frequency"]

    def test_simple_chart_without_documents(self):
        results = top_ipc_network([], {})
        assert len(results['dataSource']) == 2
        assert results['dataSource']['edges'] == []
        assert results['dataSource']['nodes'] == []


class TestMainIpcCodesChart:
    DATA = [
        {"ipc": []},
        {"ipc": [], "raw_publication_number": "US5857028A"},
        {"raw_publication_number": "DE5857028A",
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"raw_publication_number": "GG5857028A",
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"raw_publication_number": "IN5857028A",
         "ipc": ["G06F21/31", "G06K11/18", "G06F3/023"]},
        {"raw_publication_number": "CN5857028A",
         "ipc": ["G06F21/31", "G06K11/18"]},
        {"raw_publication_number": "US5637028A", "ipc": ["G06F21/31"]},
        {"ipc": [], "raw_publication_number": "US5858428A"},
        {"ipc": ["Bad_IPC_CODE"], "raw_publication_number": ""},
        {"ipc": None, "raw_publication_number": None}
    ]

    DATA_PUBLICATION = [
        {"ipc": []},
        {"ipc": [], "publication_number": "US5857028A"},
        {"publication_number": "DE5857028A",
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"publication_number": "GG5857028A",
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"publication_number": "IN5857028A",
         "ipc": ["G06F21/31", "G06K11/18", "G06F3/023"]},
        {"publication_number": "CN5857028A",
         "ipc": ["G06F21/31", "G06K11/18"]},
        {"publication_number": "US5637028A", "ipc": ["G06F21/31"]},
        {"ipc": [], "publication_number": "US5858428A"},
        {"ipc": ["Bad_IPC_CODE"], "publication_number": ""},
        {"ipc": None, "publication_number": None}
    ]

    def test_main_ipc_codes_chart(self):
        results = main_ipc_codes(self.DATA, {})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0]["value"] == 5
        assert results['dataSource'][0]["name"] == 'G06F21/31'

    def test_publication_search(self):
        results = main_ipc_codes(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0]["value"] == 5
        assert results['dataSource'][0]["name"] == 'G06F21/31'


class TestTopCpcCodeChart:
    DATA = [
        {},
        {"cpc": []},
        {"similarity_index": 0, "cpc": []},
        {"similarity_index": 45,
         "cpc": ["C08B37/0012", "H04M2250/22", "Y10S600/921", "A61K9/0092", "G06K9/00013", ""]},
        {"similarity_index": 59,
         "cpc": ["C08B37/0012", "H04M2250/22", "Y10S600/921", "A61B1/05", None]},
        {"similarity_index": 61,
         "cpc": ["G06K9/00013", "H04M2250/22", "A61B1/05"]},
        {"similarity_index": 52,
         "cpc": ["G06K9/00013", "A61K9/0092"]},
        {"similarity_index": 2,
         "cpc": ["C08B37/0012"]},
        {"similarity_index": 39,
         "cpc": []},
        {"similarity_index": 39,
         "cpc": ["Bad_CPC_CODE"]},
        {"similarity_index": None,
         "cpc": None}
    ]

    def test_top_cpc_chart(self):
        results = top_cpc(self.DATA, {})
        assert len(results['dataSource']['categories']) == 6
        assert len(results['dataSource']['value']) == 6
        assert results['dataSource']['value'][0]["name"] == results['dataSource']['categories'][0]
        assert results['dataSource']['value'][0]["y"] == 3
        assert results['dataSource']['value'][0]["name"] == "C08B37/0012"
        assert results['dataSource']['value'][4]["y"] == 2
        assert results['dataSource']['value'][4]["name"] == "A61K9/0092"


class TestCpcTimeHeatmapChart:
    DATA = [
        {},
        {"cpc": []},
        {"priority_date": "1986-02-04", "cpc": [], "publn_no_rep": "WP5858428A"},
        {"priority_date": "1992-04-15", "publn_no_rep": "DE5858428A",
         "cpc": ["C08B37/0012", "H04M2250/22", "Y10S600/921", "A61K9/0092", "G06K9/00013", ""]},
        {"priority_date": "1984-08-30", "publn_no_rep": "DE2404276A1", "cpc": ["C08B38/0012", ""]},
        {"priority_date": "1984-11-19", "publn_no_rep": "FH5858428A",
         "cpc": ["C08B38/0012", "H04M2250/22", "Y10S600/921", "A61B1/05", None]},
        {"priority_date": "1984-09-18", "publn_no_rep": "OP5858428A",
         "cpc": ["G06K9/00013", "H04M2250/22", "A61B1/05"]},
        {"priority_date": "2019-02-01", "publn_no_rep": "BS5856428A",
         "cpc": ["G06K9/00013", "A61K9/0092"]},
        {"priority_date": "2004-01-25", "publn_no_rep": "DS5358428A", "cpc": ["C08B37/0012", ]},
        {"priority_date": "2019-10-23", "publn_no_rep": "DE5857828A", "cpc": []},
        {"priority_date": "2019-10-23", "publn_no_rep": "DE5857828A", "cpc": ["Bad_CPC_CODE"]},
        {"priority_date": None, "cpc": None}
    ]

    DATA_PUBLICATION = [
        {},
        {"cpc": []},
        {"publn_date": "1986-02-04", "cpc": [], "publn_no_rep": "WP5858428A"},
        {"publn_date": "1992-04-15", "publn_no_rep": "DE5858428A",
         "cpc": ["C08B37/0012", "H04M2250/22", "Y10S600/921", "A61K9/0092", "G06K9/00013", ""]},
        {"publn_date": "1984-08-30", "publn_no_rep": "DE2404276A1", "cpc": ["C08B38/0012", ""]},
        {"publn_date": "1984-11-19", "publn_no_rep": "FH5858428A",
         "cpc": ["C08B38/0012", "H04M2250/22", "Y10S600/921", "A61B1/05", None]},
        {"publn_date": "1984-09-18", "publn_no_rep": "OP5858428A",
         "cpc": ["G06K9/00013", "H04M2250/22", "A61B1/05"]},
        {"publn_date": "2019-02-01", "publn_no_rep": "BS5856428A",
         "cpc": ["G06K9/00013", "A61K9/0092"]},
        {"publn_date": "2004-01-25", "publn_no_rep": "DS5358428A", "cpc": ["C08B37/0012", ]},
        {"publn_date": "2019-10-23", "publn_no_rep": "DE5857828A", "cpc": []},
        {"publn_date": "2019-10-23", "publn_no_rep": "DE5857828A", "cpc": ["Bad_CPC_CODE"]},
        {"publn_date": None, "cpc": None}
    ]

    def test_cpc_time_heatmap_chart(self):
        results = cpc_time(self.DATA, {})
        assert len(results['dataSource']) == 13
        assert results['dataSource'][5]['value'] == 2
        assert results['labelsY'] == [{'class': 'Y'}, {'class': 'H'}, {'class': 'G'}, {'class': 'C'}, {'class': 'A'}]
        assert results['letters'] == ['Y', 'H', 'G', 'C', 'A']
        assert results['years'] == ['1984', '1992', '2004', '2019']

    def test_cpc_time_heatmap_chart_with_parameters(self):
        results = cpc_time(self.DATA, {"classification_cpc_time": {"year_level": "1984"}})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0]['value'] == 2
        assert results['labelsY'] == [{'class': 'Y'}, {'class': 'H'}, {'class': 'G'}, {'class': 'C'}, {'class': 'A'}]
        assert results['letters'] == ['Y', 'H', 'G', 'C', 'A']
        assert results['years'] == ['1984']

        results = cpc_time(self.DATA, {"classification_cpc_time": {"level": "C"}})
        assert len(results['dataSource']) == 3
        assert results['dataSource'][1]['value'] == 2
        assert results['labelsY'] == [{'class': 'C08'}]
        assert results['letters'] == ['C08']
        assert results['years'] == ['1984', '1992', '2004']

        results = cpc_time(self.DATA, {"classification_cpc_time": {"level": "C08"}})
        assert len(results['dataSource']) == 3
        assert results['dataSource'][1]['value'] == 2
        assert results['labelsY'] == [{'class': 'C08B'}]
        assert results['letters'] == ['C08B']
        assert results['years'] == ['1984', '1992', '2004']

        results = cpc_time(self.DATA, {"classification_cpc_time": {"level": "C08B"}})
        assert len(results['dataSource']) == 3
        assert results['dataSource'][1]['value'] == 2
        assert results['labelsY'] == [{'class': 'C08B38/00'}, {'class': 'C08B37/00'}]
        assert results['letters'] == ['C08B38/00', 'C08B37/00']
        assert results['years'] == ['1984', '1992', '2004']

        results = cpc_time(self.DATA, {"classification_cpc_time": {"year_level": "1984", "level": "C08"}})
        assert len(results['dataSource']) == 1
        assert results['dataSource'][0]['value'] == 2
        assert results['labelsY'] == [{'class': 'C08B'}]
        assert results['letters'] == ['C08B']
        assert results['years'] == ['1984']

    def test_publication_search(self):
        results = cpc_time(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['dataSource']) == 13
        assert results['dataSource'][5]['value'] == 1
        assert results['labelsY'] == [{'class': 'Y'}, {'class': 'H'}, {'class': 'G'}, {'class': 'C'}, {'class': 'A'}]
        assert results['letters'] == ['Y', 'H', 'G', 'C', 'A']
        assert results['years'] == ['1984', '1992', '2004', '2019']


class TestCpcCombinationChart:
    DATA = [
        {},
        {"similarity_index": 23, "cpc": [], "publn_no_rep": "US5857028A"},
        {"similarity_index": 21, "cpc": [], "publn_no_rep": "US5889028A"},
        {"similarity_index": 34, "publn_no_rep": "DE5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"similarity_index": 11, "publn_no_rep": "GG5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"similarity_index": 14, "publn_no_rep": "IN5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"similarity_index": 10, "publn_no_rep": "CN5857028A",
         "cpc": ["G07C11/00", "G06K11/18"]},
        {"similarity_index": 22, "publn_no_rep": "US5637028A", "cpc": ["G07C11/00"]},
        {"similarity_index": 21, "cpc": [], "publn_no_rep": "US5858428A"},
        {"similarity_index": 0, "cpc": ["Bad_IPC_CODE"], "publn_no_rep": ""},
        {"similarity_index": None, "cpc": None, "publn_no_rep": None}
    ]

    def test_cpc_combination_chart(self):
        results = top_cpc_combination(self.DATA, {})
        assert len(results['dataSource']["categories"]) == 4
        assert len(results['dataSource']["value"]) == 4
        assert results['dataSource']["categories"][1] == results['dataSource']["value"][1]['name']
        assert results['dataSource']["value"][1]['y'] == 3
        assert results['dataSource']["average"][1] == 19


class TestMainCpcCodesChart:
    DATA = [
        {},
        {"cpc": [], "raw_publication_number": "US5889028A"},
        {"raw_publication_number": "DE5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"raw_publication_number": "GG5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"raw_publication_number": "IN5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"raw_publication_number": "CN5857028A",
         "cpc": ["G07C11/00", "G06K11/18"]},
        {"raw_publication_number": "US5637028A", "cpc": ["G07C11/00"]},
        {"cpc": [], "raw_publication_number": "US5858428A"},
        {"cpc": ["Bad_IPC_CODE"], "raw_publication_number": ""},
        {"cpc": None, "raw_publication_number": None}
    ]

    DATA_PUBLICATION = [
        {},
        {"cpc": [], "publication_number": "US5889028A"},
        {"publication_number": "DE5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"publication_number": "GG5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"publication_number": "IN5857028A",
         "cpc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"publication_number": "CN5857028A",
         "cpc": ["G07C11/00", "G06K11/18"]},
        {"publication_number": "US5637028A", "cpc": ["G07C11/00"]},
        {"cpc": [], "publication_number": "US5858428A"},
        {"cpc": ["Bad_IPC_CODE"], "publication_number": ""},
        {"cpc": None, "publication_number": None}
    ]

    def test_main_cpc_codes_chart(self):
        results = main_cpc_codes(self.DATA, {})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0]["value"] == 2
        assert results['dataSource'][0]["name"] == 'G06F21/31'

    def test_publication_search(self):
        results = main_cpc_codes(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['dataSource']) == 5
        assert results['dataSource'][0]["value"] == 2
        assert results['dataSource'][0]["name"] == 'G06F21/31'
