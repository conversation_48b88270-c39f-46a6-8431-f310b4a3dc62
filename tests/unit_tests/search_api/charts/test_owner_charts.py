from app.services.search.helpers.charts.owner_charts import main_owners


def ultimate_owner(ultimate_owner_list):

    return [{
        "id": 24852,
        "name": owner,
        "root_id": 24852
    } for owner in ultimate_owner_list]


class TestMainOwnersChart:
    DATA = [
        {},
        {'ultimate_owners': ultimate_owner(['FUJITSU']), 'priority_date': '2003-02-27'},
        {'ultimate_owners': ultimate_owner(['FUJITSU']), 'priority_date': None},
        {'ultimate_owners': [], 'priority_date': None},
        {'ultimate_owners': None, 'priority_date': None},
        {'ultimate_owners': ultimate_owner(['APPLE', 'APPLE']), 'priority_date': '2016-06-11'},
        {'assignees_ifi': ['APPLE', 'FUJITSU', None], 'priority_date': '2016-06-11'},
        {'assignees_ifi': ['APPLE', None], 'priority_date': '2016-06-11'},
        {'assignees_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS'],
         'priority_date': '2011-05-04'},
        {'assignees_original': ['ESIGN'], 'priority_date': '1996-12-04'},
        {'assignees_original': ['YNGIUN WANG'], 'priority_date': '1996-12-04'},
        {'assignees_original': ['FUJITSU'], 'priority_date': '2002-08-08'},
        {'assignees_original': ['SPODAK DOUGLAS', 'GONOW'], 'priority_date': '2010-03-02'},
        {'assignees_original': ['APPLE'], 'priority_date': '2016-06-11'},
        {'assignees_original': ['APPLE', 'ESIGN'], 'priority_date': '2016-05-02'},
        {'assignees_original': ['LARACEY KEVIN'], 'priority_date': '2010-04-09'}
    ]

    DATA_PUBLICATION = [
        {},
        {'ultimate_owners': ultimate_owner(['FUJITSU']), 'priority_date': '2003-02-27'},
        {'ultimate_owners': ultimate_owner(['FUJITSU']), 'priority_date': None},
        {'ultimate_owners': [], 'priority_date': None},
        {'ultimate_owners': None, 'priority_date': None},
        {'ultimate_owners': ultimate_owner(['APPLE', 'APPLE']), 'priority_date': '2016-06-11'},
        {'assignees_ifi': ['APPLE', 'FUJITSU', None], 'priority_date': '2016-06-11'},
        {'assignees_ifi': ['APPLE', None], 'priority_date': '2016-06-11'},
        {'assignees_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS'],
         'priority_date': '2011-05-04'},
        {'assignees_ifi': ['ESIGN'], 'priority_date': '1996-12-04'},
        {'assignees_ifi': ['YNGIUN WANG'], 'priority_date': '1996-12-04'},
        {'assignees_original': ['FUJITSU'], 'priority_date': '2002-08-08'},
        {'assignees_original': ['SPODAK DOUGLAS', 'GONOW'], 'priority_date': '2010-03-02'},
        {'assignees_original': ['APPLE'], 'priority_date': '2016-06-11'},
        {'assignees_original': ['APPLE', 'ESIGN'], 'priority_date': '2016-05-02'},
        {'assignees_original': ['LARACEY KEVIN'], 'priority_date': '2010-04-09'}
    ]

    def test_simple_chart(self):

        results = main_owners(self.DATA, {})
        assert len(results['datasource']) == 10
        assert results['datasource'][0]['total'] == len(self.DATA)
        assert results['datasource'][0]['value'] == 5
        assert results['datasource'][0]['name'] == 'APPLE'

    def test_simple_chart_without_documents(self):
        results = main_owners([], {})
        assert len(results['datasource']) == 0

    def test_with_parameters(self):
        results = main_owners(self.DATA, {"main_owners_quantity": 7})
        assert len(results['datasource']) == 7
        assert results['datasource'][0]['total'] == len(self.DATA)
        assert results['datasource'][0]['value'] == 5
        assert results['datasource'][0]['name'] == 'APPLE'

    def test_publication_search(self):
        results = main_owners(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 10
        assert results['datasource'][0]['total'] == len(self.DATA_PUBLICATION)
        assert results['datasource'][0]['value'] == 5
        assert results['datasource'][0]['name'] == 'APPLE'
