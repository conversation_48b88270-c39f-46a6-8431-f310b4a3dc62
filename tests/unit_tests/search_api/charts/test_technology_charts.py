from app.services.search.helpers.charts.technology_charts import technology_fields


class TestTechnologyFields:
    DATA = [
        {'tech_fields': ['Digital Communication', 'Computer Technology']},
        {'tech_fields': ['Computer Technology', 'Measurement', 'Control']},
        {'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']},
        {'tech_fields': ['Transport']},
        {'tech_fields': ['Macromolecular Chemistry, Polymers', 'Pharmaceuticals']},
        {'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'tech_fields': ['IT Methods for Management', 'Computer Technology']},
        {'tech_fields': ['Textile and Paper Machines']},
        {'tech_fields': ['Optics']},
        {'tech_fields': ['Basic Materials Chemistry', 'Macromolecular Chemistry, Polymers',
                         'Organic Fine Chemistry']},
        {'similarity_index': None},
    ]

    def test_simple_chart(self):
        results = technology_fields(self.DATA, {})
        assert len(results['datasource']) == 35
        assert results['datasource'][0]['total'] == len(self.DATA)
        assert next(i for i in results['datasource'] if i["name"] == "biotechnology")['value'] == 0
        assert next(i for i in results['datasource'] if i["name"] == "measurement")['value'] == 1
        assert next(i for i in results['datasource'] if i["name"] == "macromolecular chemistry, polymers")['value'] == 4

    def test_simple_chart_without_documents(self):
        results = technology_fields([], {})
        assert len(results['datasource']) == 35
        assert results['datasource'][0]['total'] == 0
        assert next(i for i in results['datasource'] if i["name"] == "biotechnology")['value'] == 0
        assert next(i for i in results['datasource'] if i["name"] == "measurement")['value'] == 0
        assert next(i for i in results['datasource'] if i["name"] == "macromolecular chemistry, polymers")['value'] == 0
