from app.services.search.helpers.charts.trend_charts import trend_analytic_radar, trend_collaboration_competition, \
    trend_applicant_collaboration_network


class TestTrendAnalyticRadarChart:
    DATA = [
        {},
        {'impact': None, 'risk': None, 'recency_years': None, 'consistency': None, 'citation_forward_count': None,
         'citation_backward_count': None, 'market_coverage': None, 'tech_fields': None},
        {'impact': None, 'risk': None, 'recency_years': None, 'consistency': None, 'citation_forward_count': None,
         'citation_backward_count': None, 'market_coverage': None, 'tech_fields': []},
        {'impact': 1, 'risk': 1, 'recency_years': 4.3972602, 'consistency': 0.5812874, 'citation_forward_count': 1,
         'citation_backward_count': 5, 'market_coverage': 2,
         'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Computer Technology', 'Control']},
        {'impact': 2, 'risk': 0, 'recency_years': 9.9424658, 'consistency': 0.7669489, 'citation_forward_count': 2,
         'citation_backward_count': 6, 'market_coverage': 1,
         'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']},
        {'impact': 3, 'risk': 1, 'recency_years': 2.3150685, 'consistency': 0.7235762, 'citation_forward_count': 3,
         'citation_backward_count': 7, 'market_coverage': 4,
         'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Computer Technology', 'Control']},
        {'impact': 4, 'risk': 2, 'recency_years': 6.7671232, 'consistency': 0.92614406, 'citation_forward_count': 4,
         'citation_backward_count': 8, 'market_coverage': 8,
         'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Computer Technology', 'Control']},
    ]

    STATISTIC_DATA = {
        "average_also_published_as": 0.051272285555180465,
        "average_market_coverage": 3.100852799406748,
        "average_impact": 1.175686274509804,
        "average_risk": 0.8584313725490196,
        "average_recency_years": 6.279880169087013,
        "average_citation_forward_count": 0,
        "average_consistency": 0.4838892936517958,
        "average_citation_backward_count": 0
    }

    def test_simple_chart(self, mocker):
        mocker.patch('app.extensions.StatisticExtension.perform_statistics_search',
                     return_value=self.STATISTIC_DATA)
        mocker.patch('app.extensions.StatisticExtension.get_cached_search',
                     return_value=None)
        results = trend_analytic_radar(self.DATA, {})
        assert len(results['datasource']) == 3
        assert results['datasource']['technology_average'] == [1.43, 0.57, 3.35, 0.43, 2.14, 1.43, 3.71, 2.14]
        assert results['datasource']['universe_average'] == [1.18, 0.86, 6.28, 0.48, 0.0, 0.0, 0.0, 3.1]
        assert results['datasource']['max_average'] == [1.43, 0.86, 6.28, 0.48, 2.14, 1.43, 3.71, 3.1]

    def test_simple_chart_without_documents(self, mocker):
        mocker.patch('app.extensions.StatisticExtension.perform_statistics_search',
                     return_value=self.STATISTIC_DATA)
        mocker.patch('app.extensions.StatisticExtension.get_cached_search',
                     return_value=None)
        results = trend_analytic_radar([], {})
        assert len(results['datasource']) == 3
        assert results['datasource']['technology_average'] == [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
        assert results['datasource']['universe_average'] == [1.18, 0.86, 6.28, 0.48, 0.0, 0.0, 0.0, 3.1]
        assert results['datasource']['max_average'] == results['datasource']['universe_average']


class TestTrendCollaborationCompetitionChart:
    DATA = [
        {},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2003-02-27'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': None},
        {'applicants_ifi': [], 'priority_date': None},
        {'applicants_ifi': None, 'priority_date': None},
        {'applicants_ifi': ['APPLE', 'APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'FUJITSU', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS'],
         'priority_date': '2011-05-04'},
        {'applicants_ifi': ['ESIGN'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['YNGIUN WANG'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2002-08-08'},
        {'applicants_ifi': ['SPODAK DOUGLAS', 'GONOW'], 'priority_date': '2010-03-02'},
        {'applicants_ifi': ['APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'ESIGN'], 'priority_date': '2016-05-02'},
        {'applicants_ifi': ['LARACEY KEVIN'], 'priority_date': '2010-04-09'}
    ]

    def test_simple_chart(self):
        results = trend_collaboration_competition(self.DATA, {})
        assert len(results['datasource']) == 3
        assert results['datasource']['collaboration'][0] == {'y': 2, 'publications': 2, 'year': 2016,
                                                             'name': 'Collaboration'}
        assert results['datasource']['collaboration'][-1] == {'y': 0, 'publications': 0, 'year': 1996,
                                                              'name': 'Collaboration'}
        assert results['datasource']['competition'][0] == {'y': 3, 'publications': 5, 'year': 2016,
                                                           'name': 'Competition'}
        assert results['datasource']['competition'][-1] == {'y': 2, 'publications': 2, 'year': 1996,
                                                            'name': 'Competition'}
        assert results['datasource']['years'] == [2016, 2011, 2010, 2003, 2002, 1996]

    def test_simple_chart_without_documents(self):
        results = trend_collaboration_competition([], {})
        assert len(results['datasource']) == 3
        assert results['datasource']['collaboration'] == []
        assert results['datasource']['competition'] == []
        assert results['datasource']['years'] == []


class TestTrendApplicantCollaborationNetworkChart:
    DATA = [
        {},
        {'applicants_ifi': ['FUJITSU']},
        {'applicants_ifi': ['FUJITSU']},
        {'applicants_ifi': []},
        {'applicants_ifi': None},
        {'applicants_ifi': ['APPLE', 'APPLE']},
        {'applicants_ifi': ['APPLE', 'FUJITSU', None]},
        {'applicants_ifi': ['ESIGN', 'APPLE']},
        {'applicants_ifi': ['FUJITSU', 'APPLE']},
        {'applicants_ifi': ['APPLE', 'FUJITSU', 'LUMBER LABS']},
        {'applicants_ifi': ['APPLE', 'FUJITSU', 'GONOW']},
        {'applicants_ifi': ['APPLE', None]},
        {'applicants_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS']},
        {'applicants_ifi': ['ESIGN']},
        {'applicants_ifi': ['YNGIUN WANG']},
        {'applicants_ifi': ['FUJITSU']},
        {'applicants_ifi': ['SPODAK DOUGLAS', 'GONOW']},
        {'applicants_ifi': ['APPLE']},
        {'applicants_ifi': ['LARACEY KEVIN']}
    ]

    def test_simple_chart(self):
        results = trend_applicant_collaboration_network(self.DATA, {})
        assert len(results['datasource']) == 2
        assert len(results['datasource']['value']) == 20
        assert results['datasource']['value'][0]['value'] == 4

    def test_simple_chart_with_parameters(self):
        results = trend_applicant_collaboration_network(self.DATA,
                                                        {'trend_applicant_collaboration_network_quantity': 3})
        assert len(results['datasource']) == 2
        assert len(results['datasource']['value']) == 4
        assert results['datasource']['value'][0]['value'] == 4

    def test_simple_chart_without_documents(self):
        results = trend_applicant_collaboration_network([], {})
        assert len(results['datasource']) == 2
        assert results['datasource']['value'] == []
