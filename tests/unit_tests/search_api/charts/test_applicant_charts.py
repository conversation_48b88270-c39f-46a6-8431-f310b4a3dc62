from app.services.search.helpers.charts.applicant_charts import main_applicants, number_of_applicant_over_time


class TestMainApplicantsChart:
    DATA = [
        {},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2003-02-27'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': None},
        {'applicants_ifi': [], 'priority_date': None},
        {'applicants_ifi': None, 'priority_date': None},
        {'applicants_ifi': ['APPLE', 'APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'FUJITSU', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS'],
         'priority_date': '2011-05-04'},
        {'applicants_ifi': ['ESIGN'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['YNGIUN WANG'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2002-08-08'},
        {'applicants_ifi': ['SPODAK DOUGLAS', 'GONOW'], 'priority_date': '2010-03-02'},
        {'applicants_ifi': ['APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'ESIGN'], 'priority_date': '2016-05-02'},
        {'applicants_ifi': ['LARACEY KEVIN'], 'priority_date': '2010-04-09'}
    ]

    DATA_PUBLICATION = [
        {},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2003-02-27'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': None},
        {'applicants_ifi': [], 'priority_date': None},
        {'applicants_ifi': None, 'priority_date': None},
        {'applicants_ifi': ['APPLE', 'APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'FUJITSU', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS'],
         'priority_date': '2011-05-04'},
        {'applicants_ifi': ['ESIGN'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['YNGIUN WANG'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2002-08-08'},
        {'applicants_ifi': ['SPODAK DOUGLAS', 'GONOW'], 'priority_date': '2010-03-02'},
        {'applicants_ifi': ['APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'ESIGN'], 'priority_date': '2016-05-02'},
        {'applicants_ifi': ['LARACEY KEVIN'], 'priority_date': '2010-04-09'}
    ]

    def test_simple_chart(self):
        results = main_applicants(self.DATA, {})
        assert len(results['datasource']) == 11
        assert results['datasource'][0]['total'] == len(self.DATA)
        assert results['datasource'][0]['value'] == 5
        assert results['datasource'][0]['name'] == 'APPLE'

    def test_simple_chart_without_documents(self):
        results = main_applicants([], {})
        assert len(results['datasource']) == 0

    def test_with_parameters(self):
        results = main_applicants(self.DATA, {"main_applicants_quantity": 7})
        print(results)
        assert len(results['datasource']) == 7
        assert results['datasource'][0]['total'] == len(self.DATA)
        assert results['datasource'][0]['value'] == 5
        assert results['datasource'][0]['name'] == 'APPLE'

    def test_publication_search(self):
        results = main_applicants(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 11
        assert results['datasource'][0]['total'] == len(self.DATA_PUBLICATION)
        assert results['datasource'][0]['value'] == 5
        assert results['datasource'][0]['name'] == 'APPLE'


class TestNumberOfApplicantsOverTimeChart:
    DATA = [
        {},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2003-02-27'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': None},
        {'applicants_ifi': [], 'priority_date': None},
        {'applicants_ifi': None, 'priority_date': None},
        {'applicants_ifi': ['APPLE', 'APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'FUJITSU', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', None], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS'],
         'priority_date': '2011-05-04'},
        {'applicants_ifi': ['ESIGN'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['YNGIUN WANG'], 'priority_date': '1996-12-04'},
        {'applicants_ifi': ['FUJITSU'], 'priority_date': '2002-08-08'},
        {'applicants_ifi': ['SPODAK DOUGLAS', 'GONOW'], 'priority_date': '2010-03-02'},
        {'applicants_ifi': ['APPLE'], 'priority_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'ESIGN'], 'priority_date': '2016-05-02'},
        {'applicants_ifi': ['LARACEY KEVIN'], 'priority_date': '2010-04-09'}
    ]

    DATA_PUBLICATION = [
        {},
        {'applicants_ifi': ['FUJITSU'], 'publn_date': '2003-02-27'},
        {'applicants_ifi': ['FUJITSU'], 'publn_date': None},
        {'applicants_ifi': [], 'publn_date': None},
        {'applicants_ifi': None, 'publn_date': None},
        {'applicants_ifi': ['APPLE', 'APPLE'], 'publn_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'FUJITSU', None], 'publn_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', None], 'publn_date': '2016-06-11'},
        {'applicants_ifi': ['METTLER MICHAEL', 'SNYDER JOSH BLEECHER', 'LUMBER LABS'],
         'publn_date': '2011-05-04'},
        {'applicants_ifi': ['ESIGN'], 'publn_date': '1996-12-04'},
        {'applicants_ifi': ['YNGIUN WANG'], 'publn_date': '1996-12-04'},
        {'applicants_ifi': ['FUJITSU'], 'publn_date': '2002-08-08'},
        {'applicants_ifi': ['SPODAK DOUGLAS', 'GONOW'], 'publn_date': '2010-03-02'},
        {'applicants_ifi': ['APPLE'], 'publn_date': '2016-06-11'},
        {'applicants_ifi': ['APPLE', 'ESIGN'], 'publn_date': '2016-05-02'},
        {'applicants_ifi': ['LARACEY KEVIN'], 'publn_date': '2010-04-09'}
    ]

    def test_simple_chart(self):
        results = number_of_applicant_over_time(self.DATA, {})
        assert len(results['datasource']) == 6
        assert results['datasource'][0]['x'] == 1996
        assert results['datasource'][0]['y'] == 2
        assert results['datasource'][5]['x'] == 2016
        assert results['datasource'][5]['y'] == 3

    def test_simple_chart_without_documents(self):
        results = number_of_applicant_over_time([], {})
        assert len(results['datasource']) == 0

    def test_publication_search(self):
        results = number_of_applicant_over_time(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 6
        assert results['datasource'][0]['x'] == 1996
        assert results['datasource'][0]['y'] == 2
        assert results['datasource'][5]['x'] == 2016
        assert results['datasource'][5]['y'] == 3
