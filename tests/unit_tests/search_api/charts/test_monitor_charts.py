from app.services.search.helpers.charts.monitor_charts import monitor_venn_diagram


class TestMonitorChart:
    SIMPLE_DATA = [
        {},
        {'snapshots': [{'type': 'SEMANTIC'}]},
        {'snapshots': [{'type': 'BOOLEAN'}]},
        {'snapshots': [{'type': 'CITATION'}]},
        {'snapshots': [{'type': 'MACHINE_LEARNING'}]},
    ]

    COMPLEX_DATA = [
        {'snapshots': [{'type': 'SEMANTIC'}, {'type': 'BOOLEAN'}]},
        {'snapshots': [{'type': 'BOOLEAN'}, {'type': 'SEMANTIC'}]},
        {'snapshots': [{'type': 'CITATION'}, {'type': 'MACHINE_LEARNING'}]},
        {'snapshots': [{'type': 'BOOLEAN'}, {'type': 'SEMANTIC'}, {'type': 'CITATION'}]},
        {'snapshots': [{'type': 'BOOLEAN'}, {'type': 'SEMANTIC'}, {'type': 'CITATION'}, {'type': 'MACHINE_LEARNING'}]}
    ]

    def test_simple_chart(self, mocker):
        results = monitor_venn_diagram(self.SIMPLE_DATA, {})
        assert results['datasource'] == [
            {'value': 1, 'tooltipValue': 1, 'tooltiptitle': 'BOOLEAN', 'sets': ['BOOLEAN']},
            {'value': 1, 'tooltipValue': 1, 'tooltiptitle': 'SEMANTIC', 'sets': ['SEMANTIC']},
            {'value': 1, 'tooltipValue': 1, 'tooltiptitle': 'CITATION', 'sets': ['CITATION']},
            {'value': 1, 'tooltipValue': 1, 'tooltiptitle': 'DEEP LEARNING',
             'sets': ['DEEP LEARNING']}]

    def test_simple_chart_complex_documents(self, mocker):
        results = monitor_venn_diagram(self.COMPLEX_DATA, {})
        data = results['datasource']
        assert list(filter(lambda x: x['sets'] == ['SEMANTIC'], data))[0] == \
               {'value': 4, 'tooltipValue': 0, 'tooltiptitle': 'SEMANTIC', 'sets': ['SEMANTIC']}
        assert list(filter(lambda x: x['sets'] == ['BOOLEAN', 'SEMANTIC'], data))[0] == \
               {'value': 4, 'tooltipValue': 2, 'tooltiptitle': 'BOOLEAN & SEMANTIC', 'sets': ['BOOLEAN', 'SEMANTIC']}
        assert list(filter(lambda x: x['sets'] == ['CITATION', 'DEEP LEARNING'], data))[0] == \
               {'value': 2, 'tooltipValue': 1, 'tooltiptitle': 'CITATION & DEEP LEARNING',
                'sets': ['CITATION', 'DEEP LEARNING']}
        assert list(filter(lambda x: x['sets'] == ['BOOLEAN', 'SEMANTIC', 'CITATION', 'DEEP LEARNING'], data))[0] == \
               {'value': 1, 'tooltipValue': 1, 'tooltiptitle': 'BOOLEAN & SEMANTIC & CITATION & DEEP LEARNING',
                'sets': ['BOOLEAN', 'SEMANTIC', 'CITATION', 'DEEP LEARNING']}

    def test_simple_chart_without_documents(self, mocker):
        results = monitor_venn_diagram([], {})
        assert len(results['datasource']) == 0
