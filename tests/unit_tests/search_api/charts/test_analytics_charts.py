import pytest

from app.services.search.helpers.charts.analytics_charts import patents_value, technological_broadness_indication, \
    analytics_recency_indication, analytics_patent_family, analytics_consistency_indication, \
    analytics_citations_references, min_max_values, green_families_timeline, analytics_legal_status, green_categories, \
    analytics_novelty_foundation, green_technologies_landscape, green_ranking, patents_risk, \
    analytics_citations_count, analytics_references_count, patent_value_over_risk


class TestTechnologicalBroadnessIndication:
    DATA = [
        {'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Computer Technology']},
        {'tech_fields': ['IT Methods for Management', 'Digital Communication']},
        {'tech_fields': ['IT Methods for Management']},
        {'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Computer Technology']},
    ]

    DATA_MISSING = [
        {},
        {"tech_fields": []},
        {"tech_fields": None},
    ]

    DATA_LARGE = [
        {'tech_fields': ['IT Methods for Management']} for _ in range(10000)
    ]

    def test_simple_chart(self):
        results = technological_broadness_indication(self.DATA, {})
        assert len(results['frequency']) == len(results['values']) == 3
        assert results['frequency'] == [1, 2, 3]
        assert results['values'] == [1, 1, 2]

    def test_missing_values_data_returns_empty_list(self):
        results = technological_broadness_indication(self.DATA_MISSING, {})
        assert len(results['frequency']) == len(results['values']) == 0
        assert results['frequency'] == []
        assert results['values'] == []

    def test_works_with_large_data(self):
        results = technological_broadness_indication(self.DATA_LARGE, {})
        assert len(results['frequency']) == len(results['values']) == 1
        assert results['frequency'] == [1]
        assert results['values'] == [10000]


class TestValueOverRisk:
    DATA = [
        {},
        {"risk": [], "impact": []},
        {"risk": None, "impact": None},
        {"risk": "null", "impact": "null"},
        {"risk": 1, "impact": 3},
        {"risk": "1", "impact": "3"}
    ]

    DATA_MISSING = [
        {},
        {"risk": [], "impact": []},
        {"risk": None, "impact": None},
        {"risk": 1, "impact": "null"},
        {"risk": 1, "impact": None},
        {"risk": "1"},
        {"impact": "0"},
    ]

    DATA_LARGE = [
        {"risk": 1, "impact": 1} for _ in range(10000)
    ]

    # assuming risk and value can take values 0 to 5.
    DATA_ALL_CASES = []
    for i in range(5):
        for j in range(5):
            DATA_ALL_CASES.append({"risk": i, "impact": j})

    def test_simple_chart(self):
        results = patent_value_over_risk(self.DATA, {})
        assert len(results['values']) == 1
        assert results['values'][0]['type'] == '1,3'
        assert results['values'][0]['data'] == [1, 3, 2]

    def test_missing_values_data_returns_empty_list(self):
        results = patent_value_over_risk(self.DATA_MISSING, {})
        assert len(results['values']) == 0
        assert results['values'] == []

    def test_works_with_large_data(self):
        results = patent_value_over_risk(self.DATA_LARGE, {})
        assert len(results['values']) == 1
        assert results['values'][0]['type'] == "1,1"
        assert results['values'][0]['data'] == [1, 1, 10000]

    def test_works_with_combinations(self):
        results = patent_value_over_risk(self.DATA_ALL_CASES, {})
        assert len(results['values']) == 25
        for i in range(5):
            for j in range(5):
                assert results['values'][i * 5 + j]['type'] == f'{i},{j}'
                assert results['values'][i * 5 + j]['data'] == [i, j, 1]


class TestPatentsValue:
    DATA = [
        {},
        {"risk": [], "impact": []},
        {"risk": None, "impact": None},
        {"risk": "null", "impact": "null"},
        {"risk": 1, "impact": 3},
        {"risk": "1", "impact": "3"}
    ]

    DATA_MISSING = [
        {},
        {"risk": [], "impact": []},
        {"risk": None, "impact": None},
        {"risk": 1, "impact": "null"},
        {"risk": 1, "impact": None},
        {"risk": "1"},
        {"impact": []},
    ]

    DATA_LARGE = [
        {"risk": 1, "impact": 1} for _ in range(10000)
    ]

    def test_simple_chart(self):
        results = patents_value(self.DATA, {})
        assert len(results['values']) == 1
        assert results['values'][0]['type'] == 3
        assert results['values'][0]['y'] == 2

    def test_missing_values_data_returns_empty_list(self):
        results = patents_value(self.DATA_MISSING, {})
        assert len(results['values']) == 0
        assert results['values'] == []

    def test_works_with_large_data(self):
        results = patents_value(self.DATA_LARGE, {})
        assert len(results['values']) == 1
        assert results['values'][0]['type'] == 1
        assert results['values'][0]['y'] == 10000


class TestPatentRisk:
    DATA = [
        {},
        {"risk": [], "impact": []},
        {"risk": None, "impact": None},
        {"risk": "null", "impact": "null"},
        {"risk": 1, "impact": 3},
        {"risk": "1", "impact": "3"}
    ]

    DATA_MISSING = [
        {},
        {"risk": [], "impact": []},
        {"risk": None, "impact": None},
        {"risk": "null", "impact": 1},
        {"risk": '', "impact": 1},
        {"risk": None},
        {"impact": "0"},
    ]

    DATA_LARGE = [
        {"risk": 1, "impact": 1} for _ in range(10000)
    ]

    def test_simple_chart(self):
        results = patents_risk(self.DATA, {})
        assert len(results['values']) == 1
        assert results['values'][0]['type'] == 1
        assert results['values'][0]['y'] == 2

    def test_missing_values_data_returns_empty_list(self):
        results = patents_risk(self.DATA_MISSING, {})
        assert len(results['values']) == 0
        assert results['values'] == []

    def test_works_with_large_data(self):
        results = patents_risk(self.DATA_LARGE, {})
        assert len(results['values']) == 1
        assert results['values'][0]['type'] == 1
        assert results['values'][0]['y'] == 10000


class TestAnalyticsRecencyIndication:
    DATA_MISSING = [
        {},
        {"recency_years": []},
        {"recency_years": None},
        {"recency_years": "null"},
        {"recency_years": "miao"}
    ]

    # this includes 0 to 5
    DATA_bin_0_6 = [
                       {"recency_years": 0},
                       {"recency_years": 1},
                       {"recency_years": 2},
                       {"recency_years": 3},
                       {"recency_years": 4},
                       {"recency_years": 5},
                   ] + DATA_MISSING

    # this includes 15 and 19
    DATA_bin_15_20 = [
                         {"recency_years": 15},
                         {"recency_years": 15},
                         {"recency_years": 19},
                     ] + DATA_MISSING

    # this includes 15 and 29
    DATA_bin_15_20_30 = [
                            {"recency_years": 20},
                            {"recency_years": 21},
                            {"recency_years": 29},
                            {"recency_years": 29},
                        ] + DATA_bin_15_20

    DATA_LARGE = [{"recency_years": i} for i in range(99999)]

    def test_simple_chart_bin_0_6(self):
        results = analytics_recency_indication(self.DATA_bin_0_6, {})
        assert results['dataSource'][0]['y'] == 1
        assert results['dataSource'][1]['y'] == 2
        assert results['dataSource'][2]['y'] == 3
        for i in range(3, 10):
            assert results['dataSource'][i]['y'] == 0

    def test_simple_chart_bin_6_10(self):
        results = analytics_recency_indication(self.DATA_bin_15_20, {})
        for i in range(10):
            if i == 5:
                assert results['dataSource'][i]['label'] == "15-20"
                assert results['dataSource'][i]['y'] == 3
            else:
                assert results['dataSource'][i]['y'] == 0

    def test_simple_chart_bin_6_10_20_30(self):
        results = analytics_recency_indication(self.DATA_bin_15_20_30, {})
        for i in range(10):
            if i == 5:
                assert results['dataSource'][i]['label'] == "15-20"
                assert results['dataSource'][i]['y'] == 3
            elif i == 6:
                assert results['dataSource'][i]['label'] == "20-30"
                assert results['dataSource'][i]['y'] == 4
            else:
                assert results['dataSource'][i]['y'] == 0

    def test_missing_values_data_returns_empty_list(self):
        results = analytics_recency_indication(self.DATA_MISSING, {})
        assert results['dataSource'] == []

    def test_works_with_large_data(self):
        results = analytics_recency_indication(self.DATA_LARGE, {})
        for i in range(10):
            assert results['dataSource'][i]['y'] > 0


class TestAnalyticsPatentFamily:
    """ it's similar to recency """

    DATA_MISSING = [
        {},
        {"market_coverage": []},
        {"market_coverage": None},
        {"market_coverage": "null"},
        {"market_coverage": "miao"}
    ]

    # singular range
    DATA_bin_1_5 = [
                       {"market_coverage": 1},
                       {"market_coverage": 2},
                       {"market_coverage": 3},
                       {"market_coverage": 4},
                       {"market_coverage": 5},
                   ] + DATA_MISSING

    # missing range
    DATA_bin_1_10_missing = [
                                {"market_coverage": 1},
                                {"market_coverage": 3},
                                {"market_coverage": 3},
                                {"market_coverage": 5},
                                {"market_coverage": 7},
                                {"market_coverage": 8},
                                {"market_coverage": 9},
                            ] + DATA_MISSING

    # this includes 16 and 30
    DATA_bin_16_30 = [{"market_coverage": 16},
                      {"market_coverage": 16},
                      {"market_coverage": 20},
                      {"market_coverage": 22},
                      {"market_coverage": 21},
                      {"market_coverage": 30}
                      ]

    DATA_LARGE = [{"market_coverage": i} for i in range(10000)]

    def test_simple_chart_bin_1_5(self):
        results = analytics_patent_family(self.DATA_bin_1_5, {})
        assert results['total'] == 5
        assert results['dataSource']['xAxis'] == ["01", "02", "03", "04", "05"]
        assert results['dataSource']['yAxis'] == [1] * 5
        assert results['dataSource']['yAxisAvg'] == [1, 2, 3, 4, 5]

    def test_simple_chart_bin_1_10_missing(self):
        results = analytics_patent_family(self.DATA_bin_1_10_missing, {})
        assert results['total'] == 7
        assert results['dataSource']['xAxis'] == ["01", "03", "05", "06-10"]
        assert results['dataSource']['yAxis'] == [1, 2, 1, 3]
        assert results['dataSource']['yAxisAvg'] == [1, 3, 5, 8]

    def test_simple_chart_bin_16_30(self):
        results = analytics_patent_family(self.DATA_bin_16_30, {})
        assert results['total'] == 6
        assert results['dataSource']['xAxis'] == ["16-20", "21-30"]
        assert results['dataSource']['yAxis'] == [3, 3]
        assert results['dataSource']['yAxisAvg'] == [17, 24]

    def test_missing_values_data_returns_empty_dict(self):
        results = analytics_patent_family(self.DATA_MISSING, {})
        assert results['dataSource'] == {}
        assert results['total'] == 0

    def test_works_with_large_data(self):
        results = analytics_patent_family(self.DATA_LARGE, {})
        assert results['total'] == 999  # I set the services in the code to be 999


class TestAnalyticsConsistencyIndication:
    DATA = [
        {"consistency": 0.1, "publn_no_rep": "EP-12-A1"},
        {"consistency": 0.2, "publn_no_rep": "US-23-B2"},
        {"consistency": 0.3, "publn_no_rep": "DE-34-U1"},
    ]

    DATA_MISSING = [
        {},
        {"consistency": [], "publn_no_rep": []},
        {"consistency": None, "publn_no_rep": None},
        {"consistency": 0.3, "publn_no_rep": ""},
        {"consistency": 0.7, "publn_no_rep": None},
        {"consistency": "", "publn_no_rep": "EP-2049363-A1"},
        {"consistency": "1"},
        {"consistency": -0.2},
        {"publn_no_rep": "EP-2049363-B2"}
    ]

    DATA_LARGE = [
        {"consistency": 0.1, "publn_no_rep": "EP-2049363-A1"} for _ in range(100000)
    ]

    def test_simple_chart(self):
        results = analytics_consistency_indication(self.DATA, {})
        assert len(results['dataSource']) == 3
        assert [results['dataSource'][i]['ranking'] for i in range(3)] == [1, 2, 3]
        assert [results['dataSource'][i]['y'] for i in range(3)] == [0.9, 0.8, 0.7]
        assert [results['dataSource'][i]['name'] for i in range(3)] == ["EP12A1", "US23B2", "DE34U1"]

    def test_missing_values_data_returns_empty_list(self):
        results = analytics_consistency_indication(self.DATA_MISSING, {})
        assert len(results['dataSource']) == 0
        assert results['dataSource'] == []

    def test_works_with_large_data(self):
        results = analytics_consistency_indication(self.DATA_LARGE, {})
        assert len(results['dataSource']) == 100000


class TestAnalyticsCitationsReferences:
    """"""

    DATA_MISSING = [
        {},
        {"citation_forward_count": [], "citation_backward_count": []},
        {"citation_forward_count": None, "citation_backward_count": None},
        {"citation_forward_count": "null", "citation_backward_count": "null"},
        {"citation_forward_count": None, "citation_backward_count": ""},
        {"citation_forward_count": "", "citation_backward_count": None},
        {"citation_forward_count": None},
        {"citation_backward_count": None}
    ]

    # singular range
    DATA_bin_1_5 = [dict(citation_forward_count=i, citation_backward_count=i) for i in range(1, 6)]

    # missing range
    DATA_bin_1_10_missing = [
                                {"citation_forward_count": 6, "citation_backward_count": 6},
                                {"citation_forward_count": 6, "citation_backward_count": 9},
                                {"citation_forward_count": 7, "citation_backward_count": 10},
                                {"citation_forward_count": 7, "citation_backward_count": 10},
                                {"citation_forward_count": 10, "citation_backward_count": 10},
                            ] + DATA_bin_1_5 + DATA_MISSING

    # missing range
    DATA_bin_11_50_missing = [
        {"citation_forward_count": 11},
        {"citation_forward_count": 11},
        {"citation_forward_count": 15},
        {"citation_forward_count": 17},
        {"citation_forward_count": 20},
        {"citation_forward_count": 21, "citation_backward_count": 11},
        {"citation_forward_count": 29, "citation_backward_count": 13},
        {"citation_forward_count": 30, "citation_backward_count": 22},
        {"citation_forward_count": 31, "citation_backward_count": 33},
        {"citation_backward_count": 47},
        {"citation_backward_count": 50},
        {"citation_backward_count": 50}
    ]

    DATA_LARGE = [dict(citation_forward_count=i, citation_backward_count=i) for i in range(100000)]

    # hard-coded, there are 12 entries in the count array.
    num_of_bins = 12

    def test_missing_values_data(self):
        results = analytics_citations_references(self.DATA_MISSING, {})
        assert results['citationCount'] == [0] + [0] * (self.num_of_bins - 1)
        assert results['referenceCount'] == [0] + [0] * (self.num_of_bins - 1)

    def test_simple_chart_bin_1_5(self):
        results = analytics_citations_references(self.DATA_bin_1_5, {})
        assert results['citationCount'] == [0, 1, 1, 1, 1, 1] + (self.num_of_bins - 6) * [0]
        assert results['referenceCount'] == [0, -1, -1, -1, -1, -1] + (self.num_of_bins - 6) * [0]

    def test_simple_chart_bin_1_10_missing(self):
        results = analytics_citations_references(self.DATA_bin_1_10_missing, {})
        assert results['citationCount'] == [0, 1, 1, 1, 1, 1, 5] + (self.num_of_bins - 7) * [
            0]
        assert results['referenceCount'] == [0, -1, -1, -1, -1, -1, -5] + (self.num_of_bins - 7) * [0]

    def test_simple_chart_bin_11_50_missing(self):
        results = analytics_citations_references(self.DATA_bin_11_50_missing, {})
        # as a reminder, bins =            [0, 1, 2, 3, 4, 5, 6, 11, 21, 31, 51, 100, 99999]
        assert results['citationCount'] == [0, 0, 0, 0, 0, 0, 0, 5, 3, 1, 0, 0]
        assert results['referenceCount'] == [0, 0, 0, 0, 0, 0, 0, -2, -1, -4, 0, 0]

    def test_works_with_large_data(self):
        results = analytics_citations_references(self.DATA_LARGE, {})
        assert all(x > 0 for x in results['citationCount'])
        assert all(x < 0 for x in results['referenceCount'])


class TestAnalyticsCitationsCount:
    """"""

    # missing values are dealt differently here, set to 0 instead of skipping.
    DATA_MISSING = [
        {},
        {"citation_forward_count": [], "citation_backward_count": []},
        {"citation_forward_count": None, "citation_backward_count": None},
        {"citation_forward_count": "null", "citation_backward_count": "null"},
        {"citation_forward_count": None, "citation_backward_count": ""},
        {"citation_forward_count": "", "citation_backward_count": None},
        {"citation_forward_count": None},
        {"citation_backward_count": None}
    ]

    # singular range
    DATA_bin_1_5 = [dict(citation_forward_count=i, citation_backward_count=i) for i in range(1, 6)]

    # missing range
    DATA_bin_1_10_missing = [
                                {"citation_forward_count": 6, "citation_backward_count": 6},
                                {"citation_forward_count": 6, "citation_backward_count": 9},
                                {"citation_forward_count": 7, "citation_backward_count": 10},
                                {"citation_forward_count": 7, "citation_backward_count": 10},
                                {"citation_forward_count": 10, "citation_backward_count": 10},
                            ] + DATA_bin_1_5 + DATA_MISSING

    # missing range
    DATA_bin_11_50_missing = [
        {"citation_forward_count": 11},
        {"citation_forward_count": 11},
        {"citation_forward_count": 15},
        {"citation_forward_count": 17},
        {"citation_forward_count": 20},
        {"citation_forward_count": 21, "citation_backward_count": 11},
        {"citation_forward_count": 29, "citation_backward_count": 13},
        {"citation_forward_count": 30, "citation_backward_count": 22},
        {"citation_forward_count": 31, "citation_backward_count": 33},
        {"citation_backward_count": 47},
        {"citation_backward_count": 50},
        {"citation_backward_count": 50}
    ]

    DATA_LARGE = [dict(citation_forward_count=i, citation_backward_count=i) for i in range(100000)]

    # hard-coded, there are 12 entries in the count array.
    num_of_bins = 12

    def test_missing_values_data(self):
        results = analytics_citations_count(self.DATA_MISSING, {})
        assert results['citationCount'] == [0] + [0] * (self.num_of_bins - 1)

    def test_simple_chart_bin_1_5(self):
        results = analytics_citations_count(self.DATA_bin_1_5, {})
        assert results['citationCount'] == [0, 1, 1, 1, 1, 1] + (self.num_of_bins - 6) * [0]

    def test_simple_chart_bin_1_10_missing(self):
        results = analytics_citations_count(self.DATA_bin_1_10_missing, {})
        assert results['citationCount'] == [0, 1, 1, 1, 1, 1, 5] + (self.num_of_bins - 7) * [0]

    def test_simple_chart_bin_11_50_missing(self):
        results = analytics_citations_count(self.DATA_bin_11_50_missing, {})
        # as a reminder, bins =            [0, 1, 2, 3, 4, 5, 6, 11, 21, 31, 51, 100, 99999]
        assert results['citationCount'] == [0, 0, 0, 0, 0, 0, 0, 5, 3, 1, 0, 0]

    def test_works_with_large_data(self):
        results = analytics_citations_count(self.DATA_LARGE, {})
        assert all(x > 0 for x in results['citationCount'])


class TestAnalyticsReferencesCount:
    """"""

    # missing values are dealt differently here, set to 0 instead of skipping.
    DATA_MISSING = [
        {},
        {"citation_forward_count": [], "citation_backward_count": []},
        {"citation_forward_count": None, "citation_backward_count": None},
        {"citation_forward_count": 0, "citation_backward_count": "null"},
        {"citation_forward_count": 0, "citation_backward_count": None},
        {"citation_forward_count": "", "citation_backward_count": None},
        {"citation_forward_count": None},
        {"citation_backward_count": None}
    ]

    # singular range
    DATA_bin_1_5 = [dict(citation_forward_count=i, citation_backward_count=i) for i in range(1, 6)]

    # missing range
    DATA_bin_1_10_missing = [
                                {"citation_forward_count": 6, "citation_backward_count": 6},
                                {"citation_forward_count": 6, "citation_backward_count": 9},
                                {"citation_forward_count": 7, "citation_backward_count": 10},
                                {"citation_forward_count": 7, "citation_backward_count": 10},
                                {"citation_forward_count": 10, "citation_backward_count": 10},
                            ] + DATA_bin_1_5 + DATA_MISSING

    # missing range
    DATA_bin_11_50_missing = [
        {"citation_forward_count": 11},
        {"citation_forward_count": 11},
        {"citation_forward_count": 15},
        {"citation_forward_count": 17},
        {"citation_forward_count": 20},
        {"citation_forward_count": 21, "citation_backward_count": 11},
        {"citation_forward_count": 29, "citation_backward_count": 13},
        {"citation_forward_count": 30, "citation_backward_count": 22},
        {"citation_forward_count": 31, "citation_backward_count": 33},
        {"citation_backward_count": 47},
        {"citation_backward_count": 50},
        {"citation_backward_count": 50}
    ]

    DATA_LARGE = [dict(citation_forward_count=i, citation_backward_count=i) for i in range(100000)]

    # hard-coded, there are 12 entries in the count array.
    num_of_bins = 12

    def test_missing_values_data(self):
        results = analytics_references_count(self.DATA_MISSING, {})
        assert results['referenceCount'] == [0] + [0] * (self.num_of_bins - 1)

    def test_simple_chart_bin_1_5(self):
        results = analytics_references_count(self.DATA_bin_1_5, {})
        assert results['referenceCount'] == [0, 1, 1, 1, 1, 1] + (self.num_of_bins - 6) * [0]

    def test_simple_chart_bin_1_10_missing(self):
        results = analytics_references_count(self.DATA_bin_1_10_missing, {})
        assert results['referenceCount'] == [0, 1, 1, 1, 1, 1, 5] + (self.num_of_bins - 7) * [0]

    def test_simple_chart_bin_11_50_missing(self):
        results = analytics_references_count(self.DATA_bin_11_50_missing, {})
        # as a reminder, bins =            [0, 1, 2, 3, 4, 5, 6, 11, 21, 31, 51, 100, 99999]
        assert results['referenceCount'] == [0, 0, 0, 0, 0, 0, 0, 2, 1, 4, 0, 0]

    def test_works_with_large_data(self):
        results = analytics_references_count(self.DATA_LARGE, {})
        assert all(x > 0 for x in results['referenceCount'])


class TestMinMaxValues:

    def test_simple_chart_empty_data(self):
        results = min_max_values([], {})
        assert results['datasource']['prio_date'] == {'min': None, 'max': None, 'avg': None}
        assert results['datasource']['citing'] == {'min': None, 'max': None, 'avg': None}

    INVALID_DATA = [
        {'priority_date': None, 'publication_date': None, 'citation_forward_count': None,
         'citation_backward_count': None, 'market_coverage': None, 'technology_broadness': None},
        {'priority_date': '2009-03-invalid', 'publication_date': 'invalid-date', 'citation_forward_count': 'abc123',
         'citation_backward_count': None, 'market_coverage': None, 'technology_broadness': None}
    ]

    def test_simple_chart_invalid_data(self):
        results = min_max_values(self.INVALID_DATA, {})
        assert results['datasource']['prio_date'] == {'min': None, 'max': None, 'avg': None}
        assert results['datasource']['citing'] == {'min': None, 'max': None, 'avg': None}

    DATA = [
        {'priority_date': None, 'publication_date': None, 'citation_forward_count': None,
         'citation_backward_count': None, 'market_coverage': None, 'technology_broadness': None},
        {'priority_date': '2009-03-invalid', 'publication_date': 'invalid-date', 'citation_forward_count': 'abc123',
         'citation_backward_count': None, 'market_coverage': None, 'technology_broadness': None},
        {'priority_date': '2009-03-24', 'publication_date': '2010-09-30', 'citation_forward_count': 7,
         'citation_backward_count': 3, 'market_coverage': 4, 'technology_broadness': 2},
        {'priority_date': '2010-02-08', 'publication_date': '2011-08-11', 'citation_forward_count': 3,
         'citation_backward_count': 4, 'market_coverage': 3, 'technology_broadness': 1},
        {'priority_date': '2010-07-19', 'publication_date': '2011-08-31', 'citation_forward_count': 3,
         'citation_backward_count': 0, 'market_coverage': 3, 'technology_broadness': 7},
        {'priority_date': '2010-03-31', 'publication_date': '2011-10-13', 'citation_forward_count': 2,
         'citation_backward_count': 0, 'market_coverage': 9, 'technology_broadness': 7},
        {'priority_date': '2010-12-10', 'publication_date': '2012-06-14', 'citation_forward_count': 4,
         'citation_backward_count': 7, 'market_coverage': 3, 'technology_broadness': 5},
        {'priority_date': '2010-12-21', 'publication_date': '2012-06-20', 'citation_forward_count': 2,
         'citation_backward_count': 8, 'market_coverage': 4, 'technology_broadness': 3},
        {'priority_date': '2010-12-24', 'publication_date': '2012-06-28', 'citation_forward_count': 9,
         'citation_backward_count': 3, 'market_coverage': 8, 'technology_broadness': 0},
        {'priority_date': '2002-12-10', 'publication_date': '2007-11-15', 'citation_forward_count': 5,
         'citation_backward_count': 7, 'market_coverage': 9, 'technology_broadness': 6},
        {'priority_date': '2011-02-24', 'publication_date': '2012-08-30', 'citation_forward_count': 8,
         'citation_backward_count': 6, 'market_coverage': 2, 'technology_broadness': 1},
        {'priority_date': '2011-04-26', 'publication_date': '2012-10-26', 'citation_forward_count': 5,
         'citation_backward_count': 6, 'market_coverage': 0, 'technology_broadness': 6},
    ]

    def test_simple_chart(self):
        results = min_max_values(self.DATA, {})
        assert results['datasource']['prio_date'] == {'min': '2002-12-10', 'max': '2011-04-26', 'avg': '2009-11-03'}
        assert results['datasource']['citing'] == {'min': 2, 'max': 9, 'avg': 5}
        assert results['datasource']['broadness'] == {'min': 0, 'max': 7, 'avg': 4}


class TestAnalyticsGreenFamilies:
    DATA = [
        {},
        {"cpc": ['E03C1/12'], "priority_date": '2005-01-01'},
        {"cpc": ['E03C1/12'], "priority_date": '2006-01-01'},
        {"cpc": ['E03C1/12'], "priority_date": '2007-01-01'},
        {"cpc": ['E03C1/12'], "priority_date": '2008-01-01'},
        {"cpc": ['E03C1/12'], "priority_date": '2009-01-01'},
        {"cpc": None, "ipc": None, "priority_date": '2010-01-01'},
        {"cpc": None, "ipc": [], "priority_date": '2010-01-01'},
        {
            "cpc": ['F27B1/18', 'C21B7/22'],
            "ipc": ['B01D53/92', 'B01D53/96', 'F01M13/0405'],
            "priority_date": '2010-01-01'
        },
        {"cpc": ['F27B1/18', ], "ipc": ['F01M13/0405'], "priority_date": '2011-01-01'},
        {"cpc": ['F27B1/18', 'C21B7/22'], "ipc": ['F02D21/10', 'F02D21/06'], "priority_date": '2011-01-01'},
        {
            "cpc": ['F02B47/06', 'F02M3/02'],
            "ipc": ['F02M3/045', 'F02M3/042', 'F02M3/055'],
            "priority_date": '2011-01-01'
        },
        {
            "cpc": ['F02M51/0617', 'C21B7/22'],
            "ipc": ['B01D53/92', 'F02M69/16', 'F01M13/0405'],
            "priority_date": '2012-01-01'
        },
        {"cpc": ['F27B1/18', 'E03C1/12'], "ipc": ['B01D53/92', 'C09K3/32'], "priority_date": '2012-01-01'},
        {"cpc": ['F27B1/18'], "ipc": ['B01D53/92', 'B01D53/96', 'F01M13/0405'], "priority_date": '2013-01-01'},
        {"cpc": ['F27B1/18'], "ipc": ['B01D53/92', 'B01D53/96', 'F01M13/0405'], "priority_date": '2014-01-01'},
        {"cpc": ['C21B7/22'], "ipc": ['B01D53/96', 'F01M13/0405'], "priority_date": '2015-01-01'},
        {
            "cpc": ['A43B1/12', 'C21B7/22'],
            "ipc": ['B01D53/92', 'B01D53/96', 'F01M13/0405'],
            "priority_date": '2016-01-01'
        },
        {
            "cpc": ['F27B1/18', 'C21B7/22'],
            "ipc": ['A43B1/12', 'B01D53/96', 'F01M13/0405'],
            "priority_date": '2016-01-01'
        },
        {"cpc": [], "ipc": ['A43B21/14', 'B01D53/96', 'F01M13/0405'], "priority_date": '2017-01-01'},
        {"cpc": [], "ipc": ['A43B21/14', 'B01D53/96', 'F01M13/0405'], "priority_date": '2018-01-01'},
        {"cpc": [], "ipc": ['A43B21/14', 'B01D53/96', 'F01M13/0405'], "priority_date": '2019-01-01'},
    ]

    def test_should_be_15_years(self):
        results = green_families_timeline(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 15

    def test_should_be_2_categories(self):
        results = green_families_timeline(self.DATA, {})
        assert len(results['datasource']['yAxis']) == 2

    @pytest.mark.parametrize('category, data', [
        ('Green', [1, 1, 1, 1, 1, 1, 3, 2, 1, 1, 1, 2, 1, 1, 1]),
        ('Not green', [0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0])
    ])
    def test_should_have_accurate_counts(self, category, data):
        results = green_families_timeline(self.DATA, {})
        categories = results['datasource']['yAxis']
        category_data, = [c for c in categories if c['name'] == category]
        assert category_data['data'] == data

    def test_should_handle_no_documents_gracefully(self):
        results = green_families_timeline([], {})
        assert len(results['datasource']['xAxis']) == 0
        assert len(results['datasource']['yAxis']) == 0


class TestAnalyticsLegalStatus:
    DATA = [
        {},
        {"legal_statuses": []},
        {"legal_statuses": ['active', 'expired_fee_related']},
        {"legal_statuses": ['unknown', None]},
        {"legal_statuses": ['granted']},
        {"legal_statuses": ['expired', 'unknown']},
        {"legal_statuses": ['withdrawn']},
        {"legal_statuses": ['unknown', 'abandoned']},
        {"legal_statuses": ['active_reinstated']},
        {"legal_statuses": ['in_force', 'unknown']},
        {"legal_statuses": ['active']},
        {"legal_statuses": ['active', None]},
        {"legal_statuses": ['pending', 'unknown']},
    ]

    def test_simple_chart_empty_data(self):
        results = analytics_legal_status([], {})
        assert len(results['datasource']) == 0
        assert results['total'] == 0

    def test_simple_chart(self):
        results = analytics_legal_status(self.DATA, {})
        assert len(results['datasource']) == 3
        assert results['total'] == 13
        assert {'value': "valid", "y": 7} in results['datasource']
        assert {'value': "invalid", "y": 3} in results['datasource']
        assert {'value': "unknown", "y": 3} in results['datasource']


class TestAnalyticsGreenCategories:
    DATA = [
        {},
        {"cpc": ['B01D53/58'], "priority_date": '2008-01-01'},
        {"cpc": ['B01D53/58'], "priority_date": '2009-01-01'},
        {"cpc": ['F34A1/19'], "ipc": None, "priority_date": '2010-01-01'},
        {"cpc": None, "ipc": [], "priority_date": '2010-01-01'},
        {
            "cpc": ['E03B1/041', 'F16K21/165', 'F16K21/165', 'XXXXX/0405'],
            "ipc": ['B01D53/92', 'F16K21/165', 'F01M13/0405', 'KKKK/0405'],
            "priority_date": '2010-01-01'
        },
        {"cpc": ['F27B1/18', 'F27B1/18'], "ipc": ['E03B1/041', "Y02W30/91"], "priority_date": '2011-01-01'},
        {"cpc": ['Y02W30/91'], "ipc": ["Y02W30/91"], "priority_date": '2011-01-01'},
        {"cpc": [], "ipc": [], "priority_date": '2011-01-01'}
    ]

    def test_empty_data(self):
        results = green_categories([], {})
        assert results['datasource'] == []
        assert results['total'] == 0

    def test_simple_data(self):
        results = green_categories(self.DATA, {})
        assert results['datasource'] == [
            {'name': 'Environmental management', 'value': 4,
             'codes': sorted(['F27B1/18', 'B01D53/58', 'B01D53/92']), 'green': True},
            {'name': 'Water adaptation technologies', 'value': 2,
             'codes': sorted(['E03B1/041']), 'green': True},
            {'name': 'Wastes climate solutions',
             'value': 2, 'codes': sorted(['Y02W30/91']), 'green': True},
            {'name': 'Not green',
             'value': 4, 'codes': ['F34A1/19'], 'green': False}]
        assert results['total'] == 12


class TestAnalyticsNoveltyFoundation:
    DATA = [
        {},
        {
            "recency_years": 0.47671232,
            "technological_distance": 0.61,
            "legal_statuses": ["expired"],
            "publn_no_rep": "AUPR815201A0"
        },
        {
            "recency_years": 5.2475648,
            "technological_distance": 0.53,
            "legal_statuses": ["expired_fee_related"],
            "publn_no_rep": "CA2208179A1"
        },
        {
            "recency_years": 5.2344832,
            "technological_distance": 0.62,
            "legal_statuses": ["active", "expired"],
            "publn_no_rep": "CN1263324A"
        },
        {
            "recency_years": 3.3205481,
            "technological_distance": 0.45,
            "legal_statuses": ["granted", "expired"],
            "publn_no_rep": "WO1989009235A1"
        },
        {
            "recency_years": 6.4978523,
            "technological_distance": 0.49,
            "legal_statuses": ["unknown"],
            "publn_no_rep": "CA2076385A1"
        }
    ]

    def test_empty_data(self):
        results = analytics_novelty_foundation([], {})
        assert results['datasource'] == []

    def test_simple_data(self):
        results = analytics_novelty_foundation(self.DATA, {})
        assert len(results['datasource']) == 3
        assert len(results['datasource'][0]) == 2
        assert results['datasource'][0] == {
            'data': [[0.61, 0.47671232, 'AUPR815201A0'], [0.53, 5.2475648, 'CA2208179A1']],
            'name': 'invalid'
        }
        assert results['datasource'][1] == {
            'data': [[0.62, 5.2344832, 'CN1263324A'], [0.45, 3.3205481, 'WO1989009235A1']],
            'name': 'valid'
        }
        assert results['datasource'][2] == {'data': [[0.49, 6.4978523, 'CA2076385A1']], 'name': 'unknown'}


class TestAnalyticsGreenTechnologiesLandscape:
    DATA = [
        {},
        {"cpc": ['02B10/10'], "priority_date": '2008-01-01'},
        {"cpc": ['Y02E50/10'], "priority_date": '2009-01-01'},
        {"cpc": ['Y02E60/10'], "ipc": None, "priority_date": '2010-01-01'},
        {"cpc": None, "ipc": [], "priority_date": '2010-01-01'},
        {
            "cpc": ['Y02E30/30', 'Y02T10/62', 'Y02T10/12', 'XXXXX/0405'],
            "ipc": ['B01D53/92', 'F16K21/165', 'F01M13/0405', 'KKKK/0405'],
            "priority_date": '2010-01-01'
        },
        {"cpc": ['Y02T90/14', 'B01D46/24'], "priority_date": '2011-01-01'},
        {"cpc": ['F23G5/44'], "ipc": ["E03F7/10"], "priority_date": '2011-01-01'},
        {"cpc": ['02B10/10'], "priority_date": '2008-01-01'},
        {"cpc": ['02B10/10', 'Y02E30/30', 'Y02T10/62', 'Y02T10/12'], "priority_date": '2008-01-01'},
        {"cpc": [], "ipc": [], "priority_date": '2011-01-01'}
    ]

    def test_empty_data(self):
        results = green_technologies_landscape([], {})
        assert results['datasource'] == []

    def test_simple_data(self):
        result_expected = [
            {
                'data': [
                    {'codes': ['Y02E50/10'], 'name': 'Biofuels', 'value': 1},
                    {'codes': ['Y02E30/30', 'Y02E30/30'], 'name': 'Nuclear fusion reactors', 'value': 2}
                ],
                'name': 'Energy climate solutions'
            },
            {
                'data': [
                    {'codes': ['F23G5/44'], 'name': 'Incineration and energy recovery', 'value': 1},
                    {'codes': ['B01D53/92', 'B01D46/24'], 'name': 'Post-combustion technologies', 'value': 2},
                    {'codes': ['E03F7/10'], 'name': 'Water and wastewater treatment', 'value': 1}
                ],
                'name': 'Environmental management'
            },
            {
                'data': [
                    {
                        'codes': ['Y02T10/12', 'Y02T10/12'],
                        'name': 'Conventional vehicles (based on internal combustion engine)',
                        'value': 2
                    },
                    {
                        'codes': ['Y02T90/14'],
                        'name': 'Enabling technologies in transport', 'value': 1
                    },
                    {'codes': ['Y02T10/62', 'Y02T10/62'], 'name': 'Hybrid vehicles', 'value': 2}
                ],
                'name': 'Transportation climate solutions'
            },
        ]

        results = green_technologies_landscape(self.DATA, {})
        results["datasource"].sort(key=lambda x: (x['name'], x['data'].sort(key=lambda y: y['name'])))
        assert results["datasource"] == result_expected


class TestAnalyticsGreenRanking:
    DATA = [
        {},
        {"cpc": ['B01D53/58']},
        {"cpc": ['B01D53/58'], 'tech_fields': None},
        {"cpc": ['B01D53/58'], 'tech_fields': []},
        {"cpc": ['F34A1/19'], "ipc": None, 'tech_fields': None},
        {"cpc": None, "ipc": [], 'tech_fields': None},
        {"cpc": None, "ipc": None, 'tech_fields': None},
        {"cpc": [], "ipc": [], 'tech_fields': []},
        {
            "cpc": ['E03B1/041', 'F16K21/165', 'F16K21/165', 'XXXXX/0405'],
            "ipc": ['B01D53/92', 'F16K21/165', 'F01M13/0405', 'KKKK/0405'],
            'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']
        },
        {"cpc": ['F27B1/18', 'F27B1/18'], "ipc": ['E03B1/041', "Y02W30/91"],
         'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Computer Technology']},
        {"cpc": ['Y02W30/91'], "ipc": ["Y02W30/91"],
         'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Control']},
        {"cpc": ['ABC'], "ipc": ["XXX"],
         'tech_fields': ['IT Methods for Management', 'Digital Communication', 'Control']},
        {
            "cpc": ['XXX', 'YY', 'TTT', 'HHHH'],
            "ipc": ['KKK'],
            'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']
        },
    ]

    def test_empty_data(self):
        results = green_ranking([], {})
        assert results['datasource']
        assert results['datasource']['categories'] == ['OVERALL']
        assert results['datasource']['series'] == [{'name': 'Green', 'data': [4.46], 'stack': 'Average'},
                                                   {'name': 'Non-green', 'data': [95.54], 'stack': 'Average'},
                                                   {'name': 'Green', 'data': [0.0], 'stack': 'Your portfolio'},
                                                   {'name': 'Non-green', 'data': [0.0], 'stack': 'Your portfolio'}]

    def test_simple_data(self):
        results = green_ranking(self.DATA, {})
        assert results['datasource']
        datasource = results['datasource']
        assert datasource['categories'] == ['OVERALL', 'Digital Communication', 'Control', 'Computer Technology']
        assert datasource['series'] == [{'name': 'Green', 'data': [4.46, 0.21, 1.84, 0.36], 'stack': 'Average'},
                                        {'name': 'Non-green', 'data': [95.54, 99.79, 98.16, 99.64], 'stack': 'Average'},
                                        {'name': 'Green', 'data': [60.0, 60.0, 50.0, 66.67], 'stack': 'Your portfolio'},
                                        {'name': 'Non-green', 'data': [40.0, 40.0, 50.0, 33.33],
                                         'stack': 'Your portfolio'}]
