from copy import deepcopy

from app.services.search.helpers.charts.competitors_charts import ipc_profile, similarity_profile, tech_profile, \
    over_time, cpc_profile, authorities_profile, portfolio_analytics_profile, applicant_legal_status_distribution


class TestPatentsByIpcProfileChart:
    DATA = [
        {},
        {"applicants_ifi": [], "ipc": []},
        {"applicants_ifi": [""], "ipc": []},
        {"applicants_ifi": ["OLYMPUS OPTICAL"],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"applicants_ifi": ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER"],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"applicants_ifi": ["<PERSON>L<PERSON>MPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ"],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC"],
         "ipc": ["G07C11/00", "G06K11/18"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO"],
         "ipc": ["G07C11/00"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", ""],
         "ipc": []},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", None],
         "ipc": None}
    ]

    DATA_PUBLICATION = [
        {},
        {"applicants_ifi": [], "ipc": []},
        {"applicants_ifi": [""], "ipc": []},
        {"applicants_ifi": ["OLYMPUS OPTICAL"],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", "G07C9/01", ""]},
        {"applicants_ifi": ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER"],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023", "G06F21/31", None]},
        {"applicants_ifi": ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ"],
         "ipc": ["G07C11/00", "G06K11/18", "G06F3/023"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC"],
         "ipc": ["G07C11/00", "G06K11/18"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO"],
         "ipc": ["G07C11/00"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", ""],
         "ipc": []},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", None],
         "ipc": None}
    ]

    def test_simple_chart(self):
        results = ipc_profile(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 5
        assert len(results['datasource']['yAxis']) == 5
        assert len(results['datasource']['values']) == 15
        assert results['total'] == 35

    def test_with_parameters(self):
        results = ipc_profile(self.DATA, {"ipc_profile_applicants_quantity": 3, "ipc_profile_quantity": 3})
        assert len(results['datasource']['xAxis']) == 3
        assert len(results['datasource']['yAxis']) == 3
        assert len(results['datasource']['values']) == 9
        assert results['total'] == 27

    def test_publication_search(self):
        results = ipc_profile(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']['xAxis']) == 5
        assert len(results['datasource']['yAxis']) == 5
        assert len(results['datasource']['values']) == 15
        assert results['total'] == 35


class TestPatentsByCpcProfileChart:
    DATA = [
        {},
        {"applicants_ifi": [], "cpc": []},
        {"applicants_ifi": [""], "cpc": []},
        {"applicants_ifi": ["OLYMPUS OPTICAL"],
         "cpc": ["H01L21/02395", "H01L21/02463", "H01L21/02505", "H01L21/02546", "H01L31/03046", ""]},
        {"applicants_ifi": ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER"],
         "cpc": ["H01L21/02395", "H01L21/02463", "H01L21/02505", "H01L21/02546", None]},
        {"applicants_ifi": ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ"],
         "cpc": ["H01L21/02395", "H01L21/02463", "H01L21/02505"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC"],
         "cpc": ["H01L21/02395", "H01L21/02463"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO"],
         "cpc": ["H01L21/02395"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", ""],
         "cpc": []},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", None],
         "cpc": None}
    ]

    DATA_PUBLICATION = [
        {},
        {"applicants_ifi": [], "cpc": []},
        {"applicants_ifi": [""], "cpc": []},
        {"applicants_ifi": ["OLYMPUS OPTICAL"],
         "cpc": ["H01L21/02395", "H01L21/02463", "H01L21/02505", "H01L21/02546", "H01L31/03046", ""]},
        {"applicants_ifi": ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER"],
         "cpc": ["H01L21/02395", "H01L21/02463", "H01L21/02505", "H01L21/02546", None]},
        {"applicants_ifi": ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ"],
         "cpc": ["H01L21/02395", "H01L21/02463", "H01L21/02505"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC"],
         "cpc": ["H01L21/02395", "H01L21/02463"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO"],
         "cpc": ["H01L21/02395"]},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", ""],
         "cpc": []},
        {"applicants_ifi":
         ["OLYMPUS OPTICAL", "CHINOIN GYOGYSZER", "SIEMENSHWEIZ", "BUKH MEDITEC", "CHISSO", None],
         "cpc": None}
    ]

    def test_simple_chart(self):
        results = cpc_profile(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 5
        assert len(results['datasource']['yAxis']) == 5
        assert len(results['datasource']['values']) == 15
        assert results['total'] == 35

    def test_with_parameters(self):
        results = cpc_profile(self.DATA, {"cpc_profile_applicants_quantity": 3, "cpc_profile_quantity": 3})
        assert len(results['datasource']['xAxis']) == 3
        assert len(results['datasource']['yAxis']) == 3
        assert len(results['datasource']['values']) == 9
        assert results['total'] == 27

    def test_publication_search(self):
        results = cpc_profile(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']['xAxis']) == 5
        assert len(results['datasource']['yAxis']) == 5
        assert len(results['datasource']['values']) == 15
        assert results['total'] == 35


class TestPatentsBySimilarityProfileChart:
    DATA = [
        {},
        {"applicants_ifi": [], "similarity_index": None},
        {"applicants_ifi": None, "similarity_index": 999},
        {"applicants_ifi": ["KIESER KUNO", "BAYER"], "similarity_index": 331},
        {"applicants_ifi": ["KIESER KUNO", "BAYER"], "similarity_index": 299},
        {"applicants_ifi": ["SUKAVA SULO", "BVP"], "similarity_index": 276},
        {"applicants_ifi": ["BIRICH VLADIMIR", "BAYER"], "similarity_index": 252},
        {"applicants_ifi": ["HIMMELMANN RICHARD A"], "similarity_index": 236},
        {"applicants_ifi": ["REICHENBACH SILVIO", "KIESER KUNO"], "similarity_index": 228},
        {"applicants_ifi": ["ZAHNRADFABRIK FRIEDRICHSHAFEN"], "similarity_index": 213},
        {"applicants_ifi": ["LOOSEN CHRISTIAN", "KIESER KUNO"], "similarity_index": 205},
        {"applicants_ifi": ["BVP", "BAYER", "SUKAVA SULO"], "similarity_index": 200},
        {"applicants_ifi": ["TSIBULEVSKIY ROMAN", "GREENBAUM BARRY"], "similarity_index": 197},
        {"applicants_ifi": ["PUCEL MARKUS", "KIESER KUNO"], "similarity_index": 195},
        {"applicants_ifi": ["PREDRAG SREBRO", "SHARMA CHIRAG"], "similarity_index": 190},
        {"applicants_ifi": ["TRW SEMICONDUCTOR", "SUKAVA SULO"], "similarity_index": 331},
        {"applicants_ifi": ["FACEBOOK", "BAYER", "KIESER KUNO"], "similarity_index": 200},
        {"applicants_ifi": ["KALFHAUS HEINRICH", "BVP"], "similarity_index": 299},
        {"applicants_ifi": ["DELTILOG", "SUKAVA SULO"], "similarity_index": 276},
        {"applicants_ifi": ["VANGUELOV BORISLAV", "ROUDENKO SERGEI"], "similarity_index": 252},
        {"applicants_ifi": ["BIRICH VLADIMIR"], "similarity_index": 236},
        {"applicants_ifi": ["SCHALLER KAI CHRISTIAN"], "similarity_index": 228},
        {"applicants_ifi": ["AIRBUS OPERATIONS", "BVP"], "similarity_index": 213},
        {"applicants_ifi": ["BAYER", "DELTILOG"], "similarity_index": 205},
        {"applicants_ifi": ["BAYER", "DELTILOG"], "similarity_index": 200},
        {"applicants_ifi": ["FACEBOOK", "SUKAVA SULO"], "similarity_index": 197},
        {"applicants_ifi": ["KIEFER INGE H"], "similarity_index": 195},
        {"applicants_ifi": ["MATTHIESEN UTE"], "similarity_index": 190}
    ]

    DATA_PUBLICATION = [
        {},
        {"applicants_ifi": [], "similarity_index": None},
        {"applicants_ifi": None, "similarity_index": 999},
        {"applicants_ifi": ["KIESER KUNO", "BAYER"], "similarity_index": 331},
        {"applicants_ifi": ["KIESER KUNO", "BAYER"], "similarity_index": 299},
        {"applicants_ifi": ["SUKAVA SULO", "BVP"], "similarity_index": 276},
        {"applicants_ifi": ["BIRICH VLADIMIR", "BAYER"], "similarity_index": 252},
        {"applicants_ifi": ["HIMMELMANN RICHARD A"], "similarity_index": 236},
        {"applicants_ifi": ["REICHENBACH SILVIO", "KIESER KUNO"], "similarity_index": 228},
        {"applicants_ifi": ["ZAHNRADFABRIK FRIEDRICHSHAFEN"], "similarity_index": 213},
        {"applicants_ifi": ["LOOSEN CHRISTIAN", "KIESER KUNO"], "similarity_index": 205},
        {"applicants_ifi": ["BVP", "BAYER", "SUKAVA SULO"], "similarity_index": 200},
        {"applicants_ifi": ["TSIBULEVSKIY ROMAN", "GREENBAUM BARRY"], "similarity_index": 197},
        {"applicants_ifi": ["PUCEL MARKUS", "KIESER KUNO"], "similarity_index": 195},
        {"applicants_ifi": ["PREDRAG SREBRO", "SHARMA CHIRAG"], "similarity_index": 190},
        {"applicants_ifi": ["TRW SEMICONDUCTOR", "SUKAVA SULO"], "similarity_index": 331},
        {"applicants_ifi": ["FACEBOOK", "BAYER", "KIESER KUNO"], "similarity_index": 200},
        {"applicants_ifi": ["KALFHAUS HEINRICH", "BVP"], "similarity_index": 299},
        {"applicants_ifi": ["DELTILOG", "SUKAVA SULO"], "similarity_index": 276},
        {"applicants_ifi": ["VANGUELOV BORISLAV", "ROUDENKO SERGEI"], "similarity_index": 252},
        {"applicants_ifi": ["BIRICH VLADIMIR"], "similarity_index": 236},
        {"applicants_ifi": ["SCHALLER KAI CHRISTIAN"], "similarity_index": 228},
        {"applicants_ifi": ["AIRBUS OPERATIONS", "BVP"], "similarity_index": 213},
        {"applicants_ifi": ["BAYER", "DELTILOG"], "similarity_index": 205},
        {"applicants_ifi": ["BAYER", "DELTILOG"], "similarity_index": 200},
        {"applicants_ifi": ["FACEBOOK", "SUKAVA SULO"], "similarity_index": 197},
        {"applicants_ifi": ["KIEFER INGE H"], "similarity_index": 195},
        {"applicants_ifi": ["MATTHIESEN UTE"], "similarity_index": 190}
    ]

    def test_simple_chart(self):
        results = similarity_profile(self.DATA, {})
        assert len(results['categories']) == 15
        assert len(results['datasource']) == 15
        assert results['datasource'][0]['applicant'] == 'BAYER'
        assert results['datasource'][0]['families'] == 7
        assert results['datasource'][0]['high'] == 331
        assert results['datasource'][0]['low'] == 200
        assert results['datasource'][0]['median'] == 205
        assert results['datasource'][0]['q1'] == 200
        assert results['datasource'][0]['q3'] == 275
        assert results['mean'] == 216.6

    def test_with_parameters(self):
        results = similarity_profile(self.DATA, {"similarity_profile_applicants_quantity": 5})
        assert len(results['categories']) == 5
        assert len(results['datasource']) == 5
        assert results['datasource'][3]['applicant'] == 'BVP'
        assert results['datasource'][3]['families'] == 4
        assert results['datasource'][3]['high'] == 299
        assert results['datasource'][3]['low'] == 200
        assert results['datasource'][3]['median'] == 244
        assert results['datasource'][3]['q1'] == 206
        assert results['datasource'][3]['q3'] == 287
        assert results['mean'] == 229.2

    def test_publication_search(self):
        results = similarity_profile(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['categories']) == 15
        assert len(results['datasource']) == 15
        assert results['datasource'][0]['applicant'] == 'BAYER'
        assert results['datasource'][0]['families'] == 7
        assert results['datasource'][0]['high'] == 331
        assert results['datasource'][0]['low'] == 200
        assert results['datasource'][0]['median'] == 205
        assert results['datasource'][0]['q1'] == 200
        assert results['datasource'][0]['q3'] == 275
        assert results['mean'] == 216.6


class TestPatentsByTechProfileChart:
    DATA = [
        {'tech_fields': None, 'applicants_ifi': None},
        {'tech_fields': [], 'applicants_ifi': []},
        {'tech_fields': None, 'applicants_ifi': ['PHILIPS']},
        {'tech_fields': [], 'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Chemical Engineering'], 'applicants_ifi': []},
        {'tech_fields': ['Chemical Engineering'], 'applicants_ifi': None},
        {'tech_fields': ['Chemical Engineering', 'Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Chemical Engineering', 'Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['SIEMENS']},
        {'tech_fields': ['Basic Materials Chemistry', 'Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': [
            'Chemical Engineering', 'Electrical Machinery, Apparatus, Energy', 'Optics', 'Semiconductors',
            'Surface Technology, Coating'
        ],
            'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Chemical Engineering', 'Semiconductors', 'Surface Technology, Coating', 'Transport'],
         'applicants_ifi': ['HITACHI']},
        {'tech_fields': ['Chemical Engineering', 'Electrical Machinery, Apparatus, Energy', 'Optics', 'Semiconductors'],
         'applicants_ifi': ['RCA']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['RCA']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['RCA']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Optics', 'Semiconductors'],
         'applicants_ifi': ['THOMSON CSF']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['TEXAS INSTR']},
        {'tech_fields': ['Chemical Engineering', 'Machine Tools', 'Semiconductors'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Surface Technology, Coating'],
         'applicants_ifi': ['SILTRONIC', 'UNIV ULM']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['POST OFFICE']},
        {'tech_fields': ['Chemical Engineering', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILCO FORD']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Surface Technology, Coating'],
         'applicants_ifi': ['ROCKWELL']},
        {'tech_fields': ['Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['LABO ELECTRONIQUE PHYSIQUE']},
        {'tech_fields': ['Chemical Engineering', 'Surface Technology, Coating'],
         'applicants_ifi': ['SIEMENS']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['STC', 'NORTHERN TELECOM EURO']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['MASSACHUSETTS INST']},
        {'tech_fields': ['Other Consumer Goods', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['TOKYO SHIBAURA ELECTRIC']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['SIEMENS']}
    ]

    DATA_PUBLICATION = [
        {'tech_fields': None, 'applicants_ifi': None},
        {'tech_fields': [], 'applicants_ifi': []},
        {'tech_fields': None, 'applicants_ifi': ['PHILIPS']},
        {'tech_fields': [], 'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Chemical Engineering'], 'applicants_ifi': []},
        {'tech_fields': ['Chemical Engineering'], 'applicants_ifi': None},
        {'tech_fields': ['Chemical Engineering', 'Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Chemical Engineering', 'Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['SIEMENS']},
        {'tech_fields': ['Basic Materials Chemistry', 'Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': [
            'Chemical Engineering', 'Electrical Machinery, Apparatus, Energy', 'Optics', 'Semiconductors',
            'Surface Technology, Coating'
        ],
            'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Chemical Engineering', 'Semiconductors', 'Surface Technology, Coating', 'Transport'],
         'applicants_ifi': ['HITACHI']},
        {'tech_fields': ['Chemical Engineering', 'Electrical Machinery, Apparatus, Energy', 'Optics', 'Semiconductors'],
         'applicants_ifi': ['RCA']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['RCA']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['RCA']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Optics', 'Semiconductors'],
         'applicants_ifi': ['THOMSON CSF']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['TEXAS INSTR']},
        {'tech_fields': ['Chemical Engineering', 'Machine Tools', 'Semiconductors'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Surface Technology, Coating'],
         'applicants_ifi': ['SILTRONIC', 'UNIV ULM']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['POST OFFICE']},
        {'tech_fields': ['Chemical Engineering', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILCO FORD']},
        {'tech_fields': [
            'Chemical Engineering', 'Materials, Metallurgy', 'Semiconductors', 'Surface Technology, Coating'
        ],
            'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Surface Technology, Coating'],
         'applicants_ifi': ['ROCKWELL']},
        {'tech_fields': ['Optics', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['LABO ELECTRONIQUE PHYSIQUE']},
        {'tech_fields': ['Chemical Engineering', 'Surface Technology, Coating'],
         'applicants_ifi': ['SIEMENS']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['PHILIPS']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['STC', 'NORTHERN TELECOM EURO']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['MASSACHUSETTS INST']},
        {'tech_fields': ['Other Consumer Goods', 'Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['TOKYO SHIBAURA ELECTRIC']},
        {'tech_fields': ['Semiconductors', 'Surface Technology, Coating'],
         'applicants_ifi': ['SIEMENS']}
    ]

    def test_simple_chart(self):
        results = tech_profile(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 15
        assert len(results['datasource']['yAxis']) == 10
        assert len(results['datasource']['values']) == 47
        assert results['datasource']['values'][0]['tech_fields'] == 'Surface Technology, Coating'
        assert results['datasource']['values'][0]['x'] == 0
        assert results['datasource']['values'][0]['y'] == 0
        assert results['datasource']['values'][0]['value'] == 7
        assert results['total'] == 75

    def test_with_parameters(self):
        results = tech_profile(self.DATA, {"tech_profile_applicants_quantity": 5, "tech_profile_quantity": 3})
        assert len(results['datasource']['xAxis']) == 5
        assert len(results['datasource']['yAxis']) == 3
        assert len(results['datasource']['values']) == 15
        assert results['datasource']['values'][4]['tech_fields'] == 'Semiconductors'
        assert results['datasource']['values'][4]['x'] == 1
        assert results['datasource']['values'][4]['y'] == 1
        assert results['datasource']['values'][4]['value'] == 3
        assert results['total'] == 40

    def test_without_documents(self):
        results = tech_profile([], {})
        assert len(results['datasource']['xAxis']) == 0
        assert len(results['datasource']['yAxis']) == 0
        assert len(results['datasource']['values']) == 0
        assert results['total'] == 0

    def test_publication_search(self):
        results = tech_profile(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']['xAxis']) == 15
        assert len(results['datasource']['yAxis']) == 10
        assert len(results['datasource']['values']) == 47
        assert results['datasource']['values'][0]['tech_fields'] == 'Surface Technology, Coating'
        assert results['datasource']['values'][0]['x'] == 0
        assert results['datasource']['values'][0]['y'] == 0
        assert results['datasource']['values'][0]['value'] == 7
        assert results['total'] == 75


class TestPatentsByOverTimeChart:
    DATA = [
        {},
        {'priority_date': '1976-08-30', 'applicants_ifi': []},
        {'priority_date': '1976-08-30', 'applicants_ifi': None},
        {'priority_date': '', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': None, 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1976-08-30', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1973-02-13', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1979-03-19', 'applicants_ifi': ['SIEMENS']},
        {'priority_date': '1979-04-24', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1973-05-01', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1974-03-27', 'applicants_ifi': ['HITACHI']},
        {'priority_date': '1968-02-26', 'applicants_ifi': ['RCA']},
        {'priority_date': '1973-08-16', 'applicants_ifi': ['RCA']},
        {'priority_date': '1971-08-27', 'applicants_ifi': ['RCA']},
        {'priority_date': '1990-04-24', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1976-05-11', 'applicants_ifi': ['THOMSON CSF']},
        {'priority_date': '1962-06-28', 'applicants_ifi': ['TEXAS INSTR']},
        {'priority_date': '1976-09-03', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '2009-11-25', 'applicants_ifi': ['SILTRONIC', 'UNIV ULM']},
        {'priority_date': '1976-04-29', 'applicants_ifi': ['POST OFFICE']},
        {'priority_date': '1967-01-03', 'applicants_ifi': ['PHILCO FORD']},
        {'priority_date': '1976-02-06', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1973-03-24', 'applicants_ifi': ['ROCKWELL']},
        {'priority_date': '1979-12-07', 'applicants_ifi': ['LABO ELECTRONIQUE PHYSIQUE']},
        {'priority_date': '1963-11-05', 'applicants_ifi': ['SIEMENS']},
        {'priority_date': '1971-07-13', 'applicants_ifi': ['PHILIPS']},
        {'priority_date': '1989-07-26', 'applicants_ifi': ['STC', 'NORTHERN TELECOM EURO']},
        {'priority_date': '1966-11-25', 'applicants_ifi': ['MASSACHUSETTS INST']},
        {'priority_date': '1969-03-18', 'applicants_ifi': ['TOKYO SHIBAURA ELECTRIC']},
        {'priority_date': '1984-10-10', 'applicants_ifi': ['SIEMENS']}
    ]

    DATA_PUBLICATION = [
        {},
        {'publn_date': '1976-08-30', 'applicants_ifi': []},
        {'publn_date': '1976-08-30', 'applicants_ifi': None},
        {'publn_date': '', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': None, 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1976-08-30', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1973-02-13', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1979-03-19', 'applicants_ifi': ['SIEMENS']},
        {'publn_date': '1979-04-24', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1973-05-01', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1974-03-27', 'applicants_ifi': ['HITACHI']},
        {'publn_date': '1968-02-26', 'applicants_ifi': ['RCA']},
        {'publn_date': '1973-08-16', 'applicants_ifi': ['RCA']},
        {'publn_date': '1971-08-27', 'applicants_ifi': ['RCA']},
        {'publn_date': '1990-04-24', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1976-05-11', 'applicants_ifi': ['THOMSON CSF']},
        {'publn_date': '1962-06-28', 'applicants_ifi': ['TEXAS INSTR']},
        {'publn_date': '1976-09-03', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '2009-11-25', 'applicants_ifi': ['SILTRONIC', 'UNIV ULM']},
        {'publn_date': '1976-04-29', 'applicants_ifi': ['POST OFFICE']},
        {'publn_date': '1967-01-03', 'applicants_ifi': ['PHILCO FORD']},
        {'publn_date': '1976-02-06', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1973-03-24', 'applicants_ifi': ['ROCKWELL']},
        {'publn_date': '1979-12-07', 'applicants_ifi': ['LABO ELECTRONIQUE PHYSIQUE']},
        {'publn_date': '1963-11-05', 'applicants_ifi': ['SIEMENS']},
        {'publn_date': '1971-07-13', 'applicants_ifi': ['PHILIPS']},
        {'publn_date': '1989-07-26', 'applicants_ifi': ['STC', 'NORTHERN TELECOM EURO']},
        {'publn_date': '1966-11-25', 'applicants_ifi': ['MASSACHUSETTS INST']},
        {'publn_date': '1969-03-18', 'applicants_ifi': ['TOKYO SHIBAURA ELECTRIC']},
        {'publn_date': '1984-10-10', 'applicants_ifi': ['SIEMENS']}
    ]

    def test_simple_chart(self):
        results = over_time(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 15
        assert len(results['datasource']['yAxis']) == 14
        assert len(results['datasource']['values']) == 23
        assert results['datasource']['values'][0][0] == 0
        assert results['datasource']['values'][0][1] == 8
        assert results['datasource']['values'][0][2] == 3
        assert results['total'] == 26

    def test_with_parameters(self):
        results = over_time(self.DATA, {"over_time_applicants_quantity": 3})
        assert len(results['datasource']['xAxis']) == 3
        assert len(results['datasource']['yAxis']) == 8
        assert len(results['datasource']['values']) == 11
        assert results['datasource']['values'][8][0] == 2
        assert results['datasource']['values'][8][1] == 1
        assert results['datasource']['values'][8][2] == 1
        assert results['total'] == 14

    def test_publication_search(self):
        results = over_time(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']['xAxis']) == 15
        assert len(results['datasource']['yAxis']) == 14
        assert len(results['datasource']['values']) == 23
        assert results['datasource']['values'][0][0] == 0
        assert results['datasource']['values'][0][1] == 8
        assert results['datasource']['values'][0][2] == 3
        assert results['total'] == 26


class TestPatentsByAuthoritiesProfileChart:
    DATA = [
        {'authorities': None, 'applicants_ifi': None},
        {'authorities': [], 'applicants_ifi': []},
        {'authorities': None, 'applicants_ifi': ['PHILIPS']},
        {'authorities': [], 'applicants_ifi': ['PHILIPS']},
        {'authorities': ['JP'], 'applicants_ifi': None},
        {'authorities': ['JP'], 'applicants_ifi': []},
        {'authorities': ['AR', 'AU', 'BR', 'CA', 'DE', 'FR', 'GB', 'IT', 'JP', 'NL', 'SE', 'US'],
         'applicants_ifi': ['PHILIPS']},
        {'authorities': ['IT', 'DE', 'FR', 'JP', 'US', 'GB'], 'applicants_ifi': ['PHILIPS']},
        {'authorities': ['DE', 'FR', 'GB', 'JP', 'US'], 'applicants_ifi': ['SIEMENS']},
        {'authorities': ['DE', 'FR', 'GB', 'IT', 'JP', 'NL', 'US'], 'applicants_ifi': ['PHILIPS']},
        {'authorities': ['FR', 'DE', 'US', 'IT', 'BE', 'CA', 'AU', 'NL', 'CH', 'GB', 'JP'],
         'applicants_ifi': ['PHILIPS']},
        {'authorities': ['JP', 'US'], 'applicants_ifi': ['HITACHI']},
        {'authorities': ['CA', 'FR', 'US', 'GB', 'DE'], 'applicants_ifi': ['RCA']},
        {'authorities': ['US'], 'applicants_ifi': ['RCA']},
        {'authorities': ['GB', 'JP', 'US', 'CA', 'DE'], 'applicants_ifi': ['RCA']},
        {'authorities': ['DE', 'EP', 'JP', 'NL', 'US'], 'applicants_ifi': ['PHILIPS']},
        {'authorities': ['US'], 'applicants_ifi': ['THOMSON CSF']},
        {'authorities': ['US'], 'applicants_ifi': ['TEXAS INSTR']},
        {'authorities': ['CA', 'DE', 'FR', 'GB', 'JP', 'NL', 'US'], 'applicants_ifi': ['PHILIPS']},
        {'authorities': ['DE'], 'applicants_ifi': ['SILTRONIC', 'UNIV ULM']},
        {'authorities': ['DE', 'JP', 'US'], 'applicants_ifi': ['POST OFFICE']},
        {'authorities': ['US', 'GB', 'DE', 'FR'], 'applicants_ifi': ['PHILCO FORD']},
        {'authorities': ['CN'], 'applicants_ifi': ['EPI SOLUTION']},
        {'authorities': ['CA', 'DE', 'FR', 'GB', 'JP', 'US'], 'applicants_ifi': ['PHILIPS']},
        {'authorities': ['US'], 'applicants_ifi': ['ROCKWELL']},
        {'authorities': ['CA', 'DE', 'FR', 'GB', 'JP', 'US'], 'applicants_ifi': ['LABO ELECTRONIQUE PHYSIQUE']},
        {'authorities': ['US', 'SE', 'GB', 'NL', 'CH', 'DE'], 'applicants_ifi': ['SIEMENS']},
        {'authorities': ['US'], 'applicants_ifi': ['PHILIPS']},
        {'authorities': ['GB'], 'applicants_ifi': ['STC', 'NORTHERN TELECOM EURO']},
        {'authorities': ['US'], 'applicants_ifi': ['MASSACHUSETTS INST']},
        {'authorities': ['JP', 'US'], 'applicants_ifi': ['TOKYO SHIBAURA ELECTRIC']}
    ]

    DATA_PUBLICATION = [
        {'publication_number': None, 'applicants_ifi': None},
        {'publication_number': 'AR-123456-A1', 'applicants_ifi': []},
        {'publication_number': None, 'applicants_ifi': ['PHILIPS']},
        {'publication_number': 'DE-123456-A1', 'applicants_ifi': ['PHILIPS']},
        {'publication_number': 'FR-123456-A1', 'applicants_ifi': None},
        {'publication_number': 'SE-123456-A1', 'applicants_ifi': []},
        {'publication_number': 'GB-123456-A1', 'applicants_ifi': ['PHILIPS']},
        {'publication_number': 'NL-123456-A1', 'applicants_ifi': ['PHILIPS']},
        {'publication_number': 'CH-123456-A1', 'applicants_ifi': ['SIEMENS']},
        {'publication_number': 'US-123456-A1', 'applicants_ifi': ['PHILIPS']},
        {'publication_number': 'DE-123456-A1', 'applicants_ifi': ['PHILIPS']},
        {'publication_number': 'US-123456-A1', 'applicants_ifi': ['HITACHI']},
        {'publication_number': 'CA-123456-A1', 'applicants_ifi': ['RCA']},
        {'publication_number': 'US-123456-A1', 'applicants_ifi': ['RCA']},
        {'publication_number': 'FR-123456-A1', 'applicants_ifi': ['RCA']},
        {'publication_number': 'EP-123456-A1', 'applicants_ifi': ['PHILIPS']},
        {'publication_number': 'EP-123456-A1', 'applicants_ifi': ['THOMSON CSF']},
    ]

    def test_simple_chart(self):
        results = authorities_profile(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 15
        assert len(results['datasource']['yAxis']) == 16
        assert len(results['datasource']['values']) == 52
        assert results['datasource']['values'][0][0] == 0
        assert results['datasource']['values'][0][1] == 0
        assert results['datasource']['values'][0][2] == 8
        assert results['total'] == 100

    def test_with_parameters(self):
        results = authorities_profile(self.DATA, {"authorities_profile_applicants_quantity": 5,
                                                  "authorities_profile_quantity": 10})
        assert len(results['datasource']['xAxis']) == 5
        assert len(results['datasource']['yAxis']) == 10
        assert len(results['datasource']['values']) == 26
        assert results['datasource']['values'][10][0] == 1
        assert results['datasource']['values'][10][1] == 0
        assert results['datasource']['values'][10][2] == 3
        assert results['total'] == 74

    def test_without_documents(self):
        results = authorities_profile([], {})
        assert len(results['datasource']['xAxis']) == 0
        assert len(results['datasource']['yAxis']) == 0
        assert len(results['datasource']['values']) == 0
        assert results['total'] == 0

    def test_publication_search(self):
        results = authorities_profile(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']['xAxis']) == 5
        assert len(results['datasource']['yAxis']) == 8
        assert len(results['datasource']['values']) == 11
        assert results['datasource']['values'][0][0] == 0
        assert results['datasource']['values'][0][1] == 0
        assert results['datasource']['values'][0][2] == 1
        assert results['total'] == 12


class TestPatentsByPortfolioAnalyticsProfileChart:
    DATA = [
        {'tech_fields': ['Basic Communication Processes'],
         'impact_weighted': 0.9, 'risk_weighted': 0.3, 'consistency': 0, 'impact': 1, 'recency_years': 9.6123295,
         'recency': 0, 'risk': 0, 'market_coverage': 1, 'citation_backward_count': 2, 'citation_forward_count': 11,
         'applicants_ifi': ['IBM'], 'claims_exist': 0, 'description_exists': 0,
         'cpc': ['H01M2/1077', 'H01M2220/20', 'Y02E60/10'], 'ipc': ['H01M2/10', 'B01D53/94'],
         'technological_distance': 0.66},
        {'tech_fields': ['Audio-Visual Technology', 'Computer Technology', 'Furniture, Games'],
         'impact_weighted': 1.8, 'risk_weighted': 1.8, 'consistency': 0, 'impact': 2, 'recency_years': 3.9630136,
         'recency': 1, 'risk': 2, 'market_coverage': 11, 'citation_backward_count': 5, 'citation_forward_count': 20,
         'applicants_ifi': ['IBM DEUT'], 'claims_exist': 0, 'description_exists': 0,
         'cpc': ['B60L2200/44', 'B60L58/20', 'B60L58/21', 'B66F9/24', 'H02J7/0013', 'H02J7/0025'],
         'ipc': ['B60L11/18', 'B66F9/24', 'H02J7/00'], 'technological_distance': 0.37},
        {'tech_fields': ['Computer Technology', 'Measurement'],
         'impact_weighted': 0.3, 'risk_weighted': 0.3, 'consistency': 0.5781516, 'impact': 0, 'recency_years': 14.16849,
         'recency': 0, 'risk': 0, 'market_coverage': 1, 'citation_backward_count': 2, 'citation_forward_count': 26,
         'applicants_ifi': ['MICROSOFT'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.58},
        {'tech_fields': ['Computer Technology', 'Textile and Paper Machines'],
         'impact_weighted': 0.3, 'risk_weighted': 0.3, 'consistency': 0.74289805, 'impact': 0, 'recency_years': 8.44931,
         'recency': 0, 'risk': 0, 'market_coverage': 1, 'citation_backward_count': 12, 'citation_forward_count': 1,
         'applicants_ifi': ['NEC'], 'claims_exist': 1, 'description_exists': 1, 'technological_distance': 0.79},
        {'tech_fields': ['Audio-Visual Technology', 'Basic Communication Processes', 'Computer Technology',
                         'Telecommunications', 'Textile and Paper Machines'],
         'impact_weighted': 1.8, 'risk_weighted': 0.9, 'consistency': 0.75025374, 'impact': 2, 'recency_years': 5.35205,
         'recency': 0, 'risk': 1, 'market_coverage': 4, 'citation_backward_count': 10, 'citation_forward_count': 24,
         'applicants_ifi': ['GOLD STAR'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.57},
        {'tech_fields': ['Basic Communication Processes', 'Computer Technology'],
         'impact_weighted': 0.3, 'risk_weighted': 0.3, 'consistency': 0.8065438, 'impact': 0, 'recency_years': 7.021917,
         'recency': 0, 'risk': 0, 'market_coverage': 2, 'citation_backward_count': 16, 'citation_forward_count': 0,
         'applicants_ifi': ['IND TECH RES INST'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.82},
        {'tech_fields': ['Digital Communication', 'Thermal Processes and Apparatus'],
         'impact_weighted': None, 'risk_weighted': None, 'consistency': None, 'impact': None, 'recency_years': None,
         'recency': None, 'risk': None, 'market_coverage': 1, 'citation_backward_count': 8,
         'citation_forward_count': None,
         'applicants_ifi': ['SICHUAN HONGMEIELLIGENT'], 'claims_exist': 0, 'description_exists': 0,
         'technological_distance': 0.62},
        {'tech_fields': ['Environmental Technology', 'Telecommunications'],
         'impact_weighted': 0.9, 'risk_weighted': 0.3, 'consistency': 0, 'impact': 1, 'recency_years': 11.163231,
         'recency': 1, 'risk': 0, 'market_coverage': 1, 'citation_backward_count': 3, 'citation_forward_count': 3,
         'applicants_ifi': ['PACKARD BIOSCIENCE', 'JORDANOV VALENTIN T'], 'claims_exist': 0,
         'description_exists': 0},
        {'tech_fields': ['Basic Communication Processes', 'Biotechnology', 'Computer Technology'],
         'impact_weighted': 0.3, 'risk_weighted': 0.3, 'consistency': 0.71377474, 'impact': 0, 'recency_years': 6.86548,
         'recency': 0, 'risk': 0, 'market_coverage': 3, 'citation_backward_count': 6, 'citation_forward_count': 8,
         'applicants_ifi': ['DAEWOO EDUCATIONAL FOUND', 'KIM HYOUNG DO', 'YOO SEUNG WHA', 'CHOIOUNG HEE'],
         'claims_exist': 1, 'description_exists': 1, 'technological_distance': 0.14},
        {'tech_fields': ['Control', 'Digital Communication'],
         'impact_weighted': 0.3, 'risk_weighted': 0.9, 'consistency': 0.8696728, 'impact': 0, 'recency_years': 3.316438,
         'recency': 1, 'risk': 1, 'market_coverage': 6, 'citation_backward_count': 8, 'citation_forward_count': 11,
         'applicants_ifi': ['SHARP'], 'claims_exist': 1, 'description_exists': 1, 'technological_distance': 0.6},
        {'tech_fields': ['Basic Communication Processes', 'Computer Technology', 'Digital Communication',
                         'Measurement', 'Transport'],
         'impact_weighted': 0.9, 'risk_weighted': 0.9, 'consistency': 0.95376945, 'impact': 1, 'recency_years': 9.92328,
         'recency': 0, 'risk': 1, 'market_coverage': 3, 'citation_backward_count': 29, 'citation_forward_count': 13,
         'applicants_ifi': ['BOSCH ROBERT'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': None},
        {'tech_fields': ['Basic Communication Processes', 'Computer Technology'],
         'impact_weighted': None, 'risk_weighted': None, 'consistency': 0, 'impact': None, 'recency_years': None,
         'recency': None, 'risk': None, 'market_coverage': 3, 'citation_backward_count': 22,
         'citation_forward_count': 0,
         'applicants_ifi': ['INFINEON'], 'claims_exist': 0, 'description_exists': 0,
         'technological_distance': 0.69},
        {'tech_fields': ['Computer Technology', 'Control', 'Digital Communication'],
         'impact_weighted': 0.9, 'risk_weighted': 0.3, 'consistency': 0.6852386, 'impact': 1, 'recency_years': 2.013698,
         'recency': 2, 'risk': 0, 'market_coverage': 5, 'citation_backward_count': 23, 'citation_forward_count': 14,
         'applicants_ifi': ['SIEMENS'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.42},
        {'tech_fields': ['Basic Communication Processes'],
         'impact_weighted': 0.3, 'risk_weighted': 0.3, 'consistency': 0.63966274, 'impact': 0,
         'recency_years': 2.3424656,
         'recency': 2, 'risk': 0, 'market_coverage': 1, 'citation_backward_count': 1, 'citation_forward_count': 0,
         'applicants_ifi': ['CHEN BRYAN C', 'CHIANG CHEN M'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.52},
        {'tech_fields': ['Audio-Visual Technology', 'Computer Technology', 'Textile and Paper Machines'],
         'impact_weighted': 0.9, 'risk_weighted': 0.9, 'consistency': 0, 'impact': 1, 'recency_years': 4.049315,
         'recency': 1, 'risk': 1, 'market_coverage': 5, 'citation_backward_count': 9, 'citation_forward_count': 7,
         'applicants_ifi': ['VARIANSOC'], 'claims_exist': 0, 'description_exists': 0,
         'technological_distance': 1},
        {'tech_fields': ['Basic Communication Processes', 'Measurement'],
         'impact_weighted': None, 'risk_weighted': None, 'consistency': 0.36265445, 'impact': None,
         'recency_years': None,
         'recency': None, 'risk': None, 'market_coverage': 1, 'citation_backward_count': None,
         'citation_forward_count': None,
         'applicants_ifi': ['HEBEI XUHUI ELECTRIC'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.8},
        {'tech_fields': ['Computer Technology', 'Measurement'],
         'impact_weighted': 0.3, 'risk_weighted': 0.9, 'consistency': 0.6331372, 'impact': 0,
         'recency_years': 5.2246575,
         'recency': 2, 'risk': 1, 'market_coverage': 3, 'citation_backward_count': 6, 'citation_forward_count': 15,
         'applicants_ifi': ['SAMSUNG ELECTRONICS'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.82},
        {'tech_fields': ['Computer Technology', 'Other Special Machines'],
         'impact_weighted': None, 'risk_weighted': None, 'consistency': None, 'impact': None, 'recency_years': None,
         'recency': None, 'risk': None, 'market_coverage': 1, 'citation_backward_count': 4,
         'citation_forward_count': None,
         'applicants_ifi': ['UNIV XI AN JIAOTONG'], 'claims_exist': 0, 'description_exists': 0,
         'technological_distance': 0.6},
        {'tech_fields': ['Transport'],
         'impact_weighted': None, 'risk_weighted': None, 'consistency': 0.6749899, 'impact': None,
         'recency_years': None,
         'recency': None, 'risk': None, 'market_coverage': 1, 'citation_backward_count': 4,
         'citation_forward_count': None,
         'applicants_ifi': ['WELTMEISTERELLIGENT TRAVEL TECH SHANGHAI'], 'claims_exist': 1,
         'description_exists': 1, 'technological_distance': 0.54},
        {'tech_fields': ['Basic Communication Processes', 'Computer Technology', 'Digital Communication'],
         'impact_weighted': 0.9, 'risk_weighted': 0.9, 'consistency': 0, 'impact': 1, 'recency_years': None,
         'recency': 0, 'risk': 1, 'market_coverage': 15, 'citation_backward_count': 58, 'citation_forward_count': 0,
         'applicants_ifi': ['ALIBABA SERVICES', 'XU DAFENG', 'LI SANPING'], 'claims_exist': 0,
         'description_exists': 0, 'technological_distance': None},
        {'tech_fields': ['Audio-Visual Technology', 'Computer Technology'],
         'impact_weighted': 0.9, 'risk_weighted': 0.3, 'consistency': 0, 'impact': 1, 'recency_years': 4.4739728,
         'recency': 1, 'risk': 0, 'market_coverage': 1, 'citation_backward_count': 5, 'citation_forward_count': 10,
         'applicants_ifi': ['IBM'], 'claims_exist': 0, 'description_exists': 0, 'technological_distance': 0.74},
        {'tech_fields': ['Environmental Technology'],
         'impact_weighted': None, 'risk_weighted': None, 'consistency': None, 'impact': None,
         'recency_years': None,
         'recency': None, 'risk': None, 'market_coverage': 1, 'citation_backward_count': 6,
         'citation_forward_count': None,
         'applicants_ifi': ['WUHU MIDEA KITCHEN & BATH APPLIANCES MFG', 'MIDEA'], 'claims_exist': 0,
         'description_exists': 0, 'technological_distance': 0.68},
        {'tech_fields': ['Audio-Visual Technology', 'Computer Technology', 'Textile and Paper Machines'],
         'impact_weighted': 1.8, 'risk_weighted': 1.8, 'consistency': 0.6767523, 'impact': 2,
         'recency_years': 3.0109589,
         'recency': 2, 'risk': 2, 'market_coverage': 6, 'citation_backward_count': 13, 'citation_forward_count': 0,
         'applicants_ifi': ['ESSELTE DYMO'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.69},
        {'tech_fields': ['Audio-Visual Technology', 'Digital Communication'],
         'impact_weighted': None, 'risk_weighted': None, 'consistency': 0, 'impact': None, 'recency_years': None,
         'recency': None, 'risk': None, 'market_coverage': 1, 'citation_backward_count': 32,
         'citation_forward_count': 18,
         'applicants_ifi': ['WANG ZHONGFENG', 'BROADCOM'], 'claims_exist': 0, 'description_exists': 0,
         'technological_distance': None},
        {'tech_fields': ['Basic Communication Processes', 'Biotechnology', 'Computer Technology', 'Control',
                         'Organic Fine Chemistry'],
         'impact_weighted': 0.3, 'risk_weighted': 0.3, 'consistency': 0.38848335, 'impact': 0,
         'recency_years': 5.6328769,
         'recency': 0, 'risk': 0, 'market_coverage': 1, 'citation_backward_count': 6, 'citation_forward_count': 2,
         'applicants_ifi': ['SU XING', 'INTEL'], 'claims_exist': 1, 'description_exists': 1,
         'technological_distance': 0.74}
    ]

    def test_simple_chart(self):
        results = portfolio_analytics_profile(self.DATA, {})
        assert len(results['datasource']) == 10
        assert results['datasource'][0]['competitor'] == 'IBM'
        assert results['datasource'][0]['marker']['radius'] == 18.18
        assert results['datasource'][0]['publ_quantity'] == 2
        assert results['datasource'][0]['x'] == 0.9
        assert results['datasource'][0]['y'] == 0.3

    def test_with_parameters_01(self):
        results = portfolio_analytics_profile(self.DATA, {"portfolio_analytics_applicants_quantity": 5})
        assert len(results['datasource']) == 5
        assert results['datasource'][4]['competitor'] == 'GOLD STAR'
        assert results['datasource'][4]['marker']['radius'] == 16.67
        assert results['datasource'][4]['publ_quantity'] == 1
        assert results['datasource'][4]['x'] == 1.8
        assert results['datasource'][4]['y'] == 0.9

    def test_with_parameters_02(self):
        results = portfolio_analytics_profile(self.DATA, {
            "portfolio_analytics_profile_indicators": [{"axis": "x", "property": "impact_share"},
                                                       {"axis": "y", "property": "recency_years"}]})
        assert len(results['datasource']) == 10
        assert results['datasource'][0]['competitor'] == 'IBM'
        assert results['datasource'][0]['marker']['radius'] == 18.18
        assert results['datasource'][0]['publ_quantity'] == 2
        assert results['datasource'][0]['x'] == 0.0
        assert results['datasource'][0]['y'] == 7.04

    def test_with_parameters_03(self):
        results = portfolio_analytics_profile(self.DATA, {
            "portfolio_analytics_profile_indicators": [{"axis": "x", "property": "risk_share"},
                                                       {"axis": "y", "property": "consistency"}]})
        assert len(results['datasource']) == 10
        assert results['datasource'][2]['competitor'] == 'MICROSOFT'
        assert results['datasource'][2]['marker']['radius'] == 10.0
        assert results['datasource'][2]['publ_quantity'] == 1
        assert results['datasource'][2]['x'] == 0.0
        assert results['datasource'][2]['y'] == 0.42

    def test_with_parameters_04(self):
        results = portfolio_analytics_profile(self.DATA, {
            "portfolio_analytics_profile_indicators": [{"axis": "x", "property": "citation_backward_count"},
                                                       {"axis": "y", "property": "citation_forward_count"}]})
        assert len(results['datasource']) == 10
        assert results['datasource'][0]['competitor'] == 'IBM'
        assert results['datasource'][0]['marker']['radius'] == 18.18
        assert results['datasource'][0]['publ_quantity'] == 2
        assert results['datasource'][0]['x'] == 3.5
        assert results['datasource'][0]['y'] == 10.5

    def test_with_parameters_05(self):
        results = portfolio_analytics_profile(self.DATA, {
            "portfolio_analytics_profile_indicators": [{"axis": "x", "property": "technological_distance"},
                                                       {"axis": "y", "property": "recency_years"}]})
        assert len(results['datasource']) == 10
        assert results['datasource'][0]['competitor'] == 'IBM'
        assert results['datasource'][0]['marker']['radius'] == 18.18
        assert results['datasource'][0]['publ_quantity'] == 2
        assert results['datasource'][0]['x'] == 0.7
        assert results['datasource'][0]['y'] == 7.04

    def test_without_documents(self):
        results = portfolio_analytics_profile([], {})
        assert len(results['datasource']) == 0

    def test_without_applicants(self):
        data = deepcopy(self.DATA)
        for d in data:
            d['applicants_ifi'] = []
        results = portfolio_analytics_profile(data, {})
        assert len(results['datasource']) == 0


class TestApplicantLegalStatusDistribution:
    DATA = [
        {'applicants_ifi': ['SILICON GRAPHICS'], 'publn_no_rep': 'US6545685B1',
         'legal_statuses': ['ceased', 'unknown']},
        {'applicants_ifi': ['KONINKL KPN'], 'publn_no_rep': 'EP1296252A1', 'legal_statuses': ['expired', 'active']},
        {'applicants_ifi': ['CHECK POINT SOFTWARE'], 'publn_no_rep': 'US5835726A', 'legal_statuses': ['expired']},
        {'applicants_ifi': ['IBM'], 'publn_no_rep': 'US20050114686A1',
         'legal_statuses': ['expired', 'unknown', 'granted']},
        {'publn_no_rep': 'CN103037064A', 'legal_statuses': ['active'],
         'applicants_ifi': ['INVENTEC APPLIANCES SHANGHAI', 'INVENTEC APPLIANCES NANCHANG', 'INVENTEC APPLIANCES',
                            'YINGHUADA SHANGHAI ELECTRONIC']
         },
        {'applicants_ifi': ['HUANG CHUN-HSIANG', 'LU TAI-LING', 'WANG CHIH-KUANG', 'HTC'],
         'publn_no_rep': 'US20120291121A1', 'legal_statuses': ['expired', 'unknown']},
        {'applicants_ifi': ['CHATTERJEE MANJIRNATH', 'LIU ERIC', 'WOLF NATHANIEL', 'PALM'],
         'publn_no_rep': 'WO2010014845A2', 'legal_statuses': ['active']},
        {'applicants_ifi': ['PALM'], 'publn_no_rep': 'US7124300B1', 'legal_statuses': ['expired']},
        {'applicants_ifi': ['ARCHBOLD DAVID', 'WESTBROOKOTT D', 'FRISBIE JAMES V', 'WEST'],
         'publn_no_rep': 'US9305042B1', 'legal_statuses': ['expired_fee_related', 'active']},
        {'applicants_ifi': ['LEE JONG-IL', 'YI NAM-SU', 'FASOO'], 'publn_no_rep': 'WO2012148080A2',
         'legal_statuses': ['unknown']},
        {'applicants_ifi': ['SILICON GRAPHICS', 'FORAN JAMES L'], 'publn_no_rep': 'WO2002007092A2',
         'legal_statuses': ['expired']},
        {'applicants_ifi': ['HTC'], 'publn_no_rep': 'EP2725473A1', 'legal_statuses': ['expired']},
        {'applicants_ifi': ['PALMSOURCE'], 'publn_no_rep': 'US7346778B1', 'legal_statuses': ['unknown']},
        {'applicants_ifi': ['LENOVO SINGAPORE PTE'], 'publn_no_rep': 'JP2017138846A', 'legal_statuses': ['active']},
        {'applicants_ifi': ['ZONE LABS', 'IBM'], 'publn_no_rep': 'US5987611A',
         'legal_statuses': ['expired', 'active']},
        {'applicants_ifi': ['NINGBO PULUODI COMPUTERIZED FLAT KNITTING MACHINE'], 'publn_no_rep': 'CN102071528A',
         'legal_statuses': ['active']},
        {'applicants_ifi': ['FUJITSU', 'YAMANE YASUSHI', 'MIYAZAKI KATSUYUKI'], 'publn_no_rep': 'WO2007080629A1',
         'legal_statuses': ['expired']},
        {'applicants_ifi': ['DIGITAL EQUIP'], 'publn_no_rep': 'US5235642A', 'legal_statuses': ['expired']},
        {'applicants_ifi': ['IBM', 'IBM'], 'publn_no_rep': 'US20070016958A1', 'legal_statuses': ['expired']},
        {'applicants_ifi': ['KONINKL PHILIPS ELECTRONICS', 'TOWNSEND STEPHEN', 'KONINKLIJKE PHILIPS'],
         'publn_no_rep': 'GB0206552D0', 'legal_statuses': ['expired']},
        {'applicants_ifi': ['KONICA MINOLTA BUSINESS', 'IBM'], 'publn_no_rep': 'JP2007226293A',
         'legal_status': ['granted']},
        {'applicants_ifi': ['DIXON CHRISTOPHER J', 'PINCKNEY THOMAS', 'MCAFEE'],
         'publn_no_rep': 'US20060253578A1', 'legal_statuses': ['expired']},
        {'applicants_ifi': ['SONY ERICSSON MOBILE COMM AB', 'CARPENTER PAUL', 'EAST ALLEN'],
         'publn_no_rep': 'WO2007073422A1', 'legal_statuses': ['expired', 'expired_fee_related']},
        {'applicants_ifi': ['KONICA MINOLTA BUSINESS', 'IBM', 'IBM', None], 'publn_no_rep': 'US20070143628A1',
         'legal_statuses': ['expired', 'unknown']},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A2', 'legal_statuses': []},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A3', 'legal_statuses': []},
        {'applicants_ifi': None, 'publn_no_rep': None, 'legal_statuses': []},
    ]

    DATA_PUBLICATION = [
        {'applicants_ifi': ['SILICON GRAPHICS'], 'publn_no_rep': 'US6545685B1',
         'legal_status': 'invalid'},
        {'applicants_ifi': ['KONINKL KPN'], 'publn_no_rep': 'EP1296252A1', 'legal_status': 'active'},
        {'applicants_ifi': ['CHECK POINT SOFTWARE'], 'publn_no_rep': 'US5835726A', 'legal_status': 'expired'},
        {'applicants_ifi': ['IBM'], 'publn_no_rep': 'US20050114686A1',
         'legal_status': 'expired'},
        {'publn_no_rep': 'CN103037064A', 'legal_status': 'active',
         'applicants_ifi': ['INVENTEC APPLIANCES SHANGHAI', 'INVENTEC APPLIANCES NANCHANG', 'INVENTEC APPLIANCES',
                            'YINGHUADA SHANGHAI ELECTRONIC']
         },
        {'applicants_ifi': ['HUANG CHUN-HSIANG', 'LU TAI-LING', 'WANG CHIH-KUANG', 'HTC'],
         'publn_no_rep': 'US20120291121A1', 'legal_status': 'expired'},
        {'applicants_ifi': ['CHATTERJEE MANJIRNATH', 'LIU ERIC', 'WOLF NATHANIEL', 'PALM'],
         'publn_no_rep': 'WO2010014845A2', 'legal_status': 'active'},
        {'applicants_ifi': ['PALM'], 'publn_no_rep': 'US7124300B1', 'legal_status': 'invalid'},
        {'applicants_ifi': ['ARCHBOLD DAVID', 'WESTBROOKOTT D', 'FRISBIE JAMES V', 'WEST'],
         'publn_no_rep': 'US9305042B1', 'legal_status': 'expired'},
        {'applicants_ifi': ['LEE JONG-IL', 'YI NAM-SU', 'FASOO'], 'publn_no_rep': 'WO2012148080A2',
         'legal_status': 'unknown'},
        {'applicants_ifi': ['SILICON GRAPHICS', 'FORAN JAMES L'], 'publn_no_rep': 'WO2002007092A2',
         'legal_status': 'expired'},
        {'applicants_ifi': ['HTC'], 'publn_no_rep': 'EP2725473A1', 'legal_status': 'expired'},
        {'applicants_ifi': ['PALMSOURCE'], 'publn_no_rep': 'US7346778B1', 'legal_status': 'unknown'},
        {'applicants_ifi': ['LENOVO SINGAPORE PTE'], 'publn_no_rep': 'JP2017138846A', 'legal_status': 'active'},
        {'applicants_ifi': ['ZONE LABS', 'IBM'], 'publn_no_rep': 'US5987611A',
         'legal_status': 'expired'},
        {'applicants_ifi': ['NINGBO PULUODI COMPUTERIZED FLAT KNITTING MACHINE'], 'publn_no_rep': 'CN102071528A',
         'legal_status': 'active'},
        {'applicants_ifi': ['FUJITSU', 'YAMANE YASUSHI', 'MIYAZAKI KATSUYUKI'], 'publn_no_rep': 'WO2007080629A1',
         'legal_status': 'expired'},
        {'applicants_ifi': ['DIGITAL EQUIP'], 'publn_no_rep': 'US5235642A', 'legal_status': 'expired'},
        {'applicants_ifi': ['IBM', 'IBM'], 'publn_no_rep': 'US20070016958A1', 'legal_status': 'expired'},
        {'applicants_ifi': ['KONINKL PHILIPS ELECTRONICS', 'TOWNSEND STEPHEN', 'KONINKLIJKE PHILIPS'],
         'publn_no_rep': 'GB0206552D0', 'legal_status': 'expired'},
        {'applicants_ifi': ['KONICA MINOLTA BUSINESS', 'IBM'], 'publn_no_rep': 'JP2007226293A',
         'legal_status': 'active'},
        {'applicants_ifi': ['DIXON CHRISTOPHER J', 'PINCKNEY THOMAS', 'MCAFEE'],
         'publn_no_rep': 'US20060253578A1', 'legal_status': 'expired'},
        {'applicants_ifi': ['SONY ERICSSON MOBILE COMM AB', 'CARPENTER PAUL', 'EAST ALLEN'],
         'publn_no_rep': 'WO2007073422A1', 'legal_status': 'expired'},
        {'applicants_ifi': ['KONICA MINOLTA BUSINESS', 'IBM', 'IBM', None], 'publn_no_rep': 'US20070143628A1',
         'legal_status': 'expired'},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A2', 'legal_status': None},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A3', 'legal_status': None},
        {'applicants_ifi': None, 'publn_no_rep': None, 'legal_status': None},
    ]

    def test_simple_chart_empty_data(self):
        results = applicant_legal_status_distribution([], {})
        assert len(results['datasource']) == 5
        assert len(results['datasource']['categories']) == 0

    def test_simple_chart(self):
        results = applicant_legal_status_distribution(self.DATA, {})
        assert len(results['datasource']) == 5
        assert len(results['datasource']['categories']) == 15
        assert results['datasource']['categories'][2] == 'HTC'
        assert results['datasource']['invalid'][0] == 4
        assert results['datasource']['unknown'][0] == 1

    def test_publication_search(self):
        results = applicant_legal_status_distribution(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 5
        assert len(results['datasource']['categories']) == 15
        assert results['datasource']['categories'][2] == 'HTC'
        assert results['datasource']['invalid'][0] == 4
        assert results['datasource']['unknown'][0] == 0
