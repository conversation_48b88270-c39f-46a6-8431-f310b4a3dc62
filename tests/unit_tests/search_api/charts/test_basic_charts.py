
from app.services.search.helpers.charts.basic_charts import patents_by_authorities, top_technological_fields, \
    top_applicants, technology_timeline, technology_fields_over_time, similarity_density_curve, \
    patents_by_authorities_timeline, top_applicants_in_authority, technology_stack_timeline, top_owners


class TestPatentsByAuthoritiesChart:
    DATA = [
        {},
        {"authorities": []},
        {"authorities": ["EP"]},
        {"authorities": ["EP", "DE"]},
        {"authorities": ["EP", "DE", "FR"]},
        {"authorities": ["EP", "DE", "FR", "NL"]},
        {"authorities": ["EP", "DE", "FR", "NL", ""]},
        {"authorities": ["EP", "DE", "FR", "NL", None]},
        {"authorities": ["EP", "EP", "BE", "IT"]},
        {"authorities": ["PL", "AT"]},
    ]

    DATA_PUBLICATION = [
        {},
        {"publication_number": None},
        {"publication_number": "EP-123456-A1"},
        {"publication_number": "FR-123456-A1"},
        {"publication_number": "NL-123456-A1"},
        {"publication_number": "IT-123456-A1"},
        {"publication_number": "AT-123456-A1"},
        {"publication_number": "EP-201001-A1"},
    ]

    def test_simple_chart(self):
        results = patents_by_authorities(self.DATA, {})
        assert len(results['datasource']) == 8
        assert results['datasource'][0] == {'name': 'EP', 'y': 7}

    def test_simple_chart_without_documents(self):
        results = patents_by_authorities([], {})
        assert len(results['datasource']) == 0

    def test_with_parameters(self):
        results = patents_by_authorities(self.DATA, {'patents_by_authorities_quantity': 3})
        assert len(results['datasource']) == 4
        assert results['datasource'][1] == {'name': 'DE', 'y': 5}
        assert results['datasource'][-1] == {'name': 'Others', 'y': 7}

    def test_publication_search(self):
        results = patents_by_authorities(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 5
        assert results['datasource'][0] == {'name': 'EP', 'y': 2}


class TestTopTechnologicalFieldsChart:
    DATA = [
        {},
        {'tech_fields': ['Textile and Paper Machines']},
        {'tech_fields': ['Digital Communication', 'Medical Technology', 'Computer Technology', 'Control',
                         'Furniture, Games']},
        {'tech_fields': ['Chemical Engineering']},
        {'tech_fields': ['IT Methods for Management', 'Control']},
        {'tech_fields': ['Machine Tools']},
        {'tech_fields': ['Computer Technology']},
        {'tech_fields': ['Electrical Machinery, Apparatus, Energy', 'Basic Communication Processes']},
        {'tech_fields': ['Micro-Structural and Nano-Technology']},
        {'tech_fields': ['Audio-Visual Technology', 'Digital Communication', 'Telecommunications',
                         'Computer Technology']},
        {'tech_fields': ['Thermal Processes and Apparatus']},
        {'tech_fields': ['Computer Technology']}
    ]

    def test_simple_chart(self):
        results = top_technological_fields(self.DATA, {})
        assert 'tech_areas' in results['datasource']
        assert 'tech_fields' in results['datasource']
        assert len(results['datasource']['tech_areas']) == 5
        assert len(results['datasource']['tech_fields']) == 15
        assert results['datasource']['tech_areas'][0] == {'name': 'Electrical Engineering', 'y': 11, 'precise_y': 6}
        assert results['datasource']['tech_fields'][0] == {'area_name': 'Electrical Engineering',
                                                           'name': 'Computer Technology', 'y': 4}

    def test_simple_chart_without_documents(self):
        results = top_technological_fields([], {})
        assert 'tech_areas' in results['datasource']
        assert 'tech_fields' in results['datasource']
        assert len(results['datasource']['tech_areas']) == 0
        assert len(results['datasource']['tech_fields']) == 0

    def test_should_sort_tech_areas_by_index(self):
        results = top_technological_fields(self.DATA, {'top_technological_fields_sort_tech_areas_by': 'counts'})
        assert len(results['datasource']['tech_areas']) == 5
        assert results['datasource']['tech_areas'][0]['name'] == "Electrical Engineering"
        assert results['datasource']['tech_areas'][4]['name'] == "Other Fields"


class TestTopApplicantsChart:
    DATA = [
        {'applicants_ifi': ['SILICON GRAPHICS'], 'publn_no_rep': 'US6545685B1'},
        {'applicants_ifi': ['KONINKL KPN'], 'publn_no_rep': 'EP1296252A1'},
        {'applicants_ifi': ['CHECK POINT SOFTWARE'], 'publn_no_rep': 'US5835726A'},
        {'applicants_ifi': ['IBM'], 'publn_no_rep': 'US20050114686A1'},
        {'applicants_ifi': ['INVENTEC APPLIANCES SHANGHAI', 'INVENTEC APPLIANCES NANCHANG', 'INVENTEC APPLIANCES',
                            'YINGHUADA SHANGHAI ELECTRONIC'], 'publn_no_rep': 'CN103037064A'},
        {'applicants_ifi': ['HUANG CHUN-HSIANG', 'LU TAI-LING', 'WANG CHIH-KUANG', 'HTC'],
         'publn_no_rep': 'US20120291121A1'},
        {'applicants_ifi': ['CHATTERJEE MANJIRNATH', 'LIU ERIC', 'WOLF NATHANIEL', 'PALM'],
         'publn_no_rep': 'WO2010014845A2'},
        {'applicants_ifi': ['PALM'], 'publn_no_rep': 'US7124300B1'},
        {'applicants_ifi': ['ARCHBOLD DAVID', 'WESTBROOKOTT D', 'FRISBIE JAMES V', 'WEST'],
         'publn_no_rep': 'US9305042B1'},
        {'applicants_ifi': ['LEE JONG-IL', 'YI NAM-SU', 'FASOO'], 'publn_no_rep': 'WO2012148080A2'},
        {'applicants_ifi': ['SILICON GRAPHICS', 'FORAN JAMES L'], 'publn_no_rep': 'WO2002007092A2'},
        {'applicants_ifi': ['HTC'], 'publn_no_rep': 'EP2725473A1'},
        {'applicants_ifi': ['PALMSOURCE'], 'publn_no_rep': 'US7346778B1'},
        {'applicants_ifi': ['LENOVO SINGAPORE PTE'], 'publn_no_rep': 'JP2017138846A'},
        {'applicants_ifi': ['ZONE LABS', 'IBM'], 'publn_no_rep': 'US5987611A'},
        {'applicants_ifi': ['NINGBO PULUODI COMPUTERIZED FLAT KNITTING MACHINE'], 'publn_no_rep': 'CN102071528A'},
        {'applicants_ifi': ['FUJITSU', 'YAMANE YASUSHI', 'MIYAZAKI KATSUYUKI'], 'publn_no_rep': 'WO2007080629A1'},
        {'applicants_ifi': ['DIGITAL EQUIP'], 'publn_no_rep': 'US5235642A'},
        {'applicants_ifi': ['IBM', 'IBM'], 'publn_no_rep': 'US20070016958A1'},
        {'applicants_ifi': ['KONINKL PHILIPS ELECTRONICS', 'TOWNSEND STEPHEN', 'KONINKLIJKE PHILIPS'],
         'publn_no_rep': 'GB0206552D0'},
        {'applicants_ifi': ['KONICA MINOLTA BUSINESS', 'IBM'], 'publn_no_rep': 'JP2007226293A'},
        {'applicants_ifi': ['DIXON CHRISTOPHER J', 'PINCKNEY THOMAS', 'MCAFEE'],
         'publn_no_rep': 'US20060253578A1'},
        {'applicants_ifi': ['SONY ERICSSON MOBILE COMM AB', 'CARPENTER PAUL', 'EAST ALLEN'],
         'publn_no_rep': 'WO2007073422A1'},
        {'applicants_ifi': ['KONICA MINOLTA BUSINESS', 'IBM', 'IBM', None], 'publn_no_rep': 'US20070143628A1'},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A2'},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A3'},
        {'applicants_ifi': None, 'publn_no_rep': None},
    ]

    DATA_PUBLICATION = [
        {'applicants_ifi': ['SILICON GRAPHICS'], 'publn_no_rep': 'US6545685B1'},
        {'applicants_ifi': ['KONINKL KPN'], 'publn_no_rep': 'EP1296252A1'},
        {'applicants_ifi': ['CHECK POINT SOFTWARE'], 'publn_no_rep': 'US5835726A'},
        {'applicants_ifi': ['IBM'], 'publn_no_rep': 'US20050114686A1'},
        {'applicants_ifi': ['INVENTEC APPLIANCES SHANGHAI', 'INVENTEC APPLIANCES NANCHANG', 'INVENTEC APPLIANCES',
                            'YINGHUADA SHANGHAI ELECTRONIC'], 'publn_no_rep': 'CN103037064A'},
        {'applicants_ifi': ['HUANG CHUN-HSIANG', 'LU TAI-LING', 'WANG CHIH-KUANG', 'HTC'],
         'publn_no_rep': 'US20120291121A1'},
        {'applicants_ifi': ['CHATTERJEE MANJIRNATH', 'LIU ERIC', 'WOLF NATHANIEL', 'PALM'],
         'publn_no_rep': 'WO2010014845A2'},
        {'applicants_ifi': ['PALM'], 'publn_no_rep': 'US7124300B1'},
        {'applicants_ifi': ['ARCHBOLD DAVID', 'WESTBROOKOTT D', 'FRISBIE JAMES V', 'WEST'],
         'publn_no_rep': 'US9305042B1'},
        {'applicants_ifi': ['KONICA MINOLTA BUSINESS', 'IBM', 'IBM', None], 'publn_no_rep': 'US20070143628A1'},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A2'},
        {'applicants_ifi': [], 'publn_no_rep': 'US20070143628A3'},
        {'applicants_ifi': None, 'publn_no_rep': None},
    ]

    def test_simple_chart(self):
        results = top_applicants(self.DATA, {})
        assert len(results['datasource']) == 7
        assert results['datasource'][0] == {'name': 'IBM', 'absolute': 5, 'y': 18.52}
        assert results['datasource'][6] == {'name': 'N/A', 'absolute': 3, 'y': 11.11}
        assert results['total'] == 27
        assert results['top'] == ['IBM', 'SILICON GRAPHICS', 'HTC', 'PALM', 'KONICA MINOLTA BUSINESS']

    def test_with_parameters(self):
        results = top_applicants(self.DATA, {'top_applicants_quantity': 3})
        assert len(results['datasource']) == 5
        assert results['datasource'][0] == {'name': 'IBM', 'absolute': 5, 'y': 18.52}
        assert results['datasource'][3] == {'name': 'Others', 'absolute': 20, 'y': 74.07}
        assert results['total'] == 27
        assert results['top'] == ['IBM', 'SILICON GRAPHICS', 'HTC']

    def test_simple_chart_without_documents(self):
        results = top_applicants([], {})
        assert len(results['datasource']) == 0
        assert results['total'] == 0
        assert results['top'] == []

    def test_publication_search(self):
        results = top_applicants(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 7
        assert results['datasource'][0] == {'name': 'IBM', 'absolute': 2, 'y': 15.38}
        assert results['datasource'][6] == {'name': 'N/A', 'absolute': 3, 'y': 23.08}
        assert results['total'] == 13
        assert results['top'] == ['IBM', 'PALM', 'SILICON GRAPHICS', 'KONINKL KPN', 'CHECK POINT SOFTWARE']


def ultimate_owner(ultimate_owner_list):

    return [{
        "id": 24852,
        "name": owner,
        "root_id": 24852
    } for owner in ultimate_owner_list]


class TestTopOwnersChart:
    DATA = [
        {'ultimate_owners': ultimate_owner(['SILICON GRAPHICS']), 'publn_no_rep': 'US6545685B1'},
        {'ultimate_owners': ultimate_owner(['KONINKL KPN']), 'publn_no_rep': 'EP1296252A1'},
        {'ultimate_owners': ultimate_owner(['CHECK POINT SOFTWARE']), 'publn_no_rep': 'US5835726A'},
        {'ultimate_owners': ultimate_owner(['IBM']), 'publn_no_rep': 'US20050114686A1'},
        {'ultimate_owners': ultimate_owner(['INVENTEC APPLIANCES SHANGHAI', 'INVENTEC APPLIANCES NANCHANG',
                                            'INVENTEC APPLIANCES', 'YINGHUADA SHANGHAI ELECTRONIC']),
            'publn_no_rep': 'CN103037064A'},
        {'ultimate_owners': ultimate_owner(['HUANG CHUN-HSIANG', 'LU TAI-LING', 'WANG CHIH-KUANG', 'HTC']),
         'publn_no_rep': 'US20120291121A1'},
        {'ultimate_owners': ultimate_owner(['CHATTERJEE MANJIRNATH', 'LIU ERIC', 'WOLF NATHANIEL', 'PALM']),
         'publn_no_rep': 'WO2010014845A2'},
        {'ultimate_owners': ultimate_owner(['PALM']), 'publn_no_rep': 'US7124300B1'},
        {'ultimate_owners': ultimate_owner(['ARCHBOLD DAVID', 'WESTBROOKOTT D', 'FRISBIE JAMES V', 'WEST']),
         'publn_no_rep': 'US9305042B1'},
        {'assignees_ifi': ['LEE JONG-IL', 'YI NAM-SU', 'FASOO'], 'publn_no_rep': 'WO2012148080A2'},
        {'assignees_ifi': ['SILICON GRAPHICS', 'FORAN JAMES L'], 'publn_no_rep': 'WO2002007092A2'},
        {'assignees_ifi': ['HTC'], 'publn_no_rep': 'EP2725473A1'},
        {'assignees_ifi': ['PALMSOURCE'], 'publn_no_rep': 'US7346778B1'},
        {'assignees_ifi': ['LENOVO SINGAPORE PTE'], 'publn_no_rep': 'JP2017138846A'},
        {'assignees_ifi': ['ZONE LABS', 'IBM'], 'publn_no_rep': 'US5987611A'},
        {'assignees_ifi': ['NINGBO PULUODI COMPUTERIZED FLAT KNITTING MACHINE'], 'publn_no_rep': 'CN102071528A'},
        {'assignees_ifi': ['FUJITSU', 'YAMANE YASUSHI', 'MIYAZAKI KATSUYUKI'], 'publn_no_rep': 'WO2007080629A1'},
        {'assignees_ifi': ['DIGITAL EQUIP'], 'publn_no_rep': 'US5235642A'},
        {'assignees_original': ['IBM', 'IBM'], 'publn_no_rep': 'US20070016958A1'},
        {'assignees_original': ['KONINKL PHILIPS ELECTRONICS', 'TOWNSEND STEPHEN', 'KONINKLIJKE PHILIPS'],
         'publn_no_rep': 'GB0206552D0'},
        {'assignees_original': ['KONICA MINOLTA BUSINESS', 'IBM'], 'publn_no_rep': 'JP2007226293A'},
        {'assignees_original': ['DIXON CHRISTOPHER J', 'PINCKNEY THOMAS', 'MCAFEE'],
         'publn_no_rep': 'US20060253578A1'},
        {'assignees_original': ['SONY ERICSSON MOBILE COMM AB', 'CARPENTER PAUL', 'EAST ALLEN'],
         'publn_no_rep': 'WO2007073422A1'},
        {'assignees_original': ['KONICA MINOLTA BUSINESS', 'IBM', 'IBM', None], 'publn_no_rep': 'US20070143628A1'},
        {'assignees_original': [], 'publn_no_rep': 'US20070143628A2'},
        {'assignees_original': [], 'publn_no_rep': 'US20070143628A3'},
        {'assignees_original': None, 'publn_no_rep': None},
    ]

    DATA_PUBLICATION = [
        {'ultimate_owners': ultimate_owner(['SILICON GRAPHICS']), 'publn_no_rep': 'US6545685B1'},
        {'ultimate_owners': ultimate_owner(['KONINKL KPN']), 'publn_no_rep': 'EP1296252A1'},
        {'ultimate_owners': ultimate_owner(['CHECK POINT SOFTWARE']), 'publn_no_rep': 'US5835726A'},
        {'ultimate_owners': ultimate_owner(['IBM']), 'publn_no_rep': 'US20050114686A1'},
        {'ultimate_owners': ultimate_owner(['INVENTEC APPLIANCES SHANGHAI', 'INVENTEC APPLIANCES NANCHANG',
                                            'INVENTEC APPLIANCES', 'YINGHUADA SHANGHAI ELECTRONIC']),
         'publn_no_rep': 'CN103037064A'},
        {'assignees_ifi': ['HUANG CHUN-HSIANG', 'LU TAI-LING', 'WANG CHIH-KUANG', 'HTC'],
         'publn_no_rep': 'US20120291121A1'},
        {'assignees_ifi': ['CHATTERJEE MANJIRNATH', 'LIU ERIC', 'WOLF NATHANIEL', 'PALM'],
         'publn_no_rep': 'WO2010014845A2'},
        {'assignees_ifi': ['PALM'], 'publn_no_rep': 'US7124300B1'},
        {'assignees_ifi': ['ARCHBOLD DAVID', 'WESTBROOKOTT D', 'FRISBIE JAMES V', 'WEST'],
         'publn_no_rep': 'US9305042B1'},
        {'assignees_original': ['KONICA MINOLTA BUSINESS', 'IBM', 'IBM', None], 'publn_no_rep': 'US20070143628A1'},
        {'assignees_original': [], 'publn_no_rep': 'US20070143628A2'},
        {'assignees_original': [], 'publn_no_rep': 'US20070143628A3'},
        {'assignees_original': None, 'publn_no_rep': None},
    ]

    def test_simple_chart(self):
        results = top_owners(self.DATA, {})
        assert len(results['datasource']) == 6
        assert results['datasource'][0] == {
            'name': 'IBM', 'absolute': 5, 'query': 'OWNER_IDS=(24852) OR ASSIGNEES=(IBM)'
            ' OR ASSIGNEES_ORIGINAL=(IBM)', 'y': 18.52}
        assert results['datasource'][5] == {
            'absolute': 21,
            'name': 'Others',
            'query': 'NOT(OWNER_IDS=(24852) OR ASSIGNEES=(IBM) OR '
                     'ASSIGNEES_ORIGINAL=(IBM) OR OWNER_IDS=(24852) OR ASSIGNEES=(SILICON '
                     'GRAPHICS) OR OWNER_IDS=(24852) OR ASSIGNEES=(HTC) OR '
                     'OWNER_IDS=(24852) OR ASSIGNEES_ORIGINAL=(KONICA MINOLTA BUSINESS))',
            'y': 77.78} != {'absolute': 3, 'name': 'N/A', 'y': 11.11}
        assert results['total'] == 27
        assert results['top'] == ['IBM', 'SILICON GRAPHICS', 'HTC', 'PALM', 'KONICA MINOLTA BUSINESS']

    def test_with_parameters(self):
        results = top_owners(self.DATA, {'top_owners_quantity': 3})
        print(results['datasource'])
        assert len(results['datasource']) == 4
        assert results['datasource'][0] == {
            'name': 'IBM',
            'absolute': 5,
            'query': 'OWNER_IDS=(24852) OR ASSIGNEES=(IBM) OR ASSIGNEES_ORIGINAL=(IBM)',
            'y': 18.52}
        assert results['datasource'][3] == {
            'name': 'Others',
            'absolute': 23,
            'query': 'NOT(OWNER_IDS=(24852) OR ASSIGNEES=(IBM) OR '
                     'ASSIGNEES_ORIGINAL=(IBM) OR OWNER_IDS=(24852) OR ASSIGNEES=(SILICON '
                     'GRAPHICS) OR OWNER_IDS=(24852) OR ASSIGNEES=(HTC))',
            'y': 85.19}
        assert results['total'] == 27
        assert results['top'] == ['IBM', 'SILICON GRAPHICS', 'HTC']

    def test_simple_chart_without_documents(self):
        results = top_owners([], {})
        assert len(results['datasource']) == 0
        assert results['total'] == 0
        assert results['top'] == []

    def test_publication_search(self):
        results = top_owners(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 6
        assert results['datasource'][0] == {'name': 'IBM', 'absolute': 2,
                                            'query': 'OWNER_IDS=(24852) OR ASSIGNEES_ORIGINAL=(IBM)', 'y': 15.38}
        assert results['datasource'][5] == {
            'absolute': 8,
            'name': 'Others',
            'query': 'NOT(OWNER_IDS=(24852) OR ASSIGNEES_ORIGINAL=(IBM) OR '
                     'ASSIGNEES=(PALM) OR OWNER_IDS=(24852) OR OWNER_IDS=(24852) OR '
                    'OWNER_IDS=(24852))',
            'y': 61.54}
        assert results['total'] == 13
        assert results['top'] == ['IBM', 'PALM', 'SILICON GRAPHICS', 'KONINKL KPN', 'CHECK POINT SOFTWARE']


class TestTechnologyTimelineChart:
    DATA = [
        {'priority_date': '2003-10-31', 'similarity_index': 107},
        {'priority_date': '2011-09-29', 'similarity_index': 106},
        {'priority_date': '2005-09-16', 'similarity_index': 105},
        {'priority_date': '2000-12-01', 'similarity_index': 104},
        {'priority_date': '2009-05-03', 'similarity_index': 103},
        {'priority_date': '2002-06-25', 'similarity_index': 103},
        {'priority_date': '1998-04-24', 'similarity_index': 103},
        {'priority_date': '2013-07-30', 'similarity_index': 102},
        {'priority_date': '2017-09-09', 'similarity_index': 102},
        {'priority_date': '2014-05-29', 'similarity_index': 102},
        {'priority_date': '2009-02-19', 'similarity_index': 101},
        {'priority_date': '2001-01-10', 'similarity_index': 99},
        {'priority_date': '2004-09-22', 'similarity_index': 99},
        {'priority_date': '2012-10-03', 'similarity_index': 99},
        {'priority_date': '2013-12-10', 'similarity_index': 99},
        {'priority_date': '2010-06-29', 'similarity_index': 98},
        {'priority_date': '2012-12-27', 'similarity_index': 98},
        {'priority_date': '2011-12-23', 'similarity_index': 97},
        {'priority_date': '2000-05-09', 'similarity_index': 96},
        {'priority_date': '2014-07-31', 'similarity_index': 95},
        {'priority_date': '2015-07-15', 'similarity_index': 94},
        {'priority_date': '2013-08-12', 'similarity_index': 94},
        {'priority_date': '2008-02-15', 'similarity_index': 93},
        {'priority_date': '2005-04-04', 'similarity_index': 92},
        {'priority_date': '2007-07-11', 'similarity_index': 92},
        {'priority_date': '1999-11-16', 'similarity_index': 90},
        {'priority_date': '2000-03-02', 'similarity_index': 86},
        {'priority_date': '2001-07-24', 'similarity_index': 85},
        {'priority_date': '2012-11-22', 'similarity_index': 85},
        {'priority_date': '2011-10-25', 'similarity_index': 84},
        {'priority_date': '2003-10-30', 'similarity_index': 84},
        {'priority_date': '2012-12-29', 'similarity_index': 84},
        {'priority_date': '2012-08-24', 'similarity_index': 83},
        {'priority_date': '2007-06-22', 'similarity_index': 83},
        {'priority_date': '2004-03-16', 'similarity_index': 83},
        {'priority_date': None, 'similarity_index': 83},
        {'priority_date': '2004-03-16', 'similarity_index': None},
        {'priority_date': None, 'similarity_index': None},
    ]

    DATA_PUBLICATION = [
        {'publn_date': '2003-10-31'},
        {'publn_date': '2011-09-29'},
        {'publn_date': '2005-09-16'},
        {'publn_date': '2000-12-01'},
        {'publn_date': '2009-05-03'},
        {'publn_date': '2002-06-25'},
        {'publn_date': '1998-04-24'},
        {'publn_date': '2004-03-16'},
        {'publn_date': '2013-07-30'},
        {'publn_date': None},
        {'publn_date': '2004-03-16'},
        {'publn_date': None},
    ]

    def test_simple_chart(self):
        results = technology_timeline(self.DATA, {})
        assert results['datasource']['xAxis'] == [1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2007, 2008, 2009,
                                                  2010, 2011, 2012, 2013, 2014, 2015, 2017]
        assert results['datasource']['yAxis'] == [1, 1, 3, 2, 1, 2, 3, 2, 2, 1, 2, 1, 3, 5, 3, 2, 1, 1]
        assert results['datasource']['yAxisAvg'] == [103, 90, 95, 92, 103, 96, 61, 98, 88, 93, 102, 98, 96, 90, 98, 98,
                                                     94, 102]
        assert results['datasource']['trendline'] is None

    def test_chart_with_trendline(self):
        results = technology_timeline(self.DATA, {'technology_timeline_trendline': True})
        assert results['datasource']['yAxisPercent'] == [
            2.63, 2.63, 7.89, 5.26, 2.63, 5.26, 7.89, 5.26, 5.26, 2.63, 5.26, 2.63, 7.89, 13.16, 7.89, 5.26, 2.63, 2.63
        ]
        assert results['datasource']['trendline_percent']

    def test_simple_chart_without_documents(self):
        results = technology_timeline([], {'technology_timeline_trendline': True})
        assert results['datasource']['xAxis'] == []
        assert results['datasource']['yAxis'] == []
        assert results['datasource']['yAxisAvg'] == []
        assert results['datasource']['yAxisPercent'] == []
        assert results['datasource']['trendline'] is None
        assert results['datasource']['trendline_percent'] is None

    def test_publication_search(self):
        results = technology_timeline(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert results['datasource']['xAxis'] == [1998, 2000, 2002, 2003, 2004, 2005, 2009, 2011, 2013]
        assert results['datasource']['yAxis'] == [1, 1, 1, 1, 2, 1, 1, 1, 1]
        assert results['datasource']['yAxisAvg'] == [0, 0, 0, 0, 0, 0, 0, 0, 0]

    def test_should_restrict_on_years(self):
        params = {'technology_timeline_min_year': 2010, 'technology_timeline_max_year': 2015}
        results = technology_timeline(self.DATA, params)
        assert results['datasource']['xAxis'] == [2010, 2011, 2012, 2013, 2014, 2015]
        assert len(results['datasource']['yAxis']) == 6

    def test_should_fill_missing_years(self):
        params = {'technology_timeline_fill_missing_years': True}
        data = [
            {'priority_date': '2001-10-31', 'similarity_index': 107},
            {'priority_date': '2005-09-16', 'similarity_index': 105},
        ]
        results = technology_timeline(data, params)
        assert results['datasource']['xAxis'] == [2001, 2002, 2003, 2004, 2005]
        assert results['datasource']['yAxis'] == [1, 0, 0, 0, 1]


class TestTechnologyFieldsOverTimeChart:
    DATA = [
        {'priority_date': '1997-07-21', 'tech_fields': ['Digital Communication', 'Computer Technology']},
        {'priority_date': '1997-12-04', 'tech_fields': ['Computer Technology', 'Measurement', 'Control']},
        {'priority_date': '1998-12-22', 'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']},
        {'priority_date': '1955-11-16', 'tech_fields': ['Transport']},
        {'priority_date': '1983-02-04', 'tech_fields': ['Macromolecular Chemistry, Polymers', 'Pharmaceuticals']},
        {'priority_date': '1980-08-07', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'priority_date': '1978-07-13', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'priority_date': '2001-08-15', 'tech_fields': ['IT Methods for Management', 'Computer Technology']},
        {'priority_date': '1972-04-15', 'tech_fields': ['Textile and Paper Machines']},
        {'priority_date': '1990-05-11', 'tech_fields': ['Optics']},
        {'priority_date': None, 'tech_fields': ['Basic Materials Chemistry', 'Macromolecular Chemistry, Polymers',
                                                'Organic Fine Chemistry']},
        {'priority_date': '2004-03-16', 'similarity_index': None},
        {'priority_date': None, 'similarity_index': None},
    ]

    DATA_PUBLICATION = [
        {'publn_date': '1997-07-21', 'tech_fields': ['Digital Communication', 'Computer Technology']},
        {'publn_date': '1997-12-04', 'tech_fields': ['Computer Technology', 'Measurement', 'Control']},
        {'publn_date': '1998-12-22', 'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']},
        {'publn_date': '1955-11-16', 'tech_fields': ['Transport']},
        {'publn_date': '1983-02-04', 'tech_fields': ['Macromolecular Chemistry, Polymers', 'Pharmaceuticals']},
        {'publn_date': '1980-08-07', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'publn_date': '1978-07-13', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'publn_date': '2001-08-15', 'tech_fields': ['IT Methods for Management', 'Computer Technology']},
        {'publn_date': '1972-04-15', 'tech_fields': ['Textile and Paper Machines']},
        {'publn_date': '1990-05-11', 'tech_fields': ['Optics']},
        {'publn_date': None, 'tech_fields': ['Basic Materials Chemistry', 'Macromolecular Chemistry, Polymers',
                                             'Organic Fine Chemistry']},
        {'publn_date': '2004-03-16', 'similarity_index': None},
        {'publn_date': None, 'similarity_index': None},
    ]

    def test_simple_chart(self):
        results = technology_fields_over_time(self.DATA, {})
        assert len(results['datasource']) == 3
        assert results['total'] == len(self.DATA)
        assert results['datasource']['xAxis'] == ['Comp. Tech.', 'Macromolecular. Chem.', 'Digital Communic.',
                                                  'Control', 'Measurement', 'Transport', 'Pharmaceut.',
                                                  'IT Methods Managem.', 'Textile, Paper Mach.', 'Optics']
        assert results['datasource']['yAxis'] == [1955, 1972, 1978, 1980, 1983, 1990, 1997, 1998, 2001]
        assert results['datasource']['values'] == [(2, 6, 1), (2, 7, 1), (0, 6, 2), (0, 7, 1), (0, 8, 1), (4, 6, 1),
                                                   (3, 6, 1), (3, 7, 1), (5, 0, 1), (1, 4, 1), (1, 3, 1), (1, 2, 1),
                                                   (6, 4, 1), (7, 8, 1), (8, 1, 1), (9, 5, 1)]

    def test_with_parameters(self):
        results = technology_fields_over_time(self.DATA, {'technology_fields_over_time_quantity': 2})
        assert len(results['datasource']) == 3
        assert results['total'] == len(self.DATA)
        assert results['datasource']['xAxis'] == ['Comp. Tech.', 'Macromolecular. Chem.']
        assert results['datasource']['yAxis'] == [1955, 1972, 1978, 1980, 1983, 1990, 1997, 1998, 2001]
        assert results['datasource']['values'] == [(0, 6, 2), (0, 7, 1), (0, 8, 1), (1, 4, 1), (1, 3, 1), (1, 2, 1)]

    def test_simple_chart_without_documents(self):
        results = technology_fields_over_time([], {})
        assert len(results['datasource']) == 3
        assert results['datasource']['yAxis'] == []
        assert results['datasource']['yAxis'] == []
        assert results['datasource']['values'] == []

    def test_publication_search(self):
        results = technology_fields_over_time(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']) == 3
        assert results['total'] == len(self.DATA_PUBLICATION)
        assert results['datasource']['xAxis'] == ['Comp. Tech.', 'Macromolecular. Chem.', 'Digital Communic.',
                                                  'Control', 'Measurement', 'Transport', 'Pharmaceut.',
                                                  'IT Methods Managem.', 'Textile, Paper Mach.', 'Optics']
        assert results['datasource']['yAxis'] == [1955, 1972, 1978, 1980, 1983, 1990, 1997, 1998, 2001]
        assert results['datasource']['values'] == [(2, 6, 1), (2, 7, 1), (0, 6, 2), (0, 7, 1), (0, 8, 1), (4, 6, 1),
                                                   (3, 6, 1), (3, 7, 1), (5, 0, 1), (1, 4, 1), (1, 3, 1), (1, 2, 1),
                                                   (6, 4, 1), (7, 8, 1), (8, 1, 1), (9, 5, 1)]


class TestSimilarityDensityCurveChart:
    DATA = [
        {'publn_no_rep': 'US20110251892A1', 'similarity_index': 155},
        {'publn_no_rep': 'US20050250538A1', 'similarity_index': 149},
        {'publn_no_rep': 'US20060167819A1', 'similarity_index': 149},
        {'publn_no_rep': 'US20080041936A1', 'similarity_index': 148},
        {'publn_no_rep': 'US20090307140A1', 'similarity_index': 147},
        {'publn_no_rep': 'US20040104268A1', 'similarity_index': 145},
        {'publn_no_rep': 'US20020004772A1', 'similarity_index': 141},
        {'publn_no_rep': 'EP1320014A2', 'similarity_index': 139},
        {'publn_no_rep': 'US20030110131A1', 'similarity_index': 138},
        {'publn_no_rep': 'FI980427A0', 'similarity_index': 137},
        {'publn_no_rep': 'WO2003083793A2', 'similarity_index': 136},
        {'publn_no_rep': 'US20050154879A1', 'similarity_index': 136},
        {'publn_no_rep': 'PE97234A2', 'similarity_index': 0},
        {'publn_no_rep': 'PE20099237819A1', 'similarity_index': None},
        {'publn_no_rep': None, 'similarity_index': 83},
        {'publn_no_rep': None, 'similarity_index': None},
    ]

    def test_simple_chart(self):
        results = similarity_density_curve(self.DATA, {})
        assert len(results['datasource']) == 13
        assert results['datasource'][0] == {'name': 'US20110251892A1', 'y': 155, 'order': 1}
        assert results['datasource'][-1] == {'name': 'PE97234A2', 'y': 0, 'order': 13}

    def test_simple_chart_without_documents(self):
        results = similarity_density_curve([], {})
        assert len(results['datasource']) == 0


class TestPatentsByAuthoritiesTimelineChart:
    DATA = [
        {},
        {"authorities": [], 'priority_date': ''},
        {"authorities": ["EP"], 'priority_date': None},
        {"authorities": ["EP"], 'priority_date': '2020-11-05'},
        {"authorities": ["EP", "DE"], 'priority_date': '2020-10-31'},
        {"authorities": ["EP", "DE"], 'priority_date': '2019-10-31'},
        {"authorities": ["EP", "ES"], 'priority_date': '2018-10-31'},
        {"authorities": ["EP", "DE", "FR"], 'priority_date': '2019-10-31'},
        {"authorities": ["EP", "DE", "FR", "NL"], 'priority_date': '2018-10-31'},
        {"authorities": ["EP", "DE", "FR", "NL", ""], 'priority_date': '2017-10-31'},
        {"authorities": ["EP", "DE", "FR", "NL", None], 'priority_date': '2016-10-31'},
        {"authorities": ["EP", "EP", "BE", "IT", "US"], 'priority_date': '2007-10-31'},
        {"authorities": ["EP", "WO", "GB", "CN"], 'priority_date': '2007-10-31'},
        {"authorities": ["PL", "AT"], 'priority_date': '2003-10-31'},
    ]

    DATA_PUBLICATION = [
        {},
        {"publication_number": "", 'publn_date': ''},
        {"publication_number": "US-20050250538-A1", 'publn_date': None},
        {"publication_number": "US-20110251892-A1", 'publn_date': '2020-11-05'},
        {"publication_number": "EP-1320014-A2", 'publn_date': '2020-10-31'},
        {"publication_number": "FI-980427-A0", 'publn_date': '2019-10-31'},
        {"publication_number": "WO-2003083793-A2", 'publn_date': '2018-10-31'},
        {"publication_number": "PE-97234-A2", 'publn_date': '2019-10-31'},
        {"publication_number": "PE-20099237819-A1", 'publn_date': '2018-10-31'},
        {"publication_number": "US-20030110131-A1", 'publn_date': '2017-10-31'},
    ]

    def test_simple_chart(self):
        results = patents_by_authorities_timeline(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 16
        assert results['datasource']['xAxis'][0] == 2006
        assert results['datasource']['xAxis'][-2] == 2020
        assert len(results['datasource']['yAxis']) == 11
        assert results['datasource']['yAxis'][0] == 'BE'
        assert len(results['datasource']['values']) == 176
        assert len(results['datasource']['values'][0]) == 3
        assert results['datasource']['values'][-1] == [15, 10, 29]

    def test_simple_chart_without_documents(self):
        results = patents_by_authorities_timeline([], {})
        assert not results['datasource']['xAxis']
        assert not results['datasource']['yAxis']
        assert not results['datasource']['values']

    def test_publication_search(self):
        results = patents_by_authorities_timeline(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']['xAxis']) == 16
        assert results['datasource']['xAxis'][0] == 2006
        assert results['datasource']['xAxis'][-2] == 2020
        assert len(results['datasource']['yAxis']) == 6
        assert results['datasource']['yAxis'][0] == 'EP'
        assert len(results['datasource']['values']) == 96
        assert len(results['datasource']['values'][0]) == 3
        assert results['datasource']['values'][-1] == [15, 5, 7]


class TestTopApplicantsInAuthorityChart:
    DATA = [
        {},
        {"authorities": [], 'applicants_ifi': []},
        {"authorities": ["EP"], 'applicants_ifi': ['SILTRONIC', 'UNIV ULM']},
        {"authorities": ["EP"], 'applicants_ifi': ['POST OFFICE', 'PHILIPS']},
        {"authorities": ["EP", "DE"], 'applicants_ifi': ['PHILCO FORD']},
        {"authorities": ["EP", "DE"], 'applicants_ifi': ['PHILIPS']},
        {"authorities": ["EP", "ES"], 'applicants_ifi': ['ROCKWELL']},
        {"authorities": ["EP", "DE", "FR"], 'applicants_ifi': ['LABO ELECTRONIQUE PHYSIQUE']},
        {"authorities": ["EP", "DE", "FR", "NL"], 'applicants_ifi': ['SIEMENS', 'PHILIPS']},
        {"authorities": ["EP", "DE", "FR", "NL", ""], 'applicants_ifi': ['PHILIPS']},
        {"authorities": ["EP", "DE", "FR", "NL", None], 'applicants_ifi': ['STC', 'NORTHERN TELECOM EURO']},
        {"authorities": ["EP", "EP", "BE", "IT", "US"], 'applicants_ifi': ['MASSACHUSETTS INST']},
        {"authorities": ["EP", "WO", "GB", "CN"], 'applicants_ifi': ['TOKYO SHIBAURA ELECTRIC']},
        {"authorities": ["PL", "AT"], 'applicants_ifi': ['SIEMENS']}
    ]

    DATA_PUBLICATION = [
        {},
        {"publication_number": "", 'applicants_ifi': []},
        {"publication_number": "US-20050250538-A1", 'applicants_ifi': ['SILTRONIC', 'UNIV ULM']},
        {"publication_number": "US-20110251892-A1", 'applicants_ifi': ['POST OFFICE', 'PHILIPS']},
        {"publication_number": "EP-1320014-A2", 'applicants_ifi': ['PHILCO FORD']},
        {"publication_number": "FI-980427-A0", 'applicants_ifi': ['PHILIPS']},
        {"publication_number": "WO-2003083793-A2", 'applicants_ifi': ['ROCKWELL']},
        {"publication_number": "PE-97234-A2", 'applicants_ifi': ['LABO ELECTRONIQUE PHYSIQUE']},
        {"publication_number": "PE-20099237819-A1", 'applicants_ifi': ['SIEMENS', 'PHILIPS']},
        {"publication_number": "US-20030110131-A1", 'applicants_ifi': ['PHILIPS']},
    ]

    def _find_value(self, values, x, y):
        for v in values:
            if v[0] == x and v[1] == y:
                return v[2]
        return 0

    def _print_table(self, sorted_values, x_axis_totals, y_axis_totals, expected_x_axis, expected_y_axis):
        print('name\t' + "\t".join(expected_x_axis) + '\ttotal')
        for y in range(0, len(expected_y_axis)):
            print(expected_y_axis[y][0:4] + "\t" + "\t".join([str(self._find_value(sorted_values, x, y))
                                                              for x in range(0, len(expected_x_axis))])
                  + "\t" + str(y_axis_totals[y]))

        print('total\t' + "\t".join([str(x) for x in x_axis_totals]))

    def _test_values(self, datasource, expected_values, expected_x_axis, expected_y_axis):
        print(datasource['xAxis'])
        print(datasource['yAxis'])
        x_axis_len = len(expected_x_axis)
        y_axis_len = len(expected_y_axis)
        values = datasource['values']
        print(values)

        assert sorted(datasource['xAxis']) == sorted(expected_x_axis)
        assert sorted(datasource['yAxis']) == sorted(expected_y_axis)

        total = 0
        for y in range(0, y_axis_len):
            for x in range(0, x_axis_len):
                value = self._find_value(values, x, y)
                assert value == expected_values[y][x]
                if y != y_axis_len - 1 and x != x_axis_len - 1:
                    total += value
        assert total == self._find_value(values, x_axis_len - 1, y_axis_len - 1)

    def test_simple_chart(self):
        """
        self._print_table(results['datasource']['values'], results['datasource']['xAxisTotals'],
                          results['datasource']['yAxisTotals'], expected_x_axis, expected_y_axis)
        """
        results = top_applicants_in_authority(self.DATA, {})
        expected_x_axis = ['AT', 'BE', 'CN', 'DE', 'EP', 'ES', 'FR', 'GB', 'IT', 'NL', 'PL', 'US', 'WO', 'Total']
        expected_y_axis = ['LABO ELECTRONIQUE PHYSIQUE', 'MASSACHUSETTS INST', 'NORTHERN TELECOM EURO', 'PHILCO FORD',
                           'PHILIPS', 'POST OFFICE', 'ROCKWELL', 'SIEMENS', 'SILTRONIC', 'STC',
                           'TOKYO SHIBAURA ELECTRIC', 'UNIV ULM', 'Total']
        expected_values = [
            [0, 0, 0, 1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 3],
            [0, 1, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 4],
            [0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 0, 4],
            [0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 2],
            [0, 0, 0, 3, 4, 0, 2, 0, 0, 2, 0, 0, 0, 11],
            [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [0, 0, 0, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 2],
            [1, 0, 0, 1, 1, 0, 1, 0, 0, 1, 1, 0, 0, 6],
            [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [0, 0, 0, 1, 1, 0, 1, 0, 0, 1, 0, 0, 0, 4],
            [0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 4],
            [0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1],
            [1, 1, 1, 8, 15, 1, 6, 1, 1, 5, 1, 1, 1, 43]
        ]
        self._test_values(results['datasource'], expected_values, expected_x_axis, expected_y_axis)

    def test_simple_chart_without_documents(self):
        results = top_applicants_in_authority([], {})
        assert results['datasource']['xAxis'] == []
        assert results['datasource']['yAxis'] == []
        assert results['datasource']['xAxisTotals'] == []
        assert results['datasource']['yAxisTotals'] == []
        assert len(results['datasource']['values']) == 0

    def test_simple_chart_with_parameters(self):
        """
        self._print_table(results['datasource']['values'], results['datasource']['xAxisTotals'],
                          results['datasource']['yAxisTotals'], expected_x_axis, expected_y_axis)
        """
        results = top_applicants_in_authority(self.DATA, {'top_applicants_in_authority_quantity': 5})
        expected_x_axis = ['AT', 'BE', 'DE', 'EP', 'FR', 'IT', 'NL', 'PL', 'US', 'Total']
        expected_y_axis = ['MASSACHUSETTS INST', 'NORTHERN TELECOM EURO', 'PHILIPS', 'SIEMENS', 'STC', 'Total']
        expected_values = [
            [0, 1, 0, 1, 0, 1, 0, 0, 1, 4],
            [0, 0, 1, 1, 1, 0, 1, 0, 0, 4],
            [0, 0, 3, 4, 2, 0, 2, 0, 0, 11],
            [1, 0, 1, 1, 1, 0, 1, 1, 0, 6],
            [0, 0, 1, 1, 1, 0, 1, 0, 0, 4],
            [1, 1, 6, 8, 5, 1, 5, 1, 1, 29]
        ]
        self._test_values(results['datasource'], expected_values, expected_x_axis, expected_y_axis)

    def test_publication_search(self):
        results = top_applicants_in_authority(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        expected_x_axis = ['EP', 'FI', 'PE', 'US', 'WO', 'Total']
        expected_y_axis = ['LABO ELECTRONIQUE PHYSIQUE', 'PHILCO FORD', 'PHILIPS', 'POST OFFICE',
                           'ROCKWELL', 'SIEMENS', 'SILTRONIC', 'UNIV ULM', 'Total']
        expected_values = [
            [0, 0, 1, 0, 0, 1],
            [1, 0, 0, 0, 0, 1],
            [0, 1, 1, 2, 0, 4],
            [0, 0, 0, 1, 0, 1],
            [0, 0, 0, 0, 1, 1],
            [0, 0, 1, 0, 0, 1],
            [0, 0, 0, 1, 0, 1],
            [0, 0, 0, 1, 0, 1],
            [1, 1, 3, 5, 1, 11],
        ]
        self._test_values(results['datasource'], expected_values, expected_x_axis, expected_y_axis)


class TestTechnologyStackTimeline:
    DATA = [
        {'tech_fields': ['Digital Communication', 'Computer Technology']},
        {'priority_date': '1997-07-21', 'tech_fields': ['Digital Communication', 'Computer Technology']},
        {'priority_date': '1997-12-04', 'tech_fields': ['Computer Technology', 'Measurement', 'Control']},
        {'priority_date': '1998-12-22', 'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']},
        {'priority_date': '1995-11-16', 'tech_fields': ['Transport']},
        {'priority_date': '1993-02-04', 'tech_fields': ['Macromolecular Chemistry, Polymers', 'Pharmaceuticals']},
        {'priority_date': '1980-08-07', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'priority_date': '1998-07-13', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'priority_date': '2001-08-15', 'tech_fields': ['IT Methods for Management', 'Computer Technology',
                                                        'Digital Communication']},
        {'priority_date': '2001-04-18', 'tech_fields': ['Digital Communication']},
        {'priority_date': '1992-04-15', 'tech_fields': ['Textile and Paper Machines']},
        {'priority_date': '1996-05-11', 'tech_fields': ['Optics']},
        {'priority_date': None, 'tech_fields': ['Basic Materials Chemistry', 'Macromolecular Chemistry, Polymers',
                                                'Organic Fine Chemistry']},
        {'priority_date': '2001-03-16', 'tech_fields': None},
        {'priority_date': None, 'tech_fields': None},
    ]

    DATA_PUBLICATION = [
        {'tech_fields': ['Digital Communication', 'Computer Technology']},
        {'publn_date': '1997-07-21', 'tech_fields': ['Digital Communication', 'Computer Technology']},
        {'publn_date': '1997-12-04', 'tech_fields': ['Computer Technology', 'Measurement', 'Control']},
        {'publn_date': '1998-12-22', 'tech_fields': ['Digital Communication', 'Computer Technology', 'Control']},
        {'publn_date': '1995-11-16', 'tech_fields': ['Transport']},
        {'publn_date': '1993-02-04', 'tech_fields': ['Macromolecular Chemistry, Polymers', 'Pharmaceuticals']},
        {'publn_date': '1980-08-07', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'publn_date': '1998-07-13', 'tech_fields': ['Macromolecular Chemistry, Polymers']},
        {'publn_date': '2001-08-15', 'tech_fields': ['IT Methods for Management', 'Computer Technology',
                                                     'Digital Communication']},
        {'publn_date': '2001-04-18', 'tech_fields': ['Digital Communication']},
        {'publn_date': '1992-04-15', 'tech_fields': ['Textile and Paper Machines']},
        {'publn_date': '1996-05-11', 'tech_fields': ['Optics']},
        {'publn_date': None, 'tech_fields': ['Basic Materials Chemistry', 'Macromolecular Chemistry, Polymers',
                                             'Organic Fine Chemistry']},
        {'publn_date': '2001-03-16', 'tech_fields': None},
        {'publn_date': None, 'tech_fields': None},
    ]

    def test_simple_chart(self):
        results = technology_stack_timeline(self.DATA, {})
        assert len(results['datasource']['xAxis']) == 7
        assert len(results['datasource']['yAxis']) == 5
        assert len(results['datasource']['yAxis'][0]['data']) == 7
        assert results['datasource']['yAxis'][1]['name'] == 'Digital Communic.'
        assert results['datasource']['yAxis'][1]['data'][0] == 2

    def test_simple_chart_without_documents(self):
        results = technology_stack_timeline([], {})
        assert results['datasource']['xAxis'] == []
        assert results['datasource']['yAxis'] == []

    def test_publication_search(self):
        results = technology_stack_timeline(self.DATA_PUBLICATION, {'doc_type': 'PUBLICATION'})
        assert len(results['datasource']['xAxis']) == 7
        assert len(results['datasource']['yAxis']) == 5
        assert len(results['datasource']['yAxis'][0]['data']) == 7
        assert results['datasource']['yAxis'][1]['name'] == 'Digital Communic.'
        assert results['datasource']['yAxis'][1]['data'][0] == 2
