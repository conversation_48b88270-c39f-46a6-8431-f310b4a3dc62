# Third party imports
import pytest

# Local imports
from app.ext.boolean_search.families.family_parser import FamilyParser
from app.ext.boolean_search.boolean_search_extension import BooleanSearchExtension
from app.extensions import jwt
from app.ext.boolean_search.publications.publication_parser import PublicationParser
from app.ext.boolean_search.semantic_analyzer import Semantic<PERSON><PERSON><PERSON><PERSON>
from app.ext.elastic_search.field_mappings import FAMILIES, PUBLICATIONS
from tests.unit_tests.search_api.mocks.redis import MockRedisPipeline
from octimine_common.tests import jwt_mocks


@pytest.fixture(autouse=True)
def redis(mocker):
    mocker.patch('redis.StrictRedis.set')
    mocker.patch('redis.StrictRedis.get', return_value=None)
    mocker.patch('redis.StrictRedis.pipeline', return_value=MockRedisPipeline())
    mocker.patch('redis.StrictRedis.expire', return_value=None)


@pytest.fixture(autouse=True)
def requests_mock(app, mocker):
    mocker.patch("requests.get")


@pytest.fixture(autouse=True)
def token_in_blocklist_loader(app):
    jwt.token_in_blocklist_loader(jwt_mocks.token_in_blocklist)


@pytest.fixture
def boolean_parser_family():
    boolean_parser = FamilyParser(debug=False)
    yield boolean_parser


@pytest.fixture
def boolean_parser_publication():
    boolean_parser = PublicationParser(debug=False)
    yield boolean_parser


@pytest.fixture
def es_extension(mocker):
    corporate_entities = [
        {'id': 11, 'root_id': 1, 'ancestor_ids': [1]},
        {'id': 12, 'root_id': 1, 'ancestor_ids': [1]},
        {'id': 13, 'root_id': 1, 'ancestor_ids': [1]},
        {'id': 21, 'root_id': 2, 'ancestor_ids': [2]},
        {'id': 22, 'root_id': 2, 'ancestor_ids': [2]},
        {'id': 23, 'root_id': 2, 'ancestor_ids': [2]},
    ]

    classification_codes = [
        {'id': 1, 'classification_symbol': 'Y02T10/10', 'ancestor_ids': []},
        {'id': 1, 'classification_symbol': 'Y02T10/12', 'ancestor_ids': [1]},
        {'id': 1, 'classification_symbol': 'Y02T10/30', 'ancestor_ids': [1]},
        {'id': 1, 'classification_symbol': 'Y02T10/40', 'ancestor_ids': [1]},
    ]

    def build_search_response(hits, id_field='id'):
        return {'hits': {'hits': [{'_id': h[id_field], '_source': h} for h in hits]}}

    def get_descendant_corporate_entities(root_ids=None, entity_ids=None, source=None):
        hits = [ce for rid in (entity_ids or []) for ce in corporate_entities if rid in ce['ancestor_ids']]
        return build_search_response(hits)

    def get_entries_in(classification_symbols, *args, **kwargs):
        hits = [c for c in classification_codes if c['classification_symbol'] in classification_symbols]
        return build_search_response(hits)

    def get_entries_descendant_of(ancestor_id, *args, **kwargs):
        hits = [c for c in classification_codes if ancestor_id in c['ancestor_ids']]
        return build_search_response(hits)

    es = mocker.Mock()
    es.get_descendant_corporate_entities = get_descendant_corporate_entities
    es.get_cpc_entries_in = get_entries_in
    es.get_ipc_entries_in = get_entries_in
    es.get_cpc_entries_descendant_of = get_entries_descendant_of
    es.get_ipc_entries_descendant_of = get_entries_descendant_of
    return es


@pytest.fixture
def semantic_analyzer_family(es_extension):
    analyzer = SemanticAnalyzer(es_extension, FAMILIES)
    yield analyzer


@pytest.fixture
def semantic_analyzer_publication(es_extension):
    analyzer = SemanticAnalyzer(es_extension, PUBLICATIONS)
    yield analyzer


@pytest.fixture
def flask_boolean_parser(app, es_extension):
    flask_boolean_parser = BooleanSearchExtension()
    flask_boolean_parser.init_app(app, es_extension, None, None)
    yield flask_boolean_parser


@pytest.fixture(name="blacklisted_token")
def token_in_blocklist(mocker):
    # TODO: Use a fake redis implementation to actually test this
    mocker.patch.object(jwt, '_token_in_blocklist_callback', return_value=True)
