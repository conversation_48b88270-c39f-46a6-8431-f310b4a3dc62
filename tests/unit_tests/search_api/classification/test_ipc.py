from http import HTTPStatus

import pytest
from octimine_common.tests.data import access_token

from app.extensions import elastic_search
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import ipc_results
from tests.unit_tests.search_api.mocks.jwt import get_jwt_claims

get_jwt_claims_path = "octimine_common.jwt_utils.get_jwt_claims"


class TestIPC:

    @pytest.mark.parametrize('token, expected_status', [
        (None, HTTPStatus.UNAUTHORIZED),
        (access_token, HTTPStatus.OK)
    ])
    def test_find_ipc_entries_in_list(self, client, mocker, token, expected_status):
        mocker.patch(get_jwt_claims_path, return_value=get_jwt_claims())
        mocker.patch.object(elastic_search, 'get_ipc_entries_in', autospec=True, return_value=ipc_results)
        headers = dict(Authorization='Bearer %s' % token)
        rv = client.get("/search/classification/ipc?classification_symbol=in:A,B,C", headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            assert 'A01B' in rv.json['data']['results']

    @pytest.mark.parametrize('token, expected_status', [
        (None, HTTPStatus.UNAUTHORIZED),
        (access_token, HTTPStatus.OK)
    ])
    def test_get_ipc_entry_children(self, client, mocker, token, expected_status):
        mocker.patch(get_jwt_claims_path, return_value=get_jwt_claims())
        get_ipc_entries_children_of = mocker.patch.object(elastic_search, 'get_ipc_entries_children_of',
                                                          autospec=True, return_value=ipc_results)
        headers = dict(Authorization='Bearer %s' % token)
        rv = client.get("/search/classification/ipc/children?ipc_id=1", headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            get_ipc_entries_children_of.assert_called_with(1)

    @pytest.mark.parametrize('token, expected_status', [
        (None, HTTPStatus.UNAUTHORIZED),
        (access_token, HTTPStatus.OK)
    ])
    def test_get_ipc_entry_ancestors(self, client, mocker, token, expected_status):
        mocker.patch(get_jwt_claims_path, return_value=get_jwt_claims())
        get_ipc_entry_by_ids = mocker.patch.object(elastic_search, 'get_ipc_entry_by_ids',
                                                   autospec=True, return_value=ipc_results)
        get_ipc_entries_children_of = mocker.patch.object(elastic_search, 'get_ipc_entries_children_of',
                                                          autospec=True, return_value=ipc_results)
        headers = dict(Authorization='Bearer %s' % token)
        rv = client.get("/search/classification/ipc/ancestors?id=1&include_children=1", headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            get_ipc_entry_by_ids.assert_any_call([1])
            get_ipc_entries_children_of.assert_called_with(1)
