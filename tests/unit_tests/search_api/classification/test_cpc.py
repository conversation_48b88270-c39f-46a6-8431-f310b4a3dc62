from http import HTTPStatus

import pytest
from octimine_common.tests.data import access_token

from app.extensions import elastic_search
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import cpc_results
from tests.unit_tests.search_api.mocks.jwt import get_jwt_claims

get_jwt_claims_path = "octimine_common.jwt_utils.get_jwt_claims"


class TestCPC:

    @pytest.mark.parametrize('token, expected_status', [
        (None, HTTPStatus.UNAUTHORIZED),
        (access_token, HTTPStatus.OK)
    ])
    def test_find_cpc_entries_in_list(self, client, mocker, token, expected_status):
        mocker.patch(get_jwt_claims_path, return_value=get_jwt_claims())
        mocker.patch.object(elastic_search, 'get_cpc_entries_in', autospec=True, return_value=cpc_results)
        headers = dict(Authorization='Bearer %s' % token)
        rv = client.get("/search/classification/cpc?classification_symbol=in:A,B,C", headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            assert 'F01B17/00' in rv.json['data']['results']

    @pytest.mark.parametrize('token, expected_status', [
        (None, HTTPStatus.UNAUTHORIZED),
        (access_token, HTTPStatus.OK)
    ])
    def test_get_cpc_entry_children(self, client, mocker, token, expected_status):
        mocker.patch(get_jwt_claims_path, return_value=get_jwt_claims())
        get_cpc_entries_children_of = mocker.patch.object(elastic_search, 'get_cpc_entries_children_of',
                                                          autospec=True, return_value=cpc_results)
        headers = dict(Authorization='Bearer %s' % token)
        rv = client.get("/search/classification/cpc/children?cpc_id=1", headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            get_cpc_entries_children_of.assert_called_with(1)

    @pytest.mark.parametrize('token, expected_status', [
        (None, HTTPStatus.UNAUTHORIZED),
        (access_token, HTTPStatus.OK)
    ])
    def test_get_cpc_entry_ancestors(self, client, mocker, token, expected_status):
        mocker.patch(get_jwt_claims_path, return_value=get_jwt_claims())
        get_cpc_entry_by_ids = mocker.patch.object(elastic_search, 'get_cpc_entry_by_ids',
                                                   autospec=True, return_value=cpc_results)
        get_cpc_entries_children_of = mocker.patch.object(elastic_search, 'get_cpc_entries_children_of',
                                                          autospec=True, return_value=cpc_results)
        headers = dict(Authorization='Bearer %s' % token)
        rv = client.get("/search/classification/cpc/ancestors?id=1&include_children=1", headers=headers)
        assert_status(rv, expected_status)
        if expected_status == HTTPStatus.OK:
            get_cpc_entry_by_ids.assert_any_call([1])
            get_cpc_entries_children_of.assert_called_with(1)
