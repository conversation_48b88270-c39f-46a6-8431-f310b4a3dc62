from copy import deepcopy
import pytest
from http import HTTPStatus
from tests.unit_tests.search_api.assertions import assert_status
from tests.unit_tests.search_api.data import doc_info_results
from octimine_common.tests.data import access_token


@pytest.fixture
def patch_legal_events(mocker):
    mocker.patch('app.routes.search.legal_status.legal_status.elastic_search.get_documents_info',
                 return_value=doc_info_results)


class TestLegalStatus:

    def test_forbid_unauthorized_access(self, client):
        rv = client.get('/search/legal_status/12345')
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    @pytest.mark.usefixtures('patch_legal_events')
    def test_should_retrieve_legal_statuses_for_family_id(self, client):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/legal_status/12345', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        assert len(response_data['data']['legal_statuses']) == len(doc_info_results['hits']['hits'])


class TestLegalEvents:

    def test_forbid_unauthorized_access(self, client):
        rv = client.get('/search/legal_status/12345/events')
        assert_status(rv, HTTPStatus.UNAUTHORIZED)

    @pytest.mark.usefixtures('patch_legal_events')
    def test_should_paginate_and_sort_by_effective_date_by_default(self, client):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/legal_status/12345/events', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        assert response_data['data']['page']
        assert response_data['data']['page']['current_page'] == 1
        self.assert_sorted('effective_date', 'desc', response_data['data']['legal_events'])

    @pytest.mark.usefixtures('patch_legal_events')
    @pytest.mark.parametrize('filters, expected_total_count', [
        ({}, 3),
        ({'publication_number': 'AR-062171-A1'}, 2),
        ({'publication_number': 'AR062171A1'}, 2),
        ({'publication_number': 'AR062171A1', 'impact': 'POSITIVE'}, 1),
        ({'impact': 'POSITIVE'}, 2),
    ])
    def test_should_apply_filtering(self, client, filters, expected_total_count):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/legal_status/12345/events', query_string=filters, headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        total_count = response_data['data']['page']['total_hits']
        assert total_count == expected_total_count

    @pytest.mark.usefixtures('patch_legal_events')
    @pytest.mark.parametrize('sort_field, sort_order', [
        ('effective_date', 'asc'),
        ('publication_number', 'asc'),
        ('publication_number', 'desc'),
        ('event_date', 'desc'),
        ('event_date', 'asc'),
        ('event_code', 'asc'),
        ('event_code', 'desc'),
    ])
    def test_should_apply_sorting(self, client, sort_field, sort_order):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(f'/search/legal_status/12345/events?sort_by={sort_field}&sort_order={sort_order}',
                        headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        self.assert_sorted(sort_field, sort_order, response_data['data']['legal_events'])

    @pytest.mark.usefixtures('patch_legal_events')
    def test_should_not_paginate_when_required(self, client):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/legal_status/12345/events?load_all=true', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        assert not response_data['data']['page']
        assert len(response_data['data']['legal_events']) == 3

    @pytest.mark.usefixtures('patch_legal_events')
    @pytest.mark.parametrize('page, page_size, expected_entries', [
        (1, 1, 1),
        (2, 1, 1),
        (3, 1, 1),
        (4, 1, 0),
    ])
    def test_should_apply_pagination(self, client, page, page_size, expected_entries):
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get(f'/search/legal_status/12345/events?page={page}&page_size={page_size}', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        response_data = rv.get_json()
        page_entries = response_data['data']['legal_events']
        page_info = response_data['data']['page']
        assert page_info
        assert page_info['current_page'] == page
        assert len(page_entries) == expected_entries

    def test_should_ignore_unparseable_event_attributes(self, client, mocker):
        doc_results_with_wrong_attrs = deepcopy(doc_info_results)
        doc_results_with_wrong_attrs['hits']['hits'][0]['_source']['events'][0]['attributes'] = 'not_json_parseable'
        mocker.patch('app.routes.search.legal_status.legal_status.elastic_search.get_documents_info',
                     return_value=doc_results_with_wrong_attrs)
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/legal_status/12345/events', headers=headers)
        assert_status(rv, HTTPStatus.OK)

    @staticmethod
    def assert_sorted(field, order, data):
        current = None
        for entry in data:
            if current:
                if order == 'asc':
                    assert entry[field] >= current[field]
                elif order == 'desc':
                    assert entry[field] <= current[field]
            current = entry
