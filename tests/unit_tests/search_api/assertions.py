def assert_status(rv, code, message=None, status=None):
    res = rv.get_json()
    assert code == rv.status_code, res
    if res is not None:
        if res.get('status'):
            if not status:
                status = code
            assert status == res['status']
        if message and res.get('message'):
            assert message in res['message']


def assert_called_with_kwargs(mocked, **kwargs):
    _, called_kwargs = mocked.call_args
    for k, v in kwargs.items():
        assert called_kwargs.get(k) == v
