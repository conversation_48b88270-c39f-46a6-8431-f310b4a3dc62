import pytest
from http import HTTPStatus
from app.services.attachments.functions import get_image, get_main_image, list_family_images, list_publication_images
from app.services.attachments.requests import ImageRequest, MainImageRequest, MainImageRequestMode, \
    ListFamilyImagesRequest, ListPublicationImagesRequest
from app.services.attachments.responses import AttachmentResponse


class TestAttachmentsFunctions:

    @pytest.mark.parametrize("family_id, publication_number, path, ktmine_enabled", [
        (41263486, 'US20030079057A1', "path", False),
        (None, 'US20030079057A1', "path", False),
        (41263486, 'US20030079057A1', "ktmine/path", True),
        (None, 'US20030079057A1', "ktmine/path", True)
    ])
    def test_get_image(self, mocker, family_id, publication_number, path, ktmine_enabled):
        mocker.patch("app.services.attachments.sources.ktmine.is_enabled", return_value=ktmine_enabled)
        payload = ImageRequest(publication_number, f'{path}/{publication_number}D00000.TIF', family_id)
        ktmine_func = mocker.patch("app.services.attachments.sources.ktmine.get_image",
                                   return_value=AttachmentResponse(status_code=HTTPStatus.OK))
        ifi_claims_func = mocker.patch("app.services.attachments.sources.ifi_claims.get_image")
        get_image(payload=payload)
        if ktmine_enabled:
            ifi_claims_func.assert_not_called()
            ktmine_func.assert_called_once()
        else:
            ifi_claims_func.assert_called_once()
            ktmine_func.assert_not_called()

    @pytest.mark.parametrize("family_id, publication_number, ktmine_enabled", [
        (None, 'US20030079057A1', False),
        (41263486, None, False),
        (None, 'US20030079057A1', True),
        (41263486, None, True)
    ])
    def test_get_main_image(self, mocker, family_id, publication_number, ktmine_enabled):
        mocker.patch("app.services.attachments.sources.ktmine.is_enabled", return_value=ktmine_enabled)
        payload = MainImageRequest(family_id=family_id,
                                   publication_number=publication_number,
                                   thumbnail=False,
                                   mode=MainImageRequestMode.LIGHT)
        ktmine_func = mocker.patch("app.services.attachments.sources.ktmine.get_main_image",
                                   return_value=AttachmentResponse(status_code=HTTPStatus.OK))
        ifi_claims_func = mocker.patch("app.services.attachments.sources.ifi_claims.get_main_image")
        get_main_image(payload=payload)
        if ktmine_enabled:
            ifi_claims_func.assert_not_called()
            ktmine_func.assert_called_once()
        else:
            ifi_claims_func.assert_called_once()
            ktmine_func.assert_not_called()

    @pytest.mark.parametrize("family_id, publication_number", [
        (None, 'US20030079057A1'),
        (41263486, None)
    ])
    def test_get_main_image_with_no_ktmine_images(self, mocker, family_id, publication_number):
        mocker.patch("app.services.attachments.sources.ktmine.is_enabled", return_value=True)
        payload = MainImageRequest(family_id=family_id,
                                   publication_number=publication_number,
                                   thumbnail=False,
                                   mode=MainImageRequestMode.LIGHT)
        ktmine_func = mocker.patch("app.services.attachments.sources.ktmine.get_main_image",
                                   return_value=AttachmentResponse(status_code=HTTPStatus.NOT_FOUND))
        ifi_claims_func = mocker.patch("app.services.attachments.sources.ifi_claims.get_main_image")
        get_main_image(payload=payload)
        ifi_claims_func.assert_called_once()
        ktmine_func.assert_called_once()

    @pytest.mark.parametrize("family_id, publication_number, ktmine_enabled", [
        (None, 'US20030079057A1', False),
        (41263486, None, False),
        (None, 'US20030079057A1', True),
        (41263486, None, True)
    ])
    def test_list_family_images(self, mocker, family_id, publication_number, ktmine_enabled):
        mocker.patch("app.services.attachments.sources.ktmine.is_enabled", return_value=ktmine_enabled)
        payload = ListFamilyImagesRequest(family_id=family_id,
                                          publication_number=publication_number,
                                          include_blobs=True)
        ktmine_func = mocker.patch("app.services.attachments.sources.ktmine.list_family_images",
                                   return_value={'attachments': [{'attachments': ['img']}]})
        ifi_claims_func = mocker.patch("app.services.attachments.sources.ifi_claims.list_family_images")
        list_family_images(payload=payload)
        if ktmine_enabled:
            ifi_claims_func.assert_not_called()
            ktmine_func.assert_called_once()
        else:
            ifi_claims_func.assert_called_once()
            ktmine_func.assert_not_called()

    @pytest.mark.parametrize("family_id, publication_number", [
        (None, 'US20030079057A1'),
        (41263486, None)
    ])
    def test_list_family_images_with_no_ktmine_images(self, mocker, family_id, publication_number):
        mocker.patch("app.services.attachments.sources.ktmine.is_enabled", return_value=True)
        payload = ListFamilyImagesRequest(family_id=family_id,
                                          publication_number=publication_number,
                                          include_blobs=True)
        ktmine_func = mocker.patch("app.services.attachments.sources.ktmine.list_family_images",
                                   return_value={'attachments': []})
        ifi_claims_func = mocker.patch("app.services.attachments.sources.ifi_claims.list_family_images")
        list_family_images(payload=payload)
        ifi_claims_func.assert_called_once()
        ktmine_func.assert_called_once()

    @pytest.mark.parametrize("publication_number, ktmine_enabled", [
        ('US20030079057A1', False),
        ('US20030079057A1', True)
    ])
    def test_list_publication_images(self, mocker, publication_number, ktmine_enabled):
        mocker.patch("app.services.attachments.sources.ktmine.is_enabled", return_value=ktmine_enabled)
        payload = ListPublicationImagesRequest(publication_number=publication_number,
                                               include_blobs=True)
        ktmine_func = mocker.patch("app.services.attachments.sources.ktmine.list_publication_images",
                                   return_value={'attachments': [{'attachments': ['img']}]})
        ifi_claims_func = mocker.patch("app.services.attachments.sources.ifi_claims.list_publication_images")
        list_publication_images(payload=payload)
        if ktmine_enabled:
            ifi_claims_func.assert_not_called()
            ktmine_func.assert_called_once()
        else:
            ifi_claims_func.assert_called_once()
            ktmine_func.assert_not_called()

    @pytest.mark.parametrize("publication_number", [
        ('US20030079057A1')
    ])
    def test_list_publication_images_with_no_ktmine_images(self, mocker, publication_number):
        mocker.patch("app.services.attachments.sources.ktmine.is_enabled", return_value=True)
        payload = ListPublicationImagesRequest(publication_number=publication_number,
                                               include_blobs=True)
        ktmine_func = mocker.patch("app.services.attachments.sources.ktmine.list_publication_images",
                                   return_value={'attachments': []})
        ifi_claims_func = mocker.patch("app.services.attachments.sources.ifi_claims.list_publication_images")
        list_publication_images(payload=payload)
        ifi_claims_func.assert_called_once()
        ktmine_func.assert_called_once()
