from http import HTTPStatus
import pytest
from unittest.mock import <PERSON>Mock
from octimine_common.exceptions import BadRequestException
from app.services.attachments.sources.ktmine import get_image, get_main_image, list_publication_images
from app.services.attachments.requests import ImageRequest, MainImageRequest, MainImageRequestMode, \
    ListPublicationImagesRequest
from app.ext.file_system_cache import CacheResponse
from app.extensions import ktmine_cache
from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.data import DOC_ID_PATH, doc_info_results

_MODULE = "app.services.attachments.sources.ktmine"
_PATH = 'ktmine/path/{publication_number}_D00000.TIF'


class TestKtmineService:

    @pytest.mark.parametrize("family_id, publication_number, path", [
        (None, 'US-2328353-B', _PATH.format(publication_number='US2538353A')),
        (12345, 'US-2328353-B', _PATH.format(publication_number='US2538353A')),
        (12345, None, _PATH.format(publication_number='US2538353A')),
    ])
    def test_get_image_validates_wrong_path(self, mocker, family_id, publication_number, path):
        publication_number = publication_number or 'US-3328364-A'
        mocker.patch(f"{_MODULE}._load_publication", return_value={'publication_number': publication_number})
        mocker.patch(f"{_MODULE}._get_family_publications", return_value=[{'publication_number': publication_number}])
        payload = ImageRequest(publication_number, path, family_id)
        with pytest.raises(BadRequestException):
            get_image(payload)

    @pytest.mark.parametrize("family_id, publication_number, is_cached, expected_status", [
        (12345, 'US-2328353-B', False, HTTPStatus.OK),
        (None, 'US-2328353-B', False, HTTPStatus.OK),
        (12345, 'US-2328353-B', True, HTTPStatus.OK),
        (None, 'US-2328353-B', True, HTTPStatus.OK)
    ])
    def test_get_image(self, mocker, family_id, publication_number, is_cached, expected_status):
        if is_cached:
            mocker.patch.object(ktmine_cache, "check",
                                return_value=CacheResponse(expected_status, b"<img-binary>", "image/png"))
        mocker.patch(f"{_MODULE}._load_publication", return_value={'publication_number': publication_number})
        mocker.patch(f"{_MODULE}._get_family_publications", return_value=[{'publication_number': publication_number}])
        mocker.patch(f"{_MODULE}._get_s3_client", return_value=MagicMock())
        mocker.patch(f"{_MODULE}._to_cv2_image", return_value=MagicMock())
        mocker.patch(f"{_MODULE}._convert_image", return_value=MagicMock())
        mocker.patch(f"{_MODULE}._downscale_image", return_value=MagicMock())
        mocker.patch.object(ktmine_cache, "store")
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(doc_info_results))
        path = _PATH.format(publication_number=publication_number.replace('-', ''))
        payload = ImageRequest(publication_number, path, family_id)
        response = get_image(payload)
        assert response.status_code == expected_status
        assert response.source_ucid == publication_number

    def test_validate_get_main_image(self):
        with pytest.raises(BadRequestException):
            get_main_image(payload=MainImageRequest(None, None))

    @pytest.mark.parametrize("family_id, publication_number, is_cached, expected_status", [
        (12345, 'US2328353B', False, HTTPStatus.OK),
        (None, 'US2328353B', False, HTTPStatus.OK),
        (12345, 'US2328353B', True, HTTPStatus.OK),
        (None, 'US2328353B', True, HTTPStatus.OK)
    ])
    def test_get_main_image(self, mocker, family_id, publication_number, is_cached, expected_status):
        if is_cached:
            mocker.patch.object(ktmine_cache, "check",
                                return_value=CacheResponse(expected_status, b"<img-binary>", "image/png"))
        mocker.patch(f"{_MODULE}._select_main_image", return_value=MagicMock())
        mocker.patch(f"{_MODULE}._get_s3_client", return_value=MagicMock())
        mocker.patch(f"{_MODULE}._to_cv2_image", return_value=MagicMock())
        mocker.patch(f"{_MODULE}._convert_image", return_value=MagicMock())
        mocker.patch(f"{_MODULE}._downscale_image", return_value=MagicMock())
        mocker.patch.object(ktmine_cache, "store")
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(doc_info_results))
        mocker.patch('app.services.search.functions.elastic_search.get_documents_info',
                     return_value=doc_info_results)
        payload = MainImageRequest(family_id=family_id,
                                   publication_number=publication_number,
                                   thumbnail=False,
                                   mode=MainImageRequestMode.LIGHT)
        response = get_main_image(payload=payload)
        assert response.source_ucid.replace("-", "") == publication_number
        assert response.status_code == expected_status

    def test_validate_list_publication_images(self):
        with pytest.raises(BadRequestException):
            list_publication_images(payload=ListPublicationImagesRequest(None, True))

    @pytest.mark.parametrize("publication_number, is_cached, expected_status", [
        ('US2328353B', False, HTTPStatus.OK),
        ('US2328353B', False, HTTPStatus.OK),
        ('US2328353B', True, HTTPStatus.OK),
        ('US2328353B', True, HTTPStatus.OK)
    ])
    def test_list_publication_images(self, mocker,  publication_number, is_cached, expected_status):
        if is_cached:
            mocker.patch.object(ktmine_cache, "check",
                                return_value=CacheResponse(expected_status, b"<img-binary>", "image/png"))
        mocker.patch(f"{_MODULE}._get_s3_client", return_value=MagicMock())
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(doc_info_results))
        payload = ListPublicationImagesRequest(publication_number, True)
        response = list_publication_images(payload=payload)
        assert 'attachments' in response
