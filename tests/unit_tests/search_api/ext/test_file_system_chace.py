from http import HTTPStatus
import pytest
from app.extensions import ktmine_cache
from app.ext.file_system_cache import CacheResponse


class TestFileSystemCache:

    @pytest.mark.parametrize("read_data, file_size, expected_status", [
        ("", 0, HTTPStatus.NO_CONTENT),
        (b"<img-binary>", 5, HTTPStatus.OK),
    ])
    def test_file_system_response_from_file(self, mocker, read_data, file_size, expected_status):
        mocker.patch('os.path.getsize', return_value=file_size)
        mocker.patch("builtins.open", mocker.mock_open(read_data=read_data))
        cache_response = CacheResponse.from_file("testFile.png")
        assert cache_response.status_code == expected_status

    @pytest.mark.parametrize("is_cached, expected_response, cache_timeout", [
        (True, CacheResponse(HTTPStatus.OK, b"<img-binary>", "image/png"), 3600 * 24 * 365),
        (True, CacheResponse(HTTPStatus.NO_CONTENT, None, "image/png"), 3600 * 24),
        (False, None, None)
    ])
    def test_file_systems_cache_check(self, mocker, is_cached, expected_response, cache_timeout):
        ktmine_cache._root_folder_path = 'KTMINE'
        mocker.patch.object(ktmine_cache, "_is_cached", return_value=is_cached)
        mocker.patch.object(CacheResponse, "from_file", return_value=expected_response)
        response = ktmine_cache.check("testFile.png")
        assert response == expected_response
        if response:
            assert response.cache_timeout == cache_timeout

    @pytest.mark.parametrize("response, should_be_stored", [
        (CacheResponse(HTTPStatus.OK, b"<img-binary>", "image/png"), True),
        (CacheResponse(HTTPStatus.FORBIDDEN, None, None), False),
        (CacheResponse(HTTPStatus.BAD_REQUEST, None, None), False),
        (CacheResponse(HTTPStatus.NO_CONTENT, None, None), False),
        (CacheResponse(HTTPStatus.NOT_FOUND, None, None), False),
        (CacheResponse(HTTPStatus.TOO_MANY_REQUESTS, None, None), False),
        (CacheResponse(HTTPStatus.INTERNAL_SERVER_ERROR, None, None), False),
    ])
    def test_file_system_cache_store(self, mocker, response, should_be_stored):
        ktmine_cache._root_folder_path = 'KTMINE'
        store_into_file = mocker.patch.object(ktmine_cache, '_store_into_file')
        ktmine_cache.store("path/US20030079057A1-D00000.TIF", response)
        store_into_file.assert_called_once() if should_be_stored else store_into_file.assert_not_called()
