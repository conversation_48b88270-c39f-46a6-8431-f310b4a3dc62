import pytest
import pathlib
from app.ext.elastic_search.format.description import format_description
from app.ext.elastic_search.format.claims import format_claims


class TestElasticSearchExtension:

    @staticmethod
    def _get_file_content(filename: str):
        parent_path = pathlib.Path(__file__).parent
        with open(parent_path.joinpath(filename).resolve(), 'r') as f:
            return f.read()

    @staticmethod
    def _write_result(result, path: str):
        # Useful for opening results in a browser
        with open(path, 'w') as f:
            f.write(result)

    @pytest.mark.parametrize('description_file', [f'description{i}.xml' for i in range(1, 15)])
    def test_should_format_description(self, description_file):
        result = format_description(self._get_file_content(description_file))
        assert result

    @pytest.mark.parametrize('claims_file', [f'claims{i}.xml' for i in range(1, 9)])
    def test_should_format_claims(self, claims_file):
        result = format_claims(self._get_file_content(claims_file))
        assert result
