from app.ext.elastic_search.split_join import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>lit<PERSON>, <PERSON><PERSON>hai<PERSON>


def to_execute(chunk):
    return chunk


class TestSplitJoin:

    def test_should_handle_empty_input_gracefully(self, app):
        with app.app_context():
            assert SplitJoin(to_execute, ListSplitter([], 2), Results<PERSON>hain).execute() == []

    def test_should_handle_empty_execution_results_gracefully(self, app):
        with app.app_context():
            assert SplitJoin(lambda chunk: [], ListSplitter([1, 2, 3], 2), ResultsChain).execute() == []

    def test_should_split_join_chunk(self, app):
        with app.app_context():
            data = [i for i in range(10)]
            result = SplitJoin(to_execute, ListSplitter(data, 2), ResultsChain).execute()
            assert result == data
