<description lang="EN" load-source="patent-office"
             mxw-id="PDES226959560">  <?summary-of-invention description="Summary of Invention" end="lead"?>
    <heading id="h-0001">FIELD</heading>
    <p id="p-0002" num="0001">The present invention relates to a motor control apparatus that controls a motor that
        drives a machine apparatus that moves a subject to be moved.
    </p>
    <heading id="h-0002">BACKGROUND</heading>
    <p id="p-0003" num="0002">A machine apparatus that moves a subject to be moved is known; such machine apparatus is
        driven by a motor and the motor is controlled by a motor control apparatus. An example of the subject to be
        moved is a table. Specifically, the motor control apparatus receives, from a controller, a command signal that
        specifies a destination position of the table, receives, from a detector that detects a rotational position of
        the motor, a detection signal that indicates the rotational position, and controls the motor on the basis of the
        command signal and the detection signal, which are received.
    </p>
    <p id="p-0004" num="0003">To control the motor, the motor control apparatus calculates a value of voltage to be
        applied to the motor on the basis of the command signal and the detection signal. When calculating a value of
        the voltage, the motor control apparatus converts a value indicated by the command signal to a signal that
        indicates a rotational position of the motor, considering a gear ratio of gears included in the machine
        apparatus, a pitch of a ball screw included in the machine apparatus, and resolution of the detector. That is,
        the motor control apparatus performs unit conversion.
    </p>
    <p id="p-0005" num="0004">Patent Literature 1 discloses a method for fraction arithmetic in unit conversion, where
        the numerator and denominator both have an integer term and a power-of-two term. Patent Literature 2 discloses a
        technique for acquiring information that indicates resolution by communication. Patent Literature 3 discloses a
        technique for conversion from a position command of resolution of a controller to an internal position command
        of resolution higher than the resolution of a position detector.
    </p>
    <heading id="h-0003">CITATION LIST</heading>
    <heading id="h-0004">Patent Literature</heading>
    <p id="p-0006" num="0005">Patent Literature 1: Japanese Patent Application Laid-open No. 2002-112566</p>
    <p id="p-0007" num="0006">Patent Literature 2: Japanese Patent Application Laid-open No. 2004-317261</p>
    <p id="p-0008" num="0007">Patent Literature 3: Japanese Patent Application Laid-open No. 2012-104047</p>
    <heading id="h-0005">SUMMARY</heading>
    <heading id="h-0006">Technical Problem</heading>
    <p id="p-0009" num="0008">A detector needs to be replaced when it is broken. The resolution of detectors is
        increasing year by year. When a detector needs to be replaced, a detector having the same resolution as the
        resolution of the detector to be replaced may not be available in some cases; in such cases, the motor control
        apparatus that has been used until the replacement of the detector cannot be used any longer.
    </p>
    <p id="p-0010" num="0009">The present invention has been achieved in view of the above, and an object of the present
        invention is to provide a motor control apparatus that, after a detector for detecting a rotational position of
        a motor or a position of a subject to be moved is replaced with another detector having higher resolution than
        resolution of a replaced detector, controls the motor with precision provided by resolution of the detector used
        before a replacement.
    </p>
    <heading id="h-0007">Solution to Problem</heading>
    <p id="p-0011" num="0010">To solve the problem described above and achieve the object described above, the present
        invention provides a motor control apparatus that controls a motor that drives a machine apparatus that moves a
        subject to be moved, the motor control apparatus including a resolution conversion unit that converts, on the
        basis of a ratio of resolution of a first detector that detects a position of an object and resolution of a
        second detector that detects a position of the object, a detection signal that indicates the position of the
        object that is detected by the second detector to a signal of the resolution of the first detector. The present
        invention further includes a current control unit that controls a voltage to be applied to the motor on the
        basis of a command signal that specifies a destination position of the subject to be moved and the signal
        obtained by the conversion performed by the resolution conversion unit on the detection signal. The position of
        the object is a rotational position of the motor or a position of the subject to be moved.
    </p>
    <heading id="h-0008">Advantageous Effects of Invention</heading>
    <p id="p-0012" num="0011">The motor control apparatus according to the present invention produces an effect of
        enabling control of a motor, after a detector for detecting a rotational position of a motor or a position of a
        subject to be moved is replaced with another detector having higher resolution than resolution of a replaced
        detector, controls the motor with precision provided by resolution of the detector used before a replacement.
    </p>
    <?summary-of-invention description="Summary of Invention" end="tail"?>  <?brief-description-of-drawings description="Brief Description of Drawings" end="lead"?>
    <description-of-drawings>
        <heading id="h-0009">BRIEF DESCRIPTION OF DRAWINGS</heading>
        <p id="p-0013" num="0012">
            <figref idrefs="DRAWINGS">FIG. 1</figref>       is a diagram illustrating a configuration of a motor control
            apparatus according to a first embodiment.
        </p>
        <p id="p-0014" num="0013">
            <figref idrefs="DRAWINGS">FIG. 2</figref>       is a flowchart indicating portions of operations of a unit
            conversion unit and a resolution conversion unit that are included in the motor control apparatus according
            to the first embodiment.
        </p>
        <p id="p-0015" num="0014">
            <figref idrefs="DRAWINGS">FIG. 3</figref>       is a diagram illustrating a processor that is used when at
            least a portion of functions of the unit conversion unit, a position control unit, a speed calculation unit,
            a speed control unit, a current control unit, and the resolution conversion unit, which are included in the
            motor control apparatus according to the first embodiment, is achieved by the processor.
        </p>
        <p id="p-0016" num="0015">
            <figref idrefs="DRAWINGS">FIG. 4</figref>       is a diagram illustrating a processing circuit that is used
            when at least one of constituent elements configuring the unit conversion unit, the position control unit,
            the speed calculation unit, the speed control unit, the current control unit, and the resolution conversion
            unit, which are included in the motor control apparatus according to the first embodiment, is achieved by
            the processing circuit.
        </p>
        <p id="p-0017" num="0016">
            <figref idrefs="DRAWINGS">FIG. 5</figref>       is a diagram illustrating a configuration of a motor control
            apparatus according to a second embodiment.
        </p>
    </description-of-drawings>
    <?brief-description-of-drawings description="Brief Description of Drawings" end="tail"?>  <?detailed-description description="Detailed Description" end="lead"?>
    <heading id="h-0010">DESCRIPTION OF EMBODIMENTS</heading>
    <p id="p-0018" num="0017">Exemplary embodiments of a motor control apparatus according to the present invention are
        described in detail below with reference to the drawings. The present invention is not limited to the
        embodiments.
    </p>
    <heading id="h-0011">First Embodiment</heading>
    <p id="p-0019" num="0018">
        <figref idrefs="DRAWINGS">FIG. 1</figref>     is a diagram illustrating a configuration of a motor control
        apparatus     <b>1</b>     according to a first embodiment. The motor control apparatus     <b>1</b>     is an
        apparatus that controls a motor     <b>3</b>     that drives a machine apparatus     <b>2</b>     that moves a
        subject to be moved. The machine apparatus     <b>2</b>     and the motor     <b>3</b>     are also illustrated
        in     <figref idrefs="DRAWINGS">FIG. 1</figref>    . In the first embodiment, the subject to be moved is a
        table     <b>4</b>    . The table     <b>4</b>     is also illustrated in     <figref idrefs="DRAWINGS">FIG. 1
    </figref>    .
    </p>
    <p id="p-0020" num="0019">The machine apparatus     <b>2</b>     is described first. The machine apparatus     <b>
        2
    </b>     in the first embodiment is a table driving apparatus that converts a rotational motion of the motor     <b>
        3
    </b>     to a linear motion and drives the table     <b>4</b>    ; the machine apparatus     <b>2</b>     includes a
        first gear     <b>21</b>     and a second gear     <b>22</b>    . The first gear     <b>21</b>     and the
        second gear     <b>22</b>     configure a speed reducer. The machine apparatus     <b>2</b>     also includes a
        ball screw     <b>23</b>    . The first gear     <b>21</b>     and the second gear     <b>22</b>     mesh with
        each other and transfer a force of the rotational motion supplied by the motor     <b>3</b>     to the ball
        screw     <b>23</b>    .
    </p>
    <p id="p-0021" num="0020">The first gear     <b>21</b>     is positioned relatively close to the motor     <b>3</b>     and
        relatively far from the table     <b>4</b>    ; the second gear     <b>22</b>     is positioned relatively far
        from the motor     <b>3</b>     and relatively close to the table     <b>4</b>    . In the first embodiment, the
        number of teeth of the first gear     <b>21</b>     is defined as Gr1; the number of teeth of the second gear     <b>
            22
        </b>     is defined as Gr2.
    </p>
    <p id="p-0022" num="0021">The ball screw     <b>23</b>     is coupled to the table     <b>4</b>     and produces a
        linear motion based on a force supplied from the second gear     <b>22</b>    , moving the table     <b>4</b>     on
        a predefined straight line. In the first embodiment, a movement quantity per rotation of the ball screw     <b>
            23
        </b>     is defined as PIT (mm).
    </p>
    <p id="p-0023" num="0022">A rotational-position detector     <b>5</b>     that detects the rotational position of
        the motor     <b>3</b>     is also illustrated in     <figref idrefs="DRAWINGS">FIG. 1</figref>    . In the
        first embodiment, the rotational-position detector     <b>5</b>     is replaced with a new rotational-position
        detector     <b>5</b>    . The rotational-position detector     <b>5</b>     that is to be replaced may be
        referred to as a first rotational-position detector     <b>5</b>    , and the replacing rotational-position
        detector     <b>5</b>     may be referred to as a second rotational-position detector     <b>5</b>    .
        Resolution of the rotational-position detector     <b>5</b>     that is to be replaced is RNG. That is, the
        resolution of the first rotational-position detector     <b>5</b>     is RNG. RNG represents the number of
        pulses per rotation of the motor     <b>3</b>    . The rotational position of the motor     <b>3</b>     is
        detected by one of the rotational-position detector     <b>5</b>     that is to be replaced and the replacing
        rotational-position detector     <b>5</b>    ; thus, only one rotational-position detector     <b>5</b>     is
        illustrated in     <figref idrefs="DRAWINGS">FIG. 1</figref>    .
    </p>
    <p id="p-0024" num="0023">The first rotational-position detector     <b>5</b>    , which is the rotational-position
        detector     <b>5</b>     that is to be replaced, detects the rotational position of the motor     <b>3</b>     with
        precision of 360°/RNG. For example, when RNG is 1000, the first rotational-position detector     <b>5</b>     detects
        the rotational position of the motor     <b>3</b>     with precision of 360°/1000. A controller     <b>6</b>     that
        transmits a command signal that specifies a destination position of the table     <b>4</b>     to the motor
        control apparatus     <b>1</b>     is also illustrated in     <figref idrefs="DRAWINGS">FIG. 1</figref>    . In
        the first embodiment, a minimum unit for a value indicated by the command signal is defined as IU (mm). The
        controller     <b>6</b>     is located outside the motor control apparatus     <b>1</b>    .
    </p>
    <p id="p-0025" num="0024">A configuration of the motor control apparatus     <b>1</b>     is described next. The
        motor control apparatus     <b>1</b>     includes a unit conversion unit     <b>11</b>     that converts the
        command signal, which is transmitted by the controller     <b>6</b>     to the motor control apparatus     <b>
            1
        </b>    , to a signal that specifies the rotational position of the motor     <b>3</b>     by using an
        expression (1) described below. As described above, the command signal is a signal that specifies the
        destination position of the table     <b>4</b>    . The expression (1) is an expression that expresses a unit
        conversion factor for use in the conversion of the command signal to the signal that specifies the rotational
        position of the motor     <b>3</b>    .
    </p>
    <p id="p-0026" num="0000">
        <maths id="MATH-US-00001" num="00001">
            <math overflow="scroll">
                <mtable>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mrow>
                                    <mo>[</mo>
                                    <mrow>
                                        <mi>Formula</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.8ex" width="0.8em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mn>1</mn>
                                    </mrow>
                                    <mo>]</mo>
                                </mrow>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="34.4ex" width="34.4em"/>
                                </mstyle>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mstyle>
                                <mspace height="0.3ex" width="0.3em"/>
                            </mstyle>
                        </mtd>
                    </mtr>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mrow>
                                    <mi>Unit</mi>
                                    <mo></mo>
                                    <mstyle>
                                        <mspace height="0.8ex" width="0.8em"/>
                                    </mstyle>
                                    <mo></mo>
                                    <mi>conversion</mi>
                                    <mo></mo>
                                    <mstyle>
                                        <mspace height="0.8ex" width="0.8em"/>
                                    </mstyle>
                                    <mo></mo>
                                    <mi>factor</mi>
                                </mrow>
                                <mo>=</mo>
                                <mfrac>
                                    <mrow>
                                        <mi>IU</mi>
                                        <mo>×</mo>
                                        <mi>Gr</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.3ex" width="0.3em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mn>2</mn>
                                        <mo>×</mo>
                                        <mi>RNG</mi>
                                    </mrow>
                                    <mrow>
                                        <mi>PIT</mi>
                                        <mo>×</mo>
                                        <mi>Gr</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.3ex" width="0.3em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mn>1</mn>
                                    </mrow>
                                </mfrac>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mo>(</mo>
                                <mn>1</mn>
                                <mo>)</mo>
                            </mrow>
                        </mtd>
                    </mtr>
                </mtable>
            </math>
        </maths>
    </p>
    <p id="p-0027" num="0025">It is assumed that IU, PIT, Gr1, and Gr2 are as follows: IU (mm)=1×10    <sup>−3</sup>    (mm),
        PIT (mm)=10 (mm), Gr1=10, and Gr2=20. When the value indicated by the command signal is 10000 with IU as the
        unit, the unit conversion unit     <b>11</b>     converts the command signal to the signal that specifies the
        rotational position of the motor     <b>3</b>     on the basis of an expression (2) described below. The
        expression (2) includes the unit conversion factor indicated by the expression (1).
    </p>
    <p id="p-0028" num="0000">
        <maths id="MATH-US-00002" num="00002">
            <math overflow="scroll">
                <mtable>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mrow>
                                    <mo>[</mo>
                                    <mrow>
                                        <mi>Formula</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.8ex" width="0.8em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mn>2</mn>
                                    </mrow>
                                    <mo>]</mo>
                                </mrow>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="34.4ex" width="34.4em"/>
                                </mstyle>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mstyle>
                                <mspace height="0.3ex" width="0.3em"/>
                            </mstyle>
                        </mtd>
                    </mtr>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mrow>
                                    <mi>Rotational</mi>
                                    <mo></mo>
                                    <mstyle>
                                        <mspace height="0.8ex" width="0.8em"/>
                                    </mstyle>
                                    <mo></mo>
                                    <mi>position</mi>
                                    <mo></mo>
                                    <mstyle>
                                        <mspace height="0.8ex" width="0.8em"/>
                                    </mstyle>
                                    <mo></mo>
                                    <mi>of</mi>
                                    <mo></mo>
                                    <mstyle>
                                        <mspace height="0.8ex" width="0.8em"/>
                                    </mstyle>
                                    <mo></mo>
                                    <mi>motor</mi>
                                </mrow>
                                <mo>=</mo>
                                <mrow>
                                    <mrow>
                                        <mi>Command</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.8ex" width="0.8em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mi>signal</mi>
                                        <mo>×</mo>
                                        <mi>Unit</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.8ex" width="0.8em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mi>conversion</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.8ex" width="0.8em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mi>factor</mi>
                                    </mrow>
                                    <mo>=</mo>
                                    <mrow>
                                        <mrow>
                                            <mn>10000</mn>
                                            <mo>×</mo>
                                            <mfrac>
                                                <mrow>
                                                    <msup>
                                                        <mn>10</mn>
                                                        <mrow>
                                                            <mo>-</mo>
                                                            <mn>3</mn>
                                                        </mrow>
                                                    </msup>
                                                    <mo>×</mo>
                                                    <mn>20</mn>
                                                    <mo>×</mo>
                                                    <mi>RNG</mi>
                                                </mrow>
                                                <mrow>
                                                    <mn>10</mn>
                                                    <mo>×</mo>
                                                    <mn>10</mn>
                                                </mrow>
                                            </mfrac>
                                        </mrow>
                                        <mo>=</mo>
                                        <mrow>
                                            <mn>2</mn>
                                            <mo>×</mo>
                                            <mi>RNG</mi>
                                        </mrow>
                                    </mrow>
                                </mrow>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mo>(</mo>
                                <mn>2</mn>
                                <mo>)</mo>
                            </mrow>
                        </mtd>
                    </mtr>
                </mtable>
            </math>
        </maths>
    </p>
    <p id="p-0029" num="0026">That is, when IU, PIT, Gr1, Gr2, and the command signal are as described above, the unit
        conversion unit     <b>11</b>     converts the command signal from the controller     <b>6</b>     to a signal
        of “2×RNG” that specifies that the motor     <b>3</b>     makes two rotations.
    </p>
    <p id="p-0030" num="0027">Before transmitting the command signal to the motor control apparatus     <b>1</b>    ,
        the controller     <b>6</b>     transmits data that indicates IU, PIT, Gr1, and Gr2 to the motor control
        apparatus     <b>1</b>    . The unit conversion unit     <b>11</b>     receives the data that indicates IU, PIT,
        Gr1, and Gr2 from the controller     <b>6</b>    , calculates the unit conversion factor on the basis of the
        expression (1), and stores data that indicates the unit conversion factor that is calculated. The unit
        conversion unit     <b>11</b>     includes a storage unit and stores the data that indicates the calculated unit
        conversion factor in the storage unit. An example of the storage unit is a flash memory. When receiving the
        command signal from the controller     <b>6</b>    , the unit conversion unit     <b>11</b>     converts the
        command signal to the signal that specifies the rotational position of the motor     <b>3</b>     on the basis
        of the expression (2).
    </p>
    <p id="p-0031" num="0028">The motor control apparatus     <b>1</b>     further includes a position control unit     <b>
        12
    </b>     that calculates a speed command that specifies the rotational speed of the motor     <b>3</b>     on the
        basis of the signal that specifies the rotational position of the motor     <b>3</b>    , which is obtained by
        the unit conversion unit     <b>11</b>    , and a detection signal that indicates a rotational position of the
        motor     <b>3</b>     detected by the rotational-position detector     <b>5</b>     that is to be replaced.
        Specifically, the position control unit     <b>12</b>     calculates the speed command by subtracting the
        rotational position of the motor     <b>3</b>     detected by the rotational-position detector     <b>5</b>     that
        is to be replaced from the rotational position of the motor     <b>3</b>     specified by the signal obtained by
        the unit conversion unit     <b>11</b>    . The signal obtained by the unit conversion unit     <b>11</b>     is
        a signal resulting from the conversion performed by the unit conversion unit     <b>11</b>     on the command
        signal, which specifies the destination position of the table     <b>4</b>    .
    </p>
    <p id="p-0032" num="0029">The motor control apparatus     <b>1</b>     further includes a speed calculation unit     <b>
        13
    </b>     that calculates the rotational speed of the motor     <b>3</b>     by differentiating the rotational
        position of the motor     <b>3</b>     that is detected by the rotational-position detector     <b>5</b>     that
        is to be replaced with respect to time. The motor control apparatus     <b>1</b>     further includes a speed
        control unit     <b>14</b>     that generates a current command that specifies a value of current to be applied
        to the motor     <b>3</b>     on the basis of the speed command, which is calculated by the position control
        unit     <b>12</b>    , and the rotational speed of the motor     <b>3</b>     that is calculated by the speed
        calculation unit     <b>13</b>    . Specifically, the speed control unit     <b>14</b>     calculates the
        current command by subtracting the rotational speed of the motor     <b>3</b>     that is calculated by the
        speed calculation unit     <b>13</b>     from the rotational speed of the motor     <b>3</b>     that is
        specified by the speed command, which is calculated by the position control unit     <b>12</b>    .
    </p>
    <p id="p-0033" num="0030">The motor control apparatus     <b>1</b>     further includes a current control unit     <b>
        15
    </b>     that generates a voltage command that specifies a value of voltage to be applied to the motor     <b>3</b>     on
        the basis of the current command, which is generated by the speed control unit     <b>14</b>    , and applies a
        voltage of the specified value to the motor     <b>3</b>    . That is, before the rotational-position detector     <b>
            5
        </b>     is replaced with a new rotational-position detector     <b>5</b>    , the current control unit     <b>
            15
        </b>     controls the voltage to be applied to the motor     <b>3</b>     on the basis of the command signal and
        the detection signal that indicates the rotational position of the motor     <b>3</b>     that is detected by
        the rotational-position detector     <b>5</b>     that is to be replaced.
    </p>
    <p id="p-0034" num="0031">The motor control apparatus     <b>1</b>     further includes a storage unit     <b>16</b>     that
        stores information that indicates the resolution of the rotational-position detector     <b>5</b>     that is to
        be replaced. That is, the storage unit     <b>16</b>     stores information that indicates the resolution of the
        first rotational-position detector     <b>5</b>    . An example of the storage unit     <b>16</b>     is a flash
        memory. The motor control apparatus     <b>1</b>     further includes a resolution conversion unit     <b>17</b>     that,
        when the rotational-position detector     <b>5</b>     is replaced, calculates a scale factor of resolution of
        the replacing rotational-position detector     <b>5</b>     with respect to the resolution of the replaced
        rotational-position detector     <b>5</b>    . The scale factor calculated by the resolution conversion unit     <b>
            17
        </b>     is hereinafter defined as “conversion scale factor”. As described above, the replacing
        rotational-position detector     <b>5</b>     is the second rotational-position detector     <b>5</b>    .
    </p>
    <p id="p-0035" num="0032">Specifically, the resolution conversion unit     <b>17</b>     receives information that
        indicates the resolution of the replacing rotational-position detector     <b>5</b>     from the replacing
        rotational-position detector     <b>5</b>     and calculates the conversion scale factor of the resolution of
        the replacing rotational-position detector     <b>5</b>     with respect to the resolution of the replaced
        rotational-position detector     <b>5</b>     by dividing the resolution of the replacing rotational-position
        detector     <b>5</b>    , which is indicated by the received information, by the resolution of the replaced
        rotational-position detector     <b>5</b>    , which is indicated by the information stored in the storage unit     <b>
            16
        </b>    .
    </p>
    <p id="p-0036" num="0033">For example, if the resolution of the replaced rotational-position detector     <b>5</b>     is
        1,048,576 (the number of pulses per rotation of the motor     <b>3</b>    ) and the resolution of the replacing
        rotational-position detector     <b>5</b>     is 4,194,304 (the number of pulses per rotation of the motor     <b>
            3
        </b>    ), the resolution conversion unit     <b>17</b>     calculates the conversion scale factor as “four
        times”. Four times corresponds to two bits. The resolution conversion unit     <b>17</b>     includes a storage
        unit and stores data that indicates the calculated conversion scale factor in the storage unit. An example of
        the storage unit is a flash memory.
    </p>
    <p id="p-0037" num="0034">When the rotational-position detector     <b>5</b>     is replaced, the resolution
        conversion unit     <b>17</b>     divides a value indicated by a detection signal that indicates a rotational
        position of the motor     <b>3</b>     that is detected by the replacing rotational-position detector     <b>5
        </b>     by the conversion scale factor to convert the detection signal that indicates the rotational position
        of the motor     <b>3</b>     to a signal of the resolution of the replaced rotational-position detector     <b>
            5
        </b>    . Specifically, when the rotational-position detector     <b>5</b>     is replaced, the resolution
        conversion unit     <b>17</b>     converts, on the basis of a ratio of the resolution of the first
        rotational-position detector     <b>5</b>    , which detects a rotational position of the motor     <b>3</b>    ,
        and the resolution of the second rotational-position detector     <b>5</b>    , which detects a rotational
        position of the motor     <b>3</b>    , the detection signal that indicates the rotational position of the motor     <b>
            3
        </b>     that is detected by the second rotational-position detector     <b>5</b>     to a signal of the
        resolution of the first rotational-position detector     <b>5</b>    . As described above, the first
        rotational-position detector     <b>5</b>     is the replaced rotational-position detector     <b>5</b>     and
        the second rotational-position detector     <b>5</b>     is the replacing rotational-position detector     <b>
            5
        </b>    . Before the rotational-position detector     <b>5</b>     is replaced, the resolution conversion unit     <b>
            17
        </b>     outputs the detection signal that indicates the rotational position of the motor     <b>3</b>     detected
        by the replacing rotational-position detector     <b>5</b>     to the position control unit     <b>12</b>     and
        the speed calculation unit     <b>13</b>    .
    </p>
    <p id="p-0038" num="0035">When the rotational-position detector     <b>5</b>     is replaced, the position control
        unit     <b>12</b>     calculates the speed command, which specifies the rotational speed of the motor     <b>
            3
        </b>    , on the basis of the signal that specifies the rotational position of the motor     <b>3</b>    , which
        is obtained by the unit conversion unit     <b>11</b>    , and the signal obtained by the conversion performed
        by the resolution conversion unit     <b>17</b>     on the detection signal detected by the replacing
        rotational-position detector     <b>5</b>    . The signal that indicates the rotational position of the motor     <b>
            3
        </b>     obtained by the unit conversion unit     <b>11</b>     is a signal resulting from the conversion
        performed by the unit conversion unit     <b>11</b>     on the command signal from the controller     <b>6</b>    .
        The command signal from the controller     <b>6</b>     is a signal that specifies the destination position of
        the table     <b>4</b>    . When the rotational-position detector     <b>5</b>     is replaced, the speed
        calculation unit     <b>13</b>     calculates the rotational speed of the motor     <b>3</b>     by
        differentiating a value indicated by the signal obtained by the resolution conversion unit     <b>17</b>     with
        time.
    </p>
    <p id="p-0039" num="0036">When the rotational-position detector     <b>5</b>     is replaced, the current control
        unit     <b>15</b>     generates the voltage command, which specifies a value of voltage to be applied to the
        motor     <b>3</b>    , on the basis of the current command that is generated by the speed control unit     <b>
            14
        </b>     after the rotational-position detector     <b>5</b>     is replaced, and applies a voltage of the
        specified value to the motor     <b>3</b>    . That is, when the rotational-position detector     <b>5</b>     is
        replaced, the current control unit     <b>15</b>     controls the voltage to be applied to the motor     <b>3
        </b>     on the basis of the command signal and the signal obtained by the conversion performed by the
        resolution conversion unit     <b>17</b>     on the detection signal detected by the replacing
        rotational-position detector     <b>5</b>    .
    </p>
    <p id="p-0040" num="0037">Generally, the resolution of the rotational-position detector     <b>5</b>     is a power
        of two. When a central processing unit (CPU) achieves a function of the resolution conversion unit     <b>17</b>    ,
        the CPU converts a value indicated by the detection signal detected by the replacing rotational-position
        detector     <b>5</b>     to a signal of the resolution of the replaced rotational-position detector     <b>5
        </b>     by performing a shift operation on the basis of a bit number corresponding to the calculated conversion
        scale factor.
    </p>
    <p id="p-0041" num="0038">
        <figref idrefs="DRAWINGS">FIG. 2</figref>     is a flowchart indicating portions of the operations of the unit
        conversion unit     <b>11</b>     and the resolution conversion unit     <b>17</b>    , which are included in
        the motor control apparatus     <b>1</b>     according to the first embodiment. When the rotational-position
        detector     <b>5</b>     is replaced, the unit conversion unit     <b>11</b>    , upon start of the motor
        control apparatus     <b>1</b>    , receives unit conversion data that is data for calculating the unit
        conversion factor, which is expressed by the expression (1) described above, from the controller     <b>6</b>     (S    <b>
        1
    </b>    ). The unit conversion unit     <b>11</b>     calculates the unit conversion factor on the basis of the unit
        conversion data and stores data that indicates the unit conversion factor (S    <b>2</b>    ). The resolution
        conversion unit     <b>17</b>     acquires the information that indicates the resolution of the replaced
        rotational-position detector     <b>5</b>     from the storage unit     <b>16</b>     (S    <b>3</b>    ) and
        receives the information that indicates the resolution of the replacing rotational-position detector     <b>5
    </b>     from the replacing rotational-position detector     <b>5</b>     (S    <b>4</b>    ).
    </p>
    <p id="p-0042" num="0039">The resolution conversion unit     <b>17</b>     determines whether there is a difference
        between the resolution indicated by the information acquired in step S    <b>3</b>     and the resolution
        indicated by the information received in step S    <b>4</b>     (S    <b>5</b>    ). If it is determined that
        there is no difference between the two of the resolution (No in S    <b>5</b>    ), the resolution conversion
        unit     <b>17</b>     terminates the operation for calculating the conversion scale factor. If it is determined
        that there is a difference between the two of the resolution (Yes in S    <b>5</b>    ), the resolution
        conversion unit     <b>17</b>     calculates (S    <b>6</b>    ) the conversion scale factor by dividing the
        resolution indicated by the information received in step S    <b>4</b>     by the resolution indicated by the
        information acquired in step S    <b>3</b>    . The resolution conversion unit     <b>17</b>     stores (S    <b>
            7
        </b>    ) data that indicates the conversion scale factor calculated in step S    <b>6</b>    .
    </p>
    <p id="p-0043" num="0040">After the unit conversion unit     <b>11</b>     stores the data that indicates the unit
        conversion factor and the resolution conversion unit     <b>17</b>     stores the data that indicates the
        conversion scale factor calculated after the replacement of the rotational-position detector     <b>5</b>    ,
        the unit conversion unit     <b>11</b>    , upon receiving the command signal from the controller     <b>6</b>    ,
        converts the command signal to the signal that specifies the rotational position of the motor     <b>3</b>     on
        the basis of the expression (2). The position control unit     <b>12</b>     calculates the speed command, which
        specifies the rotational speed of the motor     <b>3</b>    , on the basis of the signal that specifies the
        rotational position of the motor     <b>3</b>    , which is obtained by the unit conversion unit     <b>11</b>    ,
        and the signal obtained by the conversion performed by the resolution conversion unit     <b>17</b>     on the
        detection signal detected by the replacing rotational-position detector     <b>5</b>    .
    </p>
    <p id="p-0044" num="0041">The speed calculation unit     <b>13</b>     calculates the rotational speed of the motor     <b>
        3
    </b>     by differentiating a value indicated by the signal obtained by the resolution conversion unit     <b>17</b>     with
        time. The speed control unit     <b>14</b>     generates the current command, which specifies a value of current
        to be applied to the motor     <b>3</b>    , on the basis of the speed command, which is calculated by the
        position control unit     <b>12</b>    , and the rotational speed of the motor     <b>3</b>     that is
        calculated by the speed calculation unit     <b>13</b>    . The current control unit     <b>15</b>     generates
        the voltage command, which specifies a value of voltage to be applied to the motor     <b>3</b>    , on the
        basis of the current command, which is generated by the speed control unit     <b>14</b>    , and applies a
        voltage of the specified value to the motor     <b>3</b>    .
    </p>
    <p id="p-0045" num="0042">As described above, when the rotational-position detector     <b>5</b>    , which detects
        the rotational position of the motor     <b>3</b>    , is replaced, the motor control apparatus     <b>1</b>     converts,
        on the basis of the ratio of the resolution of the first rotational-position detector     <b>5</b>    , which is
        the replaced rotational-position detector     <b>5</b>    , and the resolution of the second rotational-position
        detector     <b>5</b>    , which is the replacing rotational-position detector     <b>5</b>    , the detection
        signal that indicates the rotational position of the motor     <b>3</b>     that is detected by the second
        rotational-position detector     <b>5</b>     to a signal of the resolution of the first rotational-position
        detector     <b>5</b>    . The motor control apparatus     <b>1</b>     controls the voltage to be applied to
        the motor     <b>3</b>     on the basis of the command signal, which specifies the destination position of the
        table     <b>4</b>    , which is a subject to be moved, and the signal obtained by the conversion performed on
        the detection signal described above.
    </p>
    <p id="p-0046" num="0043">The motor control apparatus     <b>1</b>     can thus control the motor     <b>3</b>     with
        the precision provided by the resolution of the first rotational-position detector     <b>5</b>    , which is
        the rotational-position detector     <b>5</b>     that is to be replaced, after the first rotational-position
        detector     <b>5</b>     is replaced by the second rotational-position detector     <b>5</b>    , which has
        resolution different from the resolution of the first rotational-position detector     <b>5</b>    .
    </p>
    <p id="p-0047" num="0044">In the first embodiment described above, the resolution conversion unit     <b>17</b>     receives
        the information that indicates the resolution of the replacing rotational-position detector     <b>5</b>     from
        the replacing rotational-position detector     <b>5</b>    . Alternatively, the resolution conversion unit     <b>
            17
        </b>     may receive the information that indicates the resolution of the replacing rotational-position detector     <b>
            5
        </b>     from the controller     <b>6</b>    . The resolution conversion unit     <b>17</b>     may receive the
        information that indicates the resolution of the rotational-position detector     <b>5</b>     that is to be
        replaced from the rotational-position detector     <b>5</b>     that is to be replaced or from the controller     <b>
            6
        </b>    . In any of the cases, the storage unit     <b>16</b>     stores the information that indicates the
        resolution of the rotational-position detector     <b>5</b>     that is to be replaced. The resolution
        conversion unit     <b>17</b>     converts the detection signal detected by the replacing rotational-position
        detector     <b>5</b>     to a signal of the resolution of the replaced rotational-position detector     <b>5
        </b>     on the basis of the ratio of the resolution of the replaced rotational-position detector     <b>5</b>     and
        the resolution of the replacing rotational-position detector     <b>5</b>    .
    </p>
    <p id="p-0048" num="0045">The machine apparatus     <b>2</b>     in the first embodiment described above is a table
        driving apparatus that converts a rotational motion of the motor     <b>3</b>     to a linear motion and drives
        the table     <b>4</b>    , and the number of motors     <b>3</b>     illustrated in     <figref
                idrefs="DRAWINGS">FIG. 1
        </figref>     is one. The machine apparatus     <b>2</b>     may include two motors     <b>3</b>    . One of the
        two motors     <b>3</b>     is to give a linear motion to the table     <b>4</b>     on an X axis and the other
        one of the two motors     <b>3</b>     is to give a linear motion to the table     <b>4</b>     on a Y axis that
        is orthogonal to the X axis. If the machine apparatus     <b>2</b>     includes two motors     <b>3</b>    , the
        motor control apparatus     <b>1</b>     performs the operation described above for each of the two motors     <b>
            3
        </b>    .
    </p>
    <p id="p-0049" num="0046">
        <figref idrefs="DRAWINGS">FIG. 3</figref>     is a diagram illustrating a processor     <b>31</b>     that is
        used when at least a portion of functions of the unit conversion unit     <b>11</b>    , the position control
        unit     <b>12</b>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    ,
        the current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>    , which are
        included in the motor control apparatus     <b>1</b>     according to the first embodiment, is achieved by the
        processor     <b>31</b>    . That is, at least a portion of the function of the unit conversion unit     <b>11
    </b>    , the position control unit     <b>12</b>    , the speed calculation unit     <b>13</b>    , the speed
        control unit     <b>14</b>    , the current control unit     <b>15</b>    , and the resolution conversion unit     <b>
        17
    </b>     may be achieved by the processor     <b>31</b>    , which executes a program stored in a memory     <b>32
    </b>    . The processor     <b>31</b>     is a CPU, a processing unit, an arithmetic unit, a microprocessor, a
        microcomputer, or a digital signal processor (DSP). The memory     <b>32</b>     is also illustrated in     <figref
            idrefs="DRAWINGS">FIG. 3
    </figref>    .
    </p>
    <p id="p-0050" num="0047">When at least a portion of the function of the unit conversion unit     <b>11</b>    , the
        position control unit     <b>12</b>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>
            14
        </b>    , the current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>     is
        achieved by the processor     <b>31</b>    , the portion of the function is achieved by a combination of the
        processor     <b>31</b>    , and software, firmware, or software and firmware. The software or firmware is
        described as a program and stored in the memory     <b>32</b>    . At least a portion of the function of the
        unit conversion unit     <b>11</b>    , the position control unit     <b>12</b>    , the speed calculation unit     <b>
            13
        </b>    , the speed control unit     <b>14</b>    , the current control unit     <b>15</b>    , and the
        resolution conversion unit     <b>17</b>     is achieved by the processor     <b>31</b>     reading out to
        execute the program stored in the memory     <b>32</b>    .
    </p>
    <p id="p-0051" num="0048">When at least a portion of the function of the unit conversion unit     <b>11</b>    , the
        position control unit     <b>12</b>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>
            14
        </b>    , the current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>     is
        achieved by the processor     <b>31</b>    , the motor control apparatus     <b>1</b>     includes the memory     <b>
            32
        </b>     for storing a program that results in execution of steps executed by at least a portion of the unit
        conversion unit     <b>11</b>    , the position control unit     <b>12</b>    , the speed calculation unit     <b>
            13
        </b>    , the speed control unit     <b>14</b>    , the current control unit     <b>15</b>    , and the
        resolution conversion unit     <b>17</b>    . It can be also said that the program stored in the memory     <b>
            32
        </b>     causes a computer to execute a procedure or method executed by at least a portion of the unit
        conversion unit     <b>11</b>    , the position control unit     <b>12</b>    , the speed calculation unit     <b>
            13
        </b>    , the speed control unit     <b>14</b>    , the current control unit     <b>15</b>    , and the
        resolution conversion unit     <b>17</b>    .
    </p>
    <p id="p-0052" num="0049">The memory     <b>32</b>     is, for example, a nonvolatile or volatile semiconductor
        memory, such as a random access memory (RAM), a read only memory (ROM), a flash memory, an erasable programmable
        read only memory (EPROM), an electrically erasable programmable read-only memory (EEPROM, which is a registered
        trademark), a magnetic disk, a flexible disk, an optical disk, a compact disk, a mini disk, a digital versatile
        disc (DVD), or the like.
    </p>
    <p id="p-0053" num="0050">
        <figref idrefs="DRAWINGS">FIG. 4</figref>     is a diagram illustrating a processing circuit     <b>41</b>     that
        is used when at least one of constituent elements configuring the unit conversion unit     <b>11</b>    , the
        position control unit     <b>12</b>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>
        14
    </b>    , the current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>    , which
        are included in the motor control apparatus     <b>1</b>     according to the first embodiment, is achieved by
        the processing circuit     <b>41</b>    . That is, at least a portion of functions of the unit conversion unit     <b>
        11
    </b>    , the position control unit     <b>12</b>    , the speed calculation unit     <b>13</b>    , the speed
        control unit     <b>14</b>    , the current control unit     <b>15</b>    , and the resolution conversion unit     <b>
        17
    </b>     may be achieved by the processing circuit     <b>41</b>    .
    </p>
    <p id="p-0054" num="0051">The processing circuit     <b>41</b>     is dedicated hardware. The processing circuit     <b>
        41
    </b>     is, for example, a single circuit, a compound circuit, a programmed processor, a parallel programmed
        processor, an application specific integrated circuit (ASIC), a field-programmable gate array (FPGA), or a
        combination of them. A portion of the unit conversion unit     <b>11</b>    , the position control unit     <b>
            12
        </b>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    , the current
        control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>     may be dedicated hardware
        separate from hardware of the remaining portions.
    </p>
    <p id="p-0055" num="0052">A portion of functions of the unit conversion unit     <b>11</b>    , the position control
        unit     <b>12</b>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    ,
        the current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>     may be achieved
        by software or firmware, and the remaining portions of the functions may be achieved by dedicated hardware. As
        described above, the functions of the unit conversion unit     <b>11</b>    , the position control unit     <b>
            12
        </b>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    , the current
        control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>     can be achieved by
        hardware, software, firmware, or a combination of them.
    </p>
    <heading id="h-0012">Second Embodiment</heading>
    <p id="p-0056" num="0053">
        <figref idrefs="DRAWINGS">FIG. 5</figref>     is a diagram illustrating a configuration of a motor control
        apparatus     <b>1</b>    A according to a second embodiment. The motor control apparatus     <b>1</b>    A
        includes the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    , and the current
        control unit     <b>15</b>    , which are included in the motor control apparatus     <b>1</b>     according to
        the first embodiment. The unit conversion unit     <b>11</b>     in the first embodiment is replaced by a unit
        conversion unit     <b>11</b>
        <i>a</i>    , and the position control unit     <b>12</b>     in the first embodiment is replaced by a position
        control unit     <b>12</b>
        <i>a</i>    . The storage unit     <b>16</b>     in the first embodiment is replaced by a storage unit     <b>
        16
    </b>
        <i>a</i>    . An example of the storage unit     <b>16</b>
        <i>a</i>    is a flash memory. The resolution conversion unit     <b>17</b>     is replaced by a resolution
        conversion unit     <b>17</b>
        <i>a.</i>
    </p>
    <p id="p-0057" num="0054">A machine-end detector     <b>7</b>     that detects the position of the table     <b>4
    </b>     is used in the second embodiment. Specifically, the machine-end detector     <b>7</b>     directly detects
        the position of the table     <b>4</b>    . Each of the rotational-position detector     <b>5</b>     and the
        machine-end detector     <b>7</b>     transmits a detection signal to the motor control apparatus     <b>1</b>    A
        in the second embodiment. A scheme in which each of the rotational-position detector     <b>5</b>     and the
        machine-end detector     <b>7</b>     transmits a detection signal to the motor control apparatus     <b>1</b>    A
        is a fully-closed control scheme. In the second embodiment, the rotational-position detector     <b>5</b>     is
        not replaced and the machine-end detector     <b>7</b>     is replaced. Difference from the first embodiment is
        mainly described in the second embodiment.
    </p>
    <p id="p-0058" num="0055">The machine-end detector     <b>7</b>     detects the position of the table     <b>4</b>     without
        being affected by the first gear     <b>21</b>     and the second gear     <b>22</b>    . Resolution of the
        machine-end detector     <b>7</b>     is defined as PIT (mm), which is a movement quantity per rotation of the
        ball screw     <b>23</b>    . The unit conversion unit     <b>11</b>
        <i>a</i>    converts a command signal that is transmitted by the controller     <b>6</b>     to the motor
        control apparatus     <b>1</b>    A to a signal that specifies the rotational position of the motor     <b>3</b>     by
        using an expression (3) described below. The command signal is a signal that specifies a destination position of
        the table     <b>4</b>    . The expression (3) is an expression that expresses a unit conversion factor for use
        in the conversion of the command signal to the signal that specifies the rotational position of the motor     <b>
            3
        </b>    .
    </p>
    <p id="p-0059" num="0000">
        <maths id="MATH-US-00003" num="00003">
            <math overflow="scroll">
                <mtable>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mrow>
                                    <mo>[</mo>
                                    <mrow>
                                        <mi>Formula</mi>
                                        <mo></mo>
                                        <mstyle>
                                            <mspace height="0.8ex" width="0.8em"/>
                                        </mstyle>
                                        <mo></mo>
                                        <mn>3</mn>
                                    </mrow>
                                    <mo>]</mo>
                                </mrow>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="34.4ex" width="34.4em"/>
                                </mstyle>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mstyle>
                                <mspace height="0.3ex" width="0.3em"/>
                            </mstyle>
                        </mtd>
                    </mtr>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mrow>
                                    <mi>Unit</mi>
                                    <mo></mo>
                                    <mstyle>
                                        <mspace height="0.8ex" width="0.8em"/>
                                    </mstyle>
                                    <mo></mo>
                                    <mi>conversion</mi>
                                    <mo></mo>
                                    <mstyle>
                                        <mspace height="0.8ex" width="0.8em"/>
                                    </mstyle>
                                    <mo></mo>
                                    <mi>factor</mi>
                                </mrow>
                                <mo>=</mo>
                                <mfrac>
                                    <mrow>
                                        <mi>IU</mi>
                                        <mo>×</mo>
                                        <mi>RNG</mi>
                                    </mrow>
                                    <mi>PIT</mi>
                                </mfrac>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mo>(</mo>
                                <mn>3</mn>
                                <mo>)</mo>
                            </mrow>
                        </mtd>
                    </mtr>
                </mtable>
            </math>
        </maths>
    </p>
    <p id="p-0060" num="0056">The storage unit     <b>16</b>
        <i>a</i>    stores information that indicates the resolution of the machine-end detector     <b>7</b>     that
        is to be replaced. When the machine-end detector     <b>7</b>     is replaced, the resolution conversion unit     <b>
            17
        </b>
        <i>a</i>    calculates a conversion scale factor of resolution of a replacing machine-end detector     <b>7</b>     with
        respect to the resolution of the replaced machine-end detector     <b>7</b>    . Specifically, the resolution
        conversion unit     <b>17</b>
        <i>a</i>    receives information that indicates the resolution of the replacing machine-end detector     <b>7
        </b>     from the replacing machine-end detector     <b>7</b>     and calculates the conversion scale factor of
        the resolution of the replacing machine-end detector     <b>7</b>     with respect to the resolution of the
        replaced machine-end detector     <b>7</b>     by dividing the resolution of the replacing machine-end detector     <b>
            7
        </b>    , which is indicated by the received information, by the resolution of the replaced machine-end detector     <b>
            7
        </b>    , which is indicated by the information stored in the storage unit     <b>16</b>
        <i>a.</i>
    </p>
    <p id="p-0061" num="0057">The resolution conversion unit     <b>17</b>
        <i>a</i>    includes a storage unit and stores data that indicates the calculated conversion scale factor in the
        storage unit. An example of the storage unit is a flash memory. The replaced machine-end detector     <b>7</b>     is
        a first machine-end detector     <b>7</b>     and the replacing machine-end detector     <b>7</b>     is a
        second machine-end detector     <b>7</b>    .
    </p>
    <p id="p-0062" num="0058">When the machine-end detector     <b>7</b>     is replaced, the resolution conversion unit     <b>
        17
    </b>
        <i>a</i>    divides a value indicated by the detection signal that indicates a position of the table     <b>4
        </b>     detected by the replacing machine-end detector     <b>7</b>     by the conversion scale factor to
        convert the detection signal that indicates the position of the table     <b>4</b>     that is detected by the
        replacing machine-end detector     <b>7</b>     to a signal of the resolution of the replaced machine-end
        detector     <b>7</b>    . Specifically, when the machine-end detector     <b>7</b>     is replaced, the
        resolution conversion unit     <b>17</b>
        <i>a</i>    converts the detection signal that indicates the position of the table     <b>4</b>     that is
        detected by the second machine-end detector     <b>7</b>     to a signal of the resolution of the first
        machine-end detector     <b>7</b>     on the basis of a ratio of the resolution of the first machine-end
        detector     <b>7</b>     and the resolution of the second machine-end detector     <b>7</b>    .
    </p>
    <p id="p-0063" num="0059">The position control unit     <b>12</b>
        <i>a</i>    has a function of converting the position of the table     <b>4</b>     to the rotational position
        of the motor     <b>3</b>    . Before the machine-end detector     <b>7</b>     is replaced, the position
        control unit     <b>12</b>
        <i>a</i>    uses this function and calculates a speed command that specifies the rotational speed of the motor     <b>
            3
        </b>     on the basis of the signal that specifies the rotational position of the motor     <b>3</b>    , which
        is obtained by the unit conversion unit     <b>11</b>
        <i>a</i>    , and the detection signal detected by the machine-end detector     <b>7</b>    . When the
        machine-end detector     <b>7</b>     is replaced, the position control unit     <b>12</b>
        <i>a</i>    uses this function and calculates the speed command, which specifies the rotational speed of the
        motor     <b>3</b>    , on the basis of the signal that specifies the rotational position of the motor     <b>
            3
        </b>    , which is obtained by the unit conversion unit     <b>11</b>
        <i>a</i>    , and a signal obtained by the conversion performed by the resolution conversion unit     <b>17</b>
        <i>a</i>    on the detection signal detected by the replacing machine-end detector     <b>7</b>    . The current
        control unit     <b>15</b>     controls the voltage to be applied to the motor     <b>3</b>     on the basis of
        the command signal, which specifies the destination position of the table     <b>4</b>    , which is a subject
        to be moved, and the signal obtained by the conversion performed by the resolution conversion unit     <b>17</b>
        <i>a</i>    on the detection signal.
    </p>
    <p id="p-0064" num="0060">When the machine-end detector     <b>7</b>     is replaced, the motor control apparatus     <b>
        1
    </b>    A in the second embodiment converts, on the basis of the ratio of the resolution of the first machine-end
        detector     <b>7</b>    , which is the replaced machine-end detector     <b>7</b>    , and the resolution of
        the second machine-end detector     <b>7</b>    , which is the replacing machine-end detector     <b>7</b>    ,
        the detection signal that indicates the position of the table     <b>4</b>     that is detected by the second
        machine-end detector     <b>7</b>     to a signal of the resolution of the first machine-end detector     <b>7
        </b>    . The motor control apparatus     <b>1</b>    A controls the voltage to be applied to the motor     <b>
            3
        </b>     on the basis of the command signal, which specifies the destination position of the table     <b>4</b>    ,
        which is a subject to be moved, and the signal obtained by the conversion performed on the detection signal
        described above.
    </p>
    <p id="p-0065" num="0061">The motor control apparatus     <b>1</b>    A can thus control the motor     <b>3</b>     with
        the precision provided by the resolution of the first machine-end detector     <b>7</b>    , which is the
        replaced machine-end detector     <b>7</b>    , after the first machine-end detector     <b>7</b>     is
        replaced by the second machine-end detector     <b>7</b>    , which has resolution different from the resolution
        of the first machine-end detector     <b>7</b>    .
    </p>
    <p id="p-0066" num="0062">In the second embodiment described above, the resolution conversion unit     <b>17</b>
        <i>a</i>    receives the information that indicates the resolution of the replacing machine-end detector     <b>
            7
        </b>     from the replacing machine-end detector     <b>7</b>    . Alternatively, the resolution conversion unit     <b>
            17
        </b>
        <i>a</i>    may receive the information that indicates the resolution of the replacing machine-end detector     <b>
            7
        </b>     from the controller     <b>6</b>    . The resolution conversion unit     <b>17</b>
        <i>a</i>    may receive the information that indicates the resolution of the replaced machine-end detector     <b>
            7
        </b>     from the replaced machine-end detector     <b>7</b>     or from the controller     <b>6</b>    . In any
        of the cases, the storage unit     <b>16</b>
        <i>a</i>    stores information that indicates the resolution of the replaced machine-end detector     <b>7</b>    .
        The resolution conversion unit     <b>17</b>
        <i>a</i>    converts the detection signal detected by the replacing machine-end detector     <b>7</b>     to a
        signal of the resolution of the replaced machine-end detector     <b>7</b>     on the basis of the ratio of the
        resolution of the replaced machine-end detector     <b>7</b>     and the resolution of the replacing machine-end
        detector     <b>7</b>    .
    </p>
    <p id="p-0067" num="0063">At least a portion of a function of the unit conversion unit     <b>11</b>
        <i>a</i>    , the position control unit     <b>12</b>
        <i>a</i>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    , the
        current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>
        <i>a</i>    may be achieved by a processor that has the same function as the processor     <b>31</b>     in the
        first embodiment. In this case, the motor control apparatus     <b>1</b>    A includes a memory for storing a
        program that results in execution of steps executed by at least a portion of the unit conversion unit     <b>
            11
        </b>
        <i>a</i>    , the position control unit     <b>12</b>
        <i>a</i>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    , the
        current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>
        <i>a</i>    . This memory has the same function as the memory     <b>32</b>     in the first embodiment.
    </p>
    <p id="p-0068" num="0064">At least one of constituent elements configuring the unit conversion unit     <b>11</b>
        <i>a</i>    , the position control unit     <b>12</b>
        <i>a</i>    , the speed calculation unit     <b>13</b>    , the speed control unit     <b>14</b>    , the
        current control unit     <b>15</b>    , and the resolution conversion unit     <b>17</b>
        <i>a</i>    may be achieved by a processing circuit that has the same function as the processing circuit     <b>
            41
        </b>     in the first embodiment.
    </p>
    <p id="p-0069" num="0065">Note that the configurations described in the foregoing embodiments are examples of the
        present invention; combining the present invention with other publicly known techniques is possible, and partial
        omissions or modifications are possible without departing from the spirit of the present invention.
    </p>
    <heading id="h-0013">REFERENCE SIGNS LIST</heading>
    <p id="p-0070" num="0000">
        <ul>
            <li id="ul0001-0001" num="0000">
                <ul>
                    <li id="ul0002-0001" num="0066">
                        <b>1</b>            ,             <b>1</b>            A motor control apparatus;             <b>
                        2
                    </b>             machine apparatus;             <b>3</b>             motor;             <b>4</b>             table;             <b>
                        5
                    </b>             rotational-position detector;             <b>6</b>             controller;             <b>
                        7
                    </b>             machine-end detector;             <b>11</b>            ,             <b>11</b>
                        <i>a</i>            unit conversion unit;             <b>12</b>            ,             <b>12
                    </b>
                        <i>a</i>            position control unit;             <b>13</b>             speed calculation
                        unit;             <b>14</b>             speed control unit;             <b>15</b>             current
                        control unit;             <b>16</b>            ,             <b>16</b>
                        <i>a</i>            storage unit;             <b>17</b>            ,             <b>17</b>
                        <i>a</i>            resolution conversion unit;             <b>21</b>             first gear;             <b>
                        22
                    </b>             second gear;             <b>23</b>             ball screw;             <b>31</b>             processor;             <b>
                        32
                    </b>             memory;             <b>41</b>             processing circuit.
                    </li>
                </ul>
            </li>
        </ul>
    </p>
    <?detailed-description description="Detailed Description" end="tail"?>
</description>