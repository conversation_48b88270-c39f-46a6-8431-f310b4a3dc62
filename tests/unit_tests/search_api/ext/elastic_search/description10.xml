<description lang="EN" load-source="patent-office" mxw-id="PDES59562920">
    <?cross-reference-to-related-applications description="Cross Reference To Related Applications" end="lead"?>
    <heading id="h-0001">CROSS-REFERENCE TO RELATED APPLICATIONS</heading>
    <p id="p-0002" num="0001">This application is a continuation of U.S. patent application Ser. No. 12/324,388, filed Nov. 26, 2008, which relates to U.S. Patent Application 20030130841 A1, titled “System and Method of Spoken Language Understanding in Human Computer Dialogs”, filed Dec. 5, 2002, the contents of which are incorporated by reference.</p>
    <?cross-reference-to-related-applications description="Cross Reference To Related Applications" end="tail"?>
    <?summary-of-invention description="Summary of Invention" end="lead"?>
    <heading id="h-0002">BACKGROUND OF THE INVENTION</heading>
    <p id="p-0003" num="0002">1. Field of the Invention</p>
    <p id="p-0004" num="0003">The present invention relates to automatic speech recognition and more specifically to recognizing and translating speech.</p>
    <p id="p-0005" num="0004">2. Introduction</p>
    <p id="p-0006" num="0005">Automatic speech processing has advanced significantly but is still largely compartmentalized. For instance, automatic speech recognition typically transcribes speech orthographically and hence insufficiently captures context beyond words. Enriched transcription combines automatic speech recognition, speaker identification and natural language processing with the goal of producing richly annotated speech transcriptions that are useful both to human readers and to automated programs for indexing, retrieval and analysis. Some examples of enriched transcription include punctuation detection, topic segmentation, disfluency detection and clean-up, semantic annotation, pitch accent, boundary tone detection, speaker segmentation, speaker recognition, and annotation of speaker attributes. These meta-level tags are an intermediate representation of the context of the utterance along with the content provided by the orthographical transcription.</p>
    <p id="p-0007" num="0006">Accordingly, what is needed in the art is an improved way to enrich automatic speech translation with information beyond the text to be translated.</p>
    <heading id="h-0003">SUMMARY</heading>
    <p id="p-0008" num="0007">Additional features and advantages of the invention will be set forth in the description which follows, and in part will be obvious from the description, or may be learned by practice of the invention. The features and advantages of the invention may be realized and obtained by means of the instruments and combinations particularly pointed out in the appended claims. These and other features of the present invention will become more fully apparent from the following description and appended claims, or may be learned by the practice of the invention as set forth herein.</p>
    <p id="p-0009" num="0008">Disclosed are systems, computer-implemented methods, and tangible computer-readable media for enriching spoken language translation with dialog acts. The method includes receiving a source speech signal, tagging dialog acts associated with the received source speech signal using a classification model (such as a maximum entropy model), dialog acts being domain independent or domain dependent descriptions of an intended action a speaker carries out by uttering the source speech signal, producing an enriched hypothesis of the source speech signal incorporating the dialog act tags, and outputting a natural language response of the enriched hypothesis in a target language. Tags can be grouped into sets such as statement, acknowledgement, abandoned, agreement, question, appreciation, and other. The step of producing an enriched translation of the source speech signal uses a translation model containing a dialog act specific phrase translation table. The method can further include appending to each phrase translation table belonging to a particular dialog act specific translation model those entries from a complete model that are not present in the phrase table of the dialog act specific translation model, and weighting appended entries by a factor α. When the source speech signal is a dialog turn having multiple sentences, the method can further include segmenting the source speech signal, tagging dialog acts in each segment using a maximum entropy model, and producing an enriched translation of each segment in a target language incorporated with the dialog act tags. The method can further include annotating tagged dialog acts.</p>
    <?summary-of-invention description="Summary of Invention" end="tail"?>
    <?brief-description-of-drawings description="Brief Description of Drawings" end="lead"?>
    <description-of-drawings>
        <heading id="h-0004">BRIEF DESCRIPTION OF THE DRAWINGS</heading>
        <p id="p-0010" num="0009">In order to describe the manner in which the above-recited and other advantages and features of the invention can be obtained, a more particular description of the invention briefly described above will be rendered by reference to specific embodiments thereof which are illustrated in the appended drawings. Understanding that these drawings depict only exemplary embodiments of the invention and are not therefore to be considered to be limiting of its scope, the invention will be described and explained with additional specificity and detail through the use of the accompanying drawings in which:</p>
        <p id="p-0011" num="0010">
            <figref idrefs="DRAWINGS">FIG. 1</figref>
            illustrates an example system embodiment;
        </p>
        <p id="p-0012" num="0011">
            <figref idrefs="DRAWINGS">FIG. 2</figref>
            illustrates an example method embodiment;
        </p>
        <p id="p-0013" num="0012">
            <figref idrefs="DRAWINGS">FIG. 3</figref>
            illustrates an example system for translating speech using dialog act tags;
        </p>
        <p id="p-0014" num="0013">
            <figref idrefs="DRAWINGS">FIG. 4</figref>
            illustrates an example of speech translation output enriched with a dialog act; and
        </p>
        <p id="p-0015" num="0014">
            <figref idrefs="DRAWINGS">FIG. 5</figref>
            illustrates an example dialog act taxonomy.
        </p>
    </description-of-drawings>
    <?brief-description-of-drawings description="Brief Description of Drawings" end="tail"?>
    <?detailed-description description="Detailed Description" end="lead"?>
    <heading id="h-0005">DETAILED DESCRIPTION</heading>
    <p id="p-0016" num="0015">Various embodiments of the invention are discussed in detail below. While specific implementations are discussed, it should be understood that this is done for illustration purposes only. A person skilled in the relevant art will recognize that other components and configurations may be used without parting from the spirit and scope of the invention.</p>
    <p id="p-0017" num="0016">
        With reference to
        <figref idrefs="DRAWINGS">FIG. 1</figref>
        , an exemplary system includes a general-purpose computing device
        <b>100</b>
        , including a processing unit (CPU)
        <b>120</b>
        and a system bus
        <b>110</b>
        that couples various system components including the system memory such as read only memory (ROM)
        <b>140</b>
        and random access memory (RAM)
        <b>150</b>
        to the processing unit
        <b>120</b>
        . Other system memory
        <b>130</b>
        may be available for use as well. It can be appreciated that the invention may operate on a computing device with more than one CPU
        <b>120</b>
        or on a group or cluster of computing devices networked together to provide greater processing capability. A processing unit
        <b>120</b>
        can include a general purpose CPU controlled by software as well as a special-purpose processor. An Intel Xeon LV L7345 processor is an example of a general purpose CPU which is controlled by software. Particular functionality may also be built into the design of a separate computer chip. An STMicroelectronics STA013 processor is an example of a special-purpose processor which decodes MP3 audio files. Of course, a processing unit includes any general purpose CPU and a module configured to control the CPU as well as a special-purpose processor where software is effectively incorporated into the actual processor design. A processing unit may essentially be a completely self-contained computing system, containing multiple cores or CPUs, a bus, memory controller, cache, etc. A multi-core processing unit may be symmetric or asymmetric.
    </p>
    <p id="p-0018" num="0017">
        The system bus
        <b>110</b>
        may be any of several types of bus structures including a memory bus or memory controller, a peripheral bus, and a local bus using any of a variety of bus architectures. A basic input/output (BIOS) stored in ROM
        <b>140</b>
        or the like, may provide the basic routine that helps to transfer information between elements within the computing device
        <b>100</b>
        , such as during start-up. The computing device
        <b>100</b>
        further includes storage devices such as a hard disk drive
        <b>160</b>
        , a magnetic disk drive, an optical disk drive, tape drive or the like. The storage device
        <b>160</b>
        is connected to the system bus
        <b>110</b>
        by a drive interface. The drives and the associated computer readable media provide nonvolatile storage of computer readable instructions, data structures, program modules and other data for the computing device
        <b>100</b>
        . In one aspect, a hardware module that performs a particular function includes the software component stored in a tangible computer-readable medium in connection with the necessary hardware components, such as the CPU, bus, display, and so forth, to carry out the function. The basic components are known to those of skill in the art and appropriate variations are contemplated depending on the type of device, such as whether the device is a small, handheld computing device, a desktop computer, or a computer server.
    </p>
    <p id="p-0019" num="0018">Although the exemplary environment described herein employs the hard disk, it should be appreciated by those skilled in the art that other types of computer readable media which can store data that are accessible by a computer, such as magnetic cassettes, flash memory cards, digital versatile disks, cartridges, random access memories (RAMs), read only memory (ROM), a cable or wireless signal containing a bit stream and the like, may also be used in the exemplary operating environment.</p>
    <p id="p-0020" num="0019">
        To enable user interaction with the computing device
        <b>100</b>
        , an input device
        <b>190</b>
        represents any number of input mechanisms, such as a microphone for speech, a touch-sensitive screen for gesture or graphical input, keyboard, mouse, motion input, speech and so forth. The input may be used by the presenter to indicate the beginning of a speech search query. The output device
        <b>170</b>
        can also be one or more of a number of output mechanisms known to those of skill in the art. In some instances, multimodal systems enable a user to provide multiple types of input to communicate with the computing device
        <b>100</b>
        . The communications interface
        <b>180</b>
        generally governs and manages the user input and system output. There is no restriction on the invention operating on any particular hardware arrangement and therefore the basic features here may easily be substituted for improved hardware or firmware arrangements as they are developed.
    </p>
    <p id="p-0021" num="0020">
        For clarity of explanation, the illustrative system embodiment is presented as comprising individual functional blocks (including functional blocks labeled as a “processor”). The functions these blocks represent may be provided through the use of either shared or dedicated hardware, including, but not limited to, hardware capable of executing software and hardware, such as a processor, that is purpose-built to operate as an equivalent to software executing on a general purpose processor. For example the functions of one or more processors presented in
        <figref idrefs="DRAWINGS">FIG. 1</figref>
        may be provided by a single shared processor or multiple processors. (Use of the term “processor” should not be construed to refer exclusively to hardware capable of executing software.) Illustrative embodiments may comprise microprocessor and/or digital signal processor (DSP) hardware, read-only memory (ROM) for storing software performing the operations discussed below, and random access memory (RAM) for storing results. Very large scale integration (VLSI) hardware embodiments, as well as custom VLSI circuitry in combination with a general purpose DSP circuit, may also be provided.
    </p>
    <p id="p-0022" num="0021">
        The logical operations of the various embodiments are implemented as: (1) a sequence of computer implemented steps, operations, or procedures running on a programmable circuit within a general use computer, (2) a sequence of computer implemented steps, operations, or procedures running on a specific-use programmable circuit; and/or (
        <b>3</b>
        ) interconnected machine modules or program engines within the programmable circuits.
    </p>
    <p id="p-0023" num="0022">
        Having disclosed some fundamental system components, the disclosure turns to the example method embodiment as illustrated in
        <figref idrefs="DRAWINGS">FIG. 2</figref>
        . For simplicity,
        <figref idrefs="DRAWINGS">FIG. 2</figref>
        is discussed in terms of a system configured to practice the method by translating a source signal into a target language using dialog act tags.
    </p>
    <p id="p-0024" num="0023">
        The system first receives a source speech signal (
        <b>202</b>
        ). The system accepts a source speech signal as a telephone call, as a digitally encoded file, an analog audio stream, a set of packets such as a Voice over IP (VoIP) call, or other actual speech signal or representation of a speech signal. The speech signal can be altered or preprocessed such as by a noise removal process. The speech signal may be part of a larger user-system natural language dialog.
    </p>
    <p id="p-0025" num="0024">
        The system tags dialog acts associated with the received source speech signal using a classification model (such as a maximum entropy model), dialog acts being domain independent descriptions of an intended action a speaker carries out by uttering the source speech signal (
        <b>204</b>
        ). The principle of maximum entropy states that when only partial information about the probabilities of possible outcomes is available, the system should choose probabilities so as to maximize the uncertainty about the missing information. In other words, since entropy is a measure of randomness, the system should choose the most random distribution subject to applicable constraints. A dialog act is a domain independent description of the action a person carries out by uttering a clause. A sample dialog act taxonomy is shown in
        <figref idrefs="DRAWINGS">FIG. 5</figref>
        . Different dialog systems can categorize, store, and treat dialog acts differently. Some broad categories of dialog acts tags are yes/no question, statement, command, and who/what/when/where/why question. Following is an example dialog between two individuals including dialog act tags in parentheses: P1: “Hello.” (greeting_semi_formal). P2: “Hi.” (greeting_informal). P1: “Do you know where the gym is?” (yes_no_question, question_about_location). P2: “Yes, the gym is three blocks south of here on the corner of Fun Street and Fly Street.” (positive_response, location_information). P1: “Thank you very much.” (gratitude). P2: “No problem. Good luck!” (valediction, polite_good_wishes).
    </p>
    <p id="p-0026" num="0025">The system can group tags into sets reflecting the general category of dialog act rather than specific tags, such as statement, acknowledgement, abandoned, agreement, question, appreciation, and “other”. The system can annotate tagged dialog acts to add commentary, notes, or explanations describing the dialog acts, the context giving rise to the dialog acts, the reasoning behind a classification of the dialog act into a particular set, or what categories of dialog act to expect in response.</p>
    <p id="p-0027" num="0026">
        The system produces an enriched hypothesis of the source speech signal incorporating the tagged dialog acts (
        <b>206</b>
        ). As an example, when translating a source speech signal enriched with a dialog act tag indicating that the source is a question from English to Russian, because the source is English, the question is phrased in a very word order specific way. When translating to Russian, the word order is not as important as the indication that the source speech signal is a question. The system can rearrange word order so the translation sounds more “native” in Russian, while still retaining the quality and intonation of being a question. For example, an unmodified, word-for-word translation of the English sentence “We don't need your help” sounds incorrect in Russian. A proper translation, “
        <img alt="custom-character" file="US20130151232A1-20130613-P00001.TIF" he="3.13mm" id="CUSTOM-CHARACTER-00001" img-content="character" img-format="tif" inline="no" orientation="portrait" wi="8.47mm"/>
        <img alt="custom-character" file="US20130151232A1-20130613-P00002.TIF" he="2.46mm" id="CUSTOM-CHARACTER-00002" img-content="character" img-format="tif" inline="no" orientation="portrait" wi="12.36mm"/>
        <img alt="custom-character" file="US20130151232A1-20130613-P00003.TIF" he="2.46mm" id="CUSTOM-CHARACTER-00003" img-content="character" img-format="tif" inline="no" orientation="portrait" wi="6.01mm"/>
        <img alt="custom-character" file="US20130151232A1-20130613-P00004.TIF" he="2.46mm" id="CUSTOM-CHARACTER-00004" img-content="character" img-format="tif" inline="no" orientation="portrait" wi="3.56mm"/>
        <img alt="custom-character" file="US20130151232A1-20130613-P00005.TIF" he="2.79mm" id="CUSTOM-CHARACTER-00005" img-content="character" img-format="tif" inline="no" orientation="portrait" wi="9.48mm"/>
        ” (word-for-word “your help to us not necessary”), is in a different word order entirely. Dialog act tags can function as a type of metadata to retain meaning, quality, and intonation. Similar issues arise in different languages with different word orders, grammars, and intonations for various dialog acts. Then the system outputs a version of the enriched hypothesis translated into a target language (
        <b>208</b>
        ).
    </p>
    <p id="p-0028" num="0027">When the source speech signal is a dialog turn having multiple sentences, the system reacts in a similar manner to that described above. The system can segment the source speech signal, tag dialog acts in each segment using a maximum entropy model, and produce an enriched translation of each segment in a target language incorporated the dialog act tags.</p>
    <p id="p-0029" num="0028">
        <figref idrefs="DRAWINGS">FIG. 3</figref>
        illustrates an example system for translating speech using dialog act tags
        <b>300</b>
        . A dialog act tagger
        <b>304</b>
        accepts an incoming speech signal
        <b>302</b>
        . If the tagger
        <b>304</b>
        realizes that the speech signal has multiple sentences or multiple dialog acts, a speech segmenter
        <b>306</b>
        splits the speech into discrete sentences or into discrete dialog acts. The tagger
        <b>304</b>
        then analyzes each sentence or dialog act and can classify them into sets of tags
        <b>308</b>
        based on the categories described above, such as statement, acknowledgement, abandoned, agreement, question, appreciation, etc. The tagger
        <b>304</b>
        outputs enriched, dialog-act-tagged speech
        <b>310</b>
        , sending it to a translation module
        <b>312</b>
        capable of understanding and incorporating the additional dialog act tag enriched speech
        <b>310</b>
        . A phrase translation table
        <b>314</b>
        can assist the translation module
        <b>312</b>
        in translating the enriched speech
        <b>310</b>
        . Further, dialog act specific translation models
        <b>316</b>
        can generate hypotheses that are more accurate with sufficient training data than without the use of dialog acts. The translation module
        <b>312</b>
        then converts the enriched speech
        <b>310</b>
        to enriched translated speech
        <b>318</b>
        in a language other than the original speech signal
        <b>302</b>
        . For example, the original speech signal
        <b>302</b>
        can be French and the translation module
        <b>312</b>
        can output the enriched translated speech
        <b>318</b>
        in Hindi. In one example, not shown, a single dialog act tagger
        <b>304</b>
        connects to multiple translation modules
        <b>312</b>
        , each capable of translating into a different language. In another example, a single translation module contains multiple plug-in modules which translate the speech signal
        <b>302</b>
        to multiple different languages. The system can output actual speech
        <b>318</b>
        or the system can output a set of instructions for reproducing speech, such as a lossless or lossy digital audio file or a Speech Synthesis Markup Language (SSML) file.
    </p>
    <p id="p-0030" num="0029">The system can directly exploit dialog act tags in statistical speech translation. At least two speech translation frameworks exist for exploiting dialog act tags. One is a standard phrase based statistical translation system and a second is a global lexical selection and reordering approach based on translating the source utterance into a bag-of-words (BOW). A maximum entropy dialog act tagger provides dialog act tags in accordance with the principles described herein. The dialog act tagger can be trained using a speech corpus such as the Switchboard Dialog Act Markup in Several Layers (DAMSL) corpus. The framework described herein is particularly suited for human-human and human-computer interactions in a dialog setting, where a correct understanding and application of an appropriate dialog act can compensate to some extent for information loss due to erroneous content. The system can use dialog acts to impart correct utterance level intonation during speech synthesis in the target language.</p>
    <p id="p-0031" num="0030">
        <figref idrefs="DRAWINGS">FIG. 4</figref>
        illustrates an example of speech translation output enriched with a dialog act.
        <figref idrefs="DRAWINGS">FIG. 4</figref>
        shows one example situation where the detection and transfer of dialog act information is beneficial. The source is an Arabic language sentence asking “Is this a painkiller?”. The English language reference, or optimal translation, is “Is this a painkiller?” but the direct English translation hypothesis is “This is a painkiller.” The direct translation hypothesis is a literal word for word translation and loses the sense of asking a question in English. An enriched hypothesis provides the text “this is a painkiller” and tags the phrase as a dialog act of a “Yes-No-Question”. The system can incorporate dialog act tags in an enriched hypothesis to add appropriate intonation and change word order in the English language output to sound like a question. Under ideal circumstances, the system outputs a question in English that substantially matches the reference translation, “Is this a painkiller?”
    </p>
    <p id="p-0032" num="0031">
        Before describing the dialog act qualification process, more explanation and examples of dialog acts are provided. A dialog act is a domain independent description of the action a person carries out by uttering a clause.
        <figref idrefs="DRAWINGS">FIG. 5</figref>
        shows a taxonomical hierarchy of dialog acts
        <b>500</b>
        that people perform while interacting with machines. At the top level, a clause, DIALOG_ACT
        <b>502</b>
        , can be classified either as INFORMATION
        <b>506</b>
        (those that simply provide some information) or as REQUEST
        <b>504</b>
        (those that request for some information or some action to be done). These top-level dialog acts can be further sub-classified as shown in
        <figref idrefs="DRAWINGS">FIG. 5</figref>
        . For example, the dialog act of REQUEST
        <b>504</b>
        may have sub-classifications
        <b>508</b>
        . The sub-classifications
        <b>508</b>
        can include a WH_QUESTION DA with further sub-classifications
        <b>512</b>
        , such as WHO, WHAT, WHERE, WHEN, WHY, HOW types of “WH” questions. Other sub-classifications are shown for a REQUEST
        <b>504</b>
        DA, such as a YES-NO-QUESTION and an IMPERATIVE DA.
        <figref idrefs="DRAWINGS">FIG. 5</figref>
        further shows another layer of sub-classifications
        <b>514</b>
        for IMPERATIVE as REQ_ACT, CANCEL, CHECK, TALK_TO_AGENT, and CHANGE
        <b>514</b>
        . Of these, the REQ-ACT DA is the most general one and is assigned to the clauses embodying a request for an action to be performed. Others are special cases of REQ_ACT where the actions requested are to cancel something, check for something, transfer to an agent, and to change something, etc.
    </p>
    <p id="p-0033" num="0032">
        Similarly, the INFORMATION DA
        <b>506</b>
        in
        <figref idrefs="DRAWINGS">FIG. 5</figref>
        can also have sub-classifications
        <b>510</b>
        , such as STATEMENT, SOCIAL_PHOTO, RESPONSE, and SELF-TALK. The STATEMENT DA is assigned to clauses where a person simply makes a statement, i.e., is not asking a question or requesting an action to be performed. Such statements could be either simple assertion of facts or contain some kind of modality. Accordingly STATEMENT DA can be sub-classified as ASSERTION or MODAL. The sub-hierarchy shown as
        <b>516</b>
        sub-classifies the ASSERTION DA. For example e.g. a person may make an assertion that she want to do something or wants to find out some things. These DA are labeled as WANT-TO-DO and WANT-INFORMATION, as shown in
        <b>516</b>
        . The sub-hierarchy shown as
        <b>518</b>
        further refines the MODAL sub-classification of STATEMENT
        <b>510</b>
        , where a person may predict something for the future: WILL_BE, or the person may state something that happened in the past WAS, or the person may express a desire: WISH, or a belief: BELIEVE. The SOCIAL_PHOTO DA is assigned to phrases uttered to perform some social protocol. The sub-hierarchy shown as
        <b>520</b>
        further refines the SOCIAL_PHOTO DA. The RESPONSE DA is assigned to phrases uttered to respond to a question. The sub-hierarchy shown as
        <b>522</b>
        further refines the RESPONSE DA.
    </p>
    <p id="p-0034" num="0033">
        The exemplary dialog act taxonomy shown in
        <figref idrefs="DRAWINGS">FIG. 5</figref>
        is provided for illustrative purposes only. The system can add other dialog acts to this taxonomy, refine it even further, or select a restricted set of dialog acts from this taxonomy itself depending on the level of understanding desired. For example the shaded nodes in
        <figref idrefs="DRAWINGS">FIG. 5</figref>
        show a possible set of dialog acts that a specific dialog system may wish to identify.
    </p>
    <p id="p-0035" num="0034">The system can associate dialog acts (DA) with each clause to assist in understanding user utterances qualifiedly. For example, the DA of WANT-INFORMATION can be qualified with the description of information desired; IMPERATIVE can be qualified with the action that is ordered. While dialog acts are domain-independent, their qualification involves domain-dependent objects and actions referred to in the clause. For example, the clause “Can you tell me where Zardoz is playing?” contains a dialog act of type WHERE indicating to the DM that it should find out the place associated with something. A dialog manager however also needs further qualification of the WHERE DA in that it must know the domain-dependent thing whose place it must find out. In this example, it is “playing Zardoz.” For more information and a more detailed discussion of dialog acts, see related U.S. Patent Application 20030130841.</p>
    <p id="p-0036" num="0035">
        The system uses a maximum entropy sequence tagging model for automatic dialog act tagging. The prediction problem can be modeled as a classification task: given a sequence of utterances U=u
        <sub>1</sub>
        , u
        <sub>2</sub>
        , . . . , u
        <sub>n </sub>
        and a dialog act vocabulary (d
        <sub>i</sub>
        εD, |D|=K), the system must predict the best dialog act sequence D*=d
        <sub>1</sub>
        , d
        <sub>2</sub>
        , . . . , d
        <sub>n</sub>
        . The classifier assigns to each utterance a dialog act label conditioned on a vector of local contextual feature vectors comprising the lexical, syntactic and acoustic information.
    </p>
    <p id="p-0037" num="0036">
        The general problem of enriched statistical speech-to-speech translation can be summarized as follows. S
        <sub>s</sub>
        , T
        <sub>s </sub>
        and S
        <sub>t</sub>
        , T
        <sub>t </sub>
        are the speech signals and equivalent textual transcription in the source and target language, and L
        <sub>s </sub>
        is the enriched representation for the source speech. The speech to speech translation approach described herein can be formalized as follows:
    </p>
    <p id="p-0038" num="0000">
        <maths id="MATH-US-00001" num="00001">
            <math overflow="scroll">
                <mtable>
                    <mtr>
                        <mtd>
                            <mrow>
                                <msubsup>
                                    <mi>S</mi>
                                    <mi>t</mi>
                                    <mo>*</mo>
                                </msubsup>
                                <mo>=</mo>
                                <mrow>
                                    <munder>
                                        <mrow>
                                            <mi>arg</mi>
                                            <mo></mo>
                                            <mstyle>
                                                <mspace height="0.3ex" width="0.3em"/>
                                            </mstyle>
                                            <mo></mo>
                                            <mi>max</mi>
                                        </mrow>
                                        <msub>
                                            <mi>S</mi>
                                            <mi>t</mi>
                                        </msub>
                                    </munder>
                                    <mo></mo>
                                    <mrow>
                                        <mi>P</mi>
                                        <mo></mo>
                                        <mrow>
                                            <mo>(</mo>
                                            <mrow>
                                                <msub>
                                                    <mi>S</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mo>|</mo>
                                                <msub>
                                                    <mi>S</mi>
                                                    <mi>s</mi>
                                                </msub>
                                            </mrow>
                                            <mo>)</mo>
                                        </mrow>
                                    </mrow>
                                </mrow>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mi>Equation</mi>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="0.8ex" width="0.8em"/>
                                </mstyle>
                                <mo></mo>
                                <mn>1</mn>
                            </mrow>
                        </mtd>
                    </mtr>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mrow>
                                    <mi>P</mi>
                                    <mo></mo>
                                    <mrow>
                                        <mo>(</mo>
                                        <mrow>
                                            <msub>
                                                <mi>S</mi>
                                                <mi>t</mi>
                                            </msub>
                                            <mo>|</mo>
                                            <msub>
                                                <mi>S</mi>
                                                <mi>s</mi>
                                            </msub>
                                        </mrow>
                                        <mo>)</mo>
                                    </mrow>
                                </mrow>
                                <mo>=</mo>
                                <mrow>
                                    <munderover>
                                        <mo>∑</mo>
                                        <mrow>
                                            <msub>
                                                <mi>T</mi>
                                                <mi>t</mi>
                                            </msub>
                                            <mo>,</mo>
                                            <msub>
                                                <mi>T</mi>
                                                <mi>s</mi>
                                            </msub>
                                            <mo>,</mo>
                                            <msub>
                                                <mi>L</mi>
                                                <mi>s</mi>
                                            </msub>
                                        </mrow>
                                        <msub>
                                            <mi>S</mi>
                                            <mi>t</mi>
                                        </msub>
                                    </munderover>
                                    <mo></mo>
                                    <mrow>
                                        <mi>P</mi>
                                        <mo></mo>
                                        <mrow>
                                            <mo>(</mo>
                                            <mrow>
                                                <msub>
                                                    <mi>S</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mo>,</mo>
                                                <msub>
                                                    <mi>T</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mo>,</mo>
                                                <msub>
                                                    <mi>T</mi>
                                                    <mi>s</mi>
                                                </msub>
                                                <mo>,</mo>
                                                <mrow>
                                                    <msub>
                                                        <mi>L</mi>
                                                        <mi>s</mi>
                                                    </msub>
                                                    <mo>|</mo>
                                                    <msub>
                                                        <mi>S</mi>
                                                        <mi>s</mi>
                                                    </msub>
                                                </mrow>
                                            </mrow>
                                            <mo>)</mo>
                                        </mrow>
                                    </mrow>
                                </mrow>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mi>Equation</mi>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="0.8ex" width="0.8em"/>
                                </mstyle>
                                <mo></mo>
                                <mn>2</mn>
                            </mrow>
                        </mtd>
                    </mtr>
                    <mtr>
                        <mtd>
                            <mrow>
                                <mo>≈</mo>
                                <mrow>
                                    <munderover>
                                        <mo>∑</mo>
                                        <mrow>
                                            <msub>
                                                <mi>T</mi>
                                                <mi>t</mi>
                                            </msub>
                                            <mo>,</mo>
                                            <msub>
                                                <mi>T</mi>
                                                <mi>s</mi>
                                            </msub>
                                            <mo>,</mo>
                                            <msub>
                                                <mi>L</mi>
                                                <mi>s</mi>
                                            </msub>
                                        </mrow>
                                        <mstyle>
                                            <mspace height="0.3ex" width="0.3em"/>
                                        </mstyle>
                                    </munderover>
                                    <mo></mo>
                                    <mrow>
                                        <mrow>
                                            <mi>P</mi>
                                            <mo></mo>
                                            <mrow>
                                                <mo>(</mo>
                                                <mrow>
                                                    <mrow>
                                                        <msub>
                                                            <mi>S</mi>
                                                            <mi>t</mi>
                                                        </msub>
                                                        <mo>|</mo>
                                                        <msub>
                                                            <mi>T</mi>
                                                            <mi>t</mi>
                                                        </msub>
                                                    </mrow>
                                                    <mo>,</mo>
                                                    <msub>
                                                        <mi>L</mi>
                                                        <mi>s</mi>
                                                    </msub>
                                                </mrow>
                                                <mo>)</mo>
                                            </mrow>
                                        </mrow>
                                        <mo>·</mo>
                                        <mrow>
                                            <mi>P</mi>
                                            <mo></mo>
                                            <mrow>
                                                <mo>(</mo>
                                                <mrow>
                                                    <msub>
                                                        <mi>T</mi>
                                                        <mi>t</mi>
                                                    </msub>
                                                    <mo>,</mo>
                                                    <msub>
                                                        <mi>T</mi>
                                                        <mi>s</mi>
                                                    </msub>
                                                    <mo>,</mo>
                                                    <mrow>
                                                        <msub>
                                                            <mi>L</mi>
                                                            <mi>s</mi>
                                                        </msub>
                                                        <mo>|</mo>
                                                        <msub>
                                                            <mi>S</mi>
                                                            <mi>s</mi>
                                                        </msub>
                                                    </mrow>
                                                </mrow>
                                                <mo>)</mo>
                                            </mrow>
                                        </mrow>
                                    </mrow>
                                </mrow>
                            </mrow>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mi>Equation</mi>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="0.8ex" width="0.8em"/>
                                </mstyle>
                                <mo></mo>
                                <mn>3</mn>
                            </mrow>
                        </mtd>
                    </mtr>
                </mtable>
            </math>
        </maths>
    </p>
    <p id="p-0039" num="0037">where conditional independence assumptions allow for Equation 3. Even though the system can perform recognition and translation jointly, typical speech-to-speech translation frameworks compartmentalize the automatic speech recognition, machine translation, and text to speech (TTS) and individually maximize each component for performance as follows:</p>
    <p id="p-0040" num="0000">
        <maths id="MATH-US-00002" num="00002">
            <math overflow="scroll">
                <mtable>
                    <mtr>
                        <mtd>
                            <mtable>
                                <mtr>
                                    <mtd>
                                        <mrow>
                                            <msubsup>
                                                <mi>s</mi>
                                                <mi>t</mi>
                                                <mo>*</mo>
                                            </msubsup>
                                            <mo>=</mo>
                                            <mi/>
                                            <mo></mo>
                                            <mrow>
                                                <munder>
                                                    <mrow>
                                                        <mi>arg</mi>
                                                        <mo></mo>
                                                        <mstyle>
                                                            <mspace height="0.3ex" width="0.3em"/>
                                                        </mstyle>
                                                        <mo></mo>
                                                        <mi>max</mi>
                                                    </mrow>
                                                    <msub>
                                                        <mi>S</mi>
                                                        <mi>t</mi>
                                                    </msub>
                                                </munder>
                                                <mo></mo>
                                                <mrow>
                                                    <mi>P</mi>
                                                    <mo></mo>
                                                    <mrow>
                                                        <mo>(</mo>
                                                        <mrow>
                                                            <msub>
                                                                <mi>S</mi>
                                                                <mi>t</mi>
                                                            </msub>
                                                            <mo>|</mo>
                                                            <msub>
                                                                <mi>S</mi>
                                                                <mi>s</mi>
                                                            </msub>
                                                        </mrow>
                                                        <mo>)</mo>
                                                    </mrow>
                                                </mrow>
                                            </mrow>
                                        </mrow>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mrow>
                                            <mo>≈</mo>
                                            <mi/>
                                            <mo></mo>
                                            <mrow>
                                                <munder>
                                                    <mrow>
                                                        <mi>arg</mi>
                                                        <mo></mo>
                                                        <mstyle>
                                                            <mspace height="0.3ex" width="0.3em"/>
                                                        </mstyle>
                                                        <mo></mo>
                                                        <mi>max</mi>
                                                    </mrow>
                                                    <msub>
                                                        <mi>S</mi>
                                                        <mi>t</mi>
                                                    </msub>
                                                </munder>
                                                <mo></mo>
                                                <mrow>
                                                    <mi>P</mi>
                                                    <mo></mo>
                                                    <mrow>
                                                        <mo>(</mo>
                                                        <mrow>
                                                            <mrow>
                                                                <msub>
                                                                    <mi>S</mi>
                                                                    <mi>t</mi>
                                                                </msub>
                                                                <mo>|</mo>
                                                                <msubsup>
                                                                    <mi>T</mi>
                                                                    <mi>t</mi>
                                                                    <mo>*</mo>
                                                                </msubsup>
                                                            </mrow>
                                                            <mo>,</mo>
                                                            <msubsup>
                                                                <mi>L</mi>
                                                                <mi>s</mi>
                                                                <mo>*</mo>
                                                            </msubsup>
                                                        </mrow>
                                                        <mo>)</mo>
                                                    </mrow>
                                                </mrow>
                                                <mo>×</mo>
                                                <munder>
                                                    <mrow>
                                                        <mi>arg</mi>
                                                        <mo></mo>
                                                        <mstyle>
                                                            <mspace height="0.3ex" width="0.3em"/>
                                                        </mstyle>
                                                        <mo></mo>
                                                        <mi>max</mi>
                                                    </mrow>
                                                    <msub>
                                                        <mi>T</mi>
                                                        <mi>t</mi>
                                                    </msub>
                                                </munder>
                                                <mo></mo>
                                                <mrow>
                                                    <mi>P</mi>
                                                    <mo></mo>
                                                    <mrow>
                                                        <mo>(</mo>
                                                        <mrow>
                                                            <mrow>
                                                                <msub>
                                                                    <mi>T</mi>
                                                                    <mi>t</mi>
                                                                </msub>
                                                                <mo>|</mo>
                                                                <msubsup>
                                                                    <mi>T</mi>
                                                                    <mi>s</mi>
                                                                    <mo>*</mo>
                                                                </msubsup>
                                                            </mrow>
                                                            <mo>,</mo>
                                                            <msubsup>
                                                                <mi>L</mi>
                                                                <mi>s</mi>
                                                                <mo>*</mo>
                                                            </msubsup>
                                                        </mrow>
                                                        <mo>)</mo>
                                                    </mrow>
                                                </mrow>
                                                <mo>×</mo>
                                            </mrow>
                                        </mrow>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mrow>
                                            <mi/>
                                            <mo></mo>
                                            <mrow>
                                                <munder>
                                                    <mrow>
                                                        <mi>arg</mi>
                                                        <mo></mo>
                                                        <mstyle>
                                                            <mspace height="0.3ex" width="0.3em"/>
                                                        </mstyle>
                                                        <mo></mo>
                                                        <mi>max</mi>
                                                    </mrow>
                                                    <msub>
                                                        <mi>L</mi>
                                                        <mi>s</mi>
                                                    </msub>
                                                </munder>
                                                <mo></mo>
                                                <mrow>
                                                    <mi>P</mi>
                                                    <mo></mo>
                                                    <mrow>
                                                        <mo>(</mo>
                                                        <mrow>
                                                            <mrow>
                                                                <msub>
                                                                    <mi>L</mi>
                                                                    <mi>s</mi>
                                                                </msub>
                                                                <mo>|</mo>
                                                                <msubsup>
                                                                    <mi>T</mi>
                                                                    <mi>s</mi>
                                                                    <mo>*</mo>
                                                                </msubsup>
                                                            </mrow>
                                                            <mo>,</mo>
                                                            <msub>
                                                                <mi>S</mi>
                                                                <mi>s</mi>
                                                            </msub>
                                                        </mrow>
                                                        <mo>)</mo>
                                                    </mrow>
                                                </mrow>
                                                <mo>×</mo>
                                                <munder>
                                                    <mrow>
                                                        <mi>arg</mi>
                                                        <mo></mo>
                                                        <mstyle>
                                                            <mspace height="0.3ex" width="0.3em"/>
                                                        </mstyle>
                                                        <mo></mo>
                                                        <mi>max</mi>
                                                    </mrow>
                                                    <msub>
                                                        <mi>T</mi>
                                                        <mi>s</mi>
                                                    </msub>
                                                </munder>
                                                <mo></mo>
                                                <mrow>
                                                    <mi>P</mi>
                                                    <mo></mo>
                                                    <mrow>
                                                        <mo>(</mo>
                                                        <mrow>
                                                            <msub>
                                                                <mi>T</mi>
                                                                <mi>s</mi>
                                                            </msub>
                                                            <mo>|</mo>
                                                            <msub>
                                                                <mi>S</mi>
                                                                <mi>s</mi>
                                                            </msub>
                                                        </mrow>
                                                        <mo>)</mo>
                                                    </mrow>
                                                </mrow>
                                            </mrow>
                                        </mrow>
                                    </mtd>
                                </mtr>
                            </mtable>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mi>Equation</mi>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="0.8ex" width="0.8em"/>
                                </mstyle>
                                <mo></mo>
                                <mn>4</mn>
                            </mrow>
                        </mtd>
                    </mtr>
                </mtable>
            </math>
        </maths>
    </p>
    <p id="p-0041" num="0038">
        where T
        <sub>s</sub>
        *, T
        <sub>t</sub>
        *, and S
        <sub>t</sub>
        * are the arguments maximizing each of the individual components in the translation engine. L
        <sub>s</sub>
        * is the rich annotation detected from the source speech signal and text, S
        <sub>s </sub>
        and T
        <sub>s</sub>
        * respectively. The principles described herein can be combined with nearly any speech synthesis component. The rich annotations (L
        <sub>s</sub>
        ) can include one or more of syntactic or semantic concepts, prosody, and dialog act tags.
    </p>
    <p id="p-0042" num="0039">
        One scheme for statistical translation is the phrase based approach. An example phrase based approach obtains word-level alignments from a bilingual corpus using tools such as GIZA++ and extracts phrase translation pairs from the bilingual word alignment using heuristics. If the phrase based approach has access to source side dialog acts (L
        <sub>s</sub>
        ), the translation problem can be reformulated as:
    </p>
    <p id="p-0043" num="0000">
        <maths id="MATH-US-00003" num="00003">
            <math overflow="scroll">
                <mtable>
                    <mtr>
                        <mtd>
                            <mtable>
                                <mtr>
                                    <mtd>
                                        <mrow>
                                            <msubsup>
                                                <mi>T</mi>
                                                <mi>t</mi>
                                                <mo>*</mo>
                                            </msubsup>
                                            <mo>=</mo>
                                            <mi/>
                                            <mo></mo>
                                            <mrow>
                                                <munder>
                                                    <mrow>
                                                        <mi>arg</mi>
                                                        <mo></mo>
                                                        <mstyle>
                                                            <mspace height="0.3ex" width="0.3em"/>
                                                        </mstyle>
                                                        <mo></mo>
                                                        <mi>max</mi>
                                                    </mrow>
                                                    <msub>
                                                        <mi>T</mi>
                                                        <mi>t</mi>
                                                    </msub>
                                                </munder>
                                                <mo></mo>
                                                <mrow>
                                                    <mi>P</mi>
                                                    <mo></mo>
                                                    <mrow>
                                                        <mo>(</mo>
                                                        <mrow>
                                                            <mrow>
                                                                <msub>
                                                                    <mi>T</mi>
                                                                    <mi>t</mi>
                                                                </msub>
                                                                <mo>|</mo>
                                                                <msub>
                                                                    <mi>T</mi>
                                                                    <mi>s</mi>
                                                                </msub>
                                                            </mrow>
                                                            <mo>,</mo>
                                                            <msub>
                                                                <mi>L</mi>
                                                                <mi>s</mi>
                                                            </msub>
                                                        </mrow>
                                                        <mo>)</mo>
                                                    </mrow>
                                                </mrow>
                                            </mrow>
                                        </mrow>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mrow>
                                            <mo>=</mo>
                                            <mi/>
                                            <mo></mo>
                                            <mrow>
                                                <munder>
                                                    <mrow>
                                                        <mi>arg</mi>
                                                        <mo></mo>
                                                        <mstyle>
                                                            <mspace height="0.3ex" width="0.3em"/>
                                                        </mstyle>
                                                        <mo></mo>
                                                        <mi>max</mi>
                                                    </mrow>
                                                    <msub>
                                                        <mi>T</mi>
                                                        <mi>t</mi>
                                                    </msub>
                                                </munder>
                                                <mo></mo>
                                                <mrow>
                                                    <mrow>
                                                        <mi>P</mi>
                                                        <mo></mo>
                                                        <mrow>
                                                            <mo>(</mo>
                                                            <mrow>
                                                                <mrow>
                                                                    <msub>
                                                                        <mi>T</mi>
                                                                        <mi>s</mi>
                                                                    </msub>
                                                                    <mo>|</mo>
                                                                    <msub>
                                                                        <mi>T</mi>
                                                                        <mi>t</mi>
                                                                    </msub>
                                                                </mrow>
                                                                <mo>,</mo>
                                                                <msub>
                                                                    <mi>L</mi>
                                                                    <mi>s</mi>
                                                                </msub>
                                                            </mrow>
                                                            <mo>)</mo>
                                                        </mrow>
                                                    </mrow>
                                                    <mo>·</mo>
                                                    <mrow>
                                                        <mi>P</mi>
                                                        <mo></mo>
                                                        <mrow>
                                                            <mo>(</mo>
                                                            <mrow>
                                                                <msub>
                                                                    <mi>T</mi>
                                                                    <mi>t</mi>
                                                                </msub>
                                                                <mo>|</mo>
                                                                <msub>
                                                                    <mi>L</mi>
                                                                    <mi>s</mi>
                                                                </msub>
                                                            </mrow>
                                                            <mo>)</mo>
                                                        </mrow>
                                                    </mrow>
                                                </mrow>
                                            </mrow>
                                        </mrow>
                                    </mtd>
                                </mtr>
                            </mtable>
                        </mtd>
                        <mtd>
                            <mrow>
                                <mi>Equation</mi>
                                <mo></mo>
                                <mstyle>
                                    <mspace height="0.8ex" width="0.8em"/>
                                </mstyle>
                                <mo></mo>
                                <mn>5</mn>
                            </mrow>
                        </mtd>
                    </mtr>
                </mtable>
            </math>
        </maths>
    </p>
    <p id="p-0044" num="0040">The first term in Equation 5 corresponds to a dialog act specific machine translation model and the second term corresponds to a dialog act specific language model. Given a sufficient amount of training data, such a system can generate hypotheses that are more accurate than without the use of dialog acts.</p>
    <p id="p-0045" num="0041">
        The system can use a bag-of-words (BOW) approach for enriching translation which treats the target sentence as a BOW assigned to the source sentence and its corresponding dialog act tag. Given a source sentence and the dialog act tag, the objective is to estimate the probability of finding a given word in the target sentence. Because each word in the target vocabulary is detected independently, the system can use simple binary static classifiers. The classifier is trained with word n-grams and dialog act (BOW grams(T
        <sub>s</sub>
        ), L
        <sub>s</sub>
        ) from the source sentence T
        <sub>s</sub>
        . During decoding, the system considers words with conditional probability greater than a threshold Θ as the result of lexical choice decoding. The system can train the BOW lexical choice model using a binary maximum entropy technique with L1-regularization as follows:
    </p>
    <p id="p-0046" num="0000">
        <br/>
        <?in-line-formulae description="In-line Formulae" end="lead"?>
        BOW
        <sub>T</sub>
        <sub>
            <sub2>t</sub2>
        </sub>
        <i>*={T</i>
        <sub>t</sub>
        <i>|P</i>
        (
        <i>T</i>
        <sub>t</sub>
        |BOWgrams(
        <i>T</i>
        <sub>s</sub>
        ),
        <i>L</i>
        <sub>s</sub>
        )&gt;Θ}  Equation 6
        <?in-line-formulae description="In-line Formulae" end="tail"?>
    </p>
    <p id="p-0047" num="0042">The system considers all permutations of words in BOW; and weights them according to a target language model in order to reconstruct the correct order of words in the target sentence. The system controls the length of target sentences by either varying the parameter Θ or adding optional deletion arcs to the final step of the decoding process.</p>
    <p id="p-0048" num="0043">The system can use dialog acts which are predictions from a maximum entropy based dialog act tagger. Even without reference dialog act tags, the system can still achieve modest improvements in translation quality. Improvements to the dialog act tagger and suitable adaptation techniques can further enhance system performance.</p>
    <p id="p-0049" num="0044">The principles described herein are particularly suited for translation scenarios that do not involve multiple sentences as part of a turn, such as lectures or parliamentary addresses. However, this is not a strict limitation of the proposed work. The system can appropriately handle translation scenarios having multiple sentences in each turn by segmenting the utterances before using the dialog act tagger. For example, the system can segment a long dialog turn into paragraphs, sentences, or phrases.</p>
    <p id="p-0050" num="0045">Embodiments within the scope of the present invention may also include computer-readable media for carrying or having computer-executable instructions or data structures stored thereon. Such computer-readable media can be any available media that can be accessed by a general purpose or special purpose computer, including the functional design of any special purpose processor as discussed above. By way of example, and not limitation, such computer-readable media can comprise RAM, ROM, EEPROM, CD-ROM or other optical disk storage, magnetic disk storage or other magnetic storage devices, or any other medium which can be used to carry or store desired program code means in the form of computer-executable instructions, data structures, or processor chip design. When information is transferred or provided over a network or another communications connection (either hardwired, wireless, or combination thereof) to a computer, the computer properly views the connection as a computer-readable medium. Thus, any such connection is properly termed a computer-readable medium. Combinations of the above should also be included within the scope of the computer-readable media.</p>
    <p id="p-0051" num="0046">Computer-executable instructions include, for example, instructions and data which cause a general purpose computer, special purpose computer, or special purpose processing device to perform a certain function or group of functions. Computer-executable instructions also include program modules that are executed by computers in stand-alone or network environments. Generally, program modules include routines, programs, objects, components, data structures, and functions inherent in the design of special-purpose processors that perform particular tasks or implement particular abstract data types. Computer-executable instructions, associated data structures, and program modules represent examples of the program code means for executing steps of the methods disclosed herein. The particular sequence of such executable instructions or associated data structures represents examples of corresponding acts for implementing the functions described in such steps.</p>
    <p id="p-0052" num="0047">Those of skill in the art will appreciate that other embodiments of the invention may be practiced in network computing environments with many types of computer system configurations, including personal computers, hand-held devices, multi-processor systems, microprocessor-based or programmable consumer electronics, network PCs, minicomputers, mainframe computers, and the like. Embodiments may also be practiced in distributed computing environments where tasks are performed by local and remote processing devices that are linked (either by hardwired links, wireless links, or by a combination thereof) through a communications network. In a distributed computing environment, program modules may be located in both local and remote memory storage devices.</p>
    <p id="p-0053" num="0048">The various embodiments described above are provided by way of illustration only and should not be construed to limit the invention. For example, the principles herein may be applied to speech interpretation using Voice over IP (VoIP), interpretation of live broadcast events, handheld natural language interpretation devices, etc. Those skilled in the art will readily recognize various modifications and changes that may be made to the present invention without following the example embodiments and applications illustrated and described herein, and without departing from the true spirit and scope of the present invention.</p>
    <?detailed-description description="Detailed Description" end="tail"?>
</description>
