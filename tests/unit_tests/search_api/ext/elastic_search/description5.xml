<description lang="EN" load-source="google" mxw-id="PDES215515936">
    <technical-field>
        <p>  The present invention relates to a color management system for performing color matching between image
            devices.
        </p>
    </technical-field>
    <background-art>
        <p>  Uniform color management between different image devices is called color management, and the mechanism for
            doing so is called color management system (CMS). Current CMS focuses on creating “physically equal colors”,
            and so-called device-independent CMS, which aims at handling device-independent colors, is the mainstream.
        </p>
        <p>  In the device-independent CMS, color information between two devices is transmitted through
            device-independent color values in order to perform color matching between the two devices. Therefore, it is
            necessary to predetermine the correspondence between individual device color spaces (RGB, CMYK, etc.) and
            device-independent color spaces such as XYZ tristimulus values and Lab values. Devices that comply with the
            standard must be used, or individual device characteristics must be measured with a colorimetric instrument.
            Therefore, the device-independent CMS has problems such as limited devices and the need for expensive
            colorimetric equipment.
        </p>
        <p>  In addition, the following problems have been pointed out for device-independent CMS. That is, even if
            light of the same colorimetric value can be output by the two displays, the chromatic adaptation state of
            the observer differs depending on the illumination environment, so the appearance of the same color cannot
            always be realized. Further, the colorimetric values are merely values defined by the color matching
            function of the standard observer and do not guarantee the appearance of the same color because the
            individual differences of the observers are not taken into consideration at all.
        </p>
        <p>Therefore, as a CMS taking into account differences in illumination environments and observer's chromatic
            adaptation, for example, in Patent Documents 1 and 2, from the measured or estimated spectral reflectance of
            the object surface and the estimated or assumed spectral intensity distribution of the illumination light
            source. A method for calculating and simulating the color observed under the illumination light has been
            proposed. Further, in Patent Document 3, von Kries and Naya et al. Introduce color adaptation prediction
            formulas into color conversion, and introduce a function of display observation elapsed time to cope with
            incomplete color adaptation (incomplete color constancy). In addition, it has been proposed to determine the
            color of the illumination light or the color adapted to the observer by the color of visual perception
            between the white color patch and the display. However, these methods do not take into account individual
            differences among observers.
        </p>
    </background-art>
    <citation-list>
        <patent-literature>
            <p>
                <patcit num="1">
                    <text>JP-A-8-317238</text>
                </patcit>
                <patcit num="2">
                    <text>JP 2003-235059 A</text>
                </patcit>
                <patcit num="3">
                    <text>JP-A-9-219800</text>
                </patcit>
            </p>
        </patent-literature>
    </citation-list>
    <?BRFSUM description="Brief Summary" end="lead"?>
    <summary-of-invention>
        <tech-problem>
            <p num="0007">  As described above, since all of the conventional methods must measure colorimetric values
                and spectral reflectances in advance, expensive measurement equipment is required and a certain level of
                expertise is required for the measurement. There is a problem that. In addition, the conventional method
                does not take into account individual differences among observers, and there is a problem that the same
                color appearance cannot be guaranteed for all observers. These points are the bottleneck in the spread
                of CMS (especially for general PC users).
            </p>
            <p num="0008">  The present invention has been made in view of the above circumstances, and the object of
                the present invention is a technique for realizing “appearance of equal colors” without depending on any
                of the observer, illumination light, and image device. Is to provide. A further object of the present
                invention is to provide a technique that allows anyone to easily calibrate an image device (setting
                parameters used for color conversion) without using a measuring instrument.
            </p>
        </tech-problem>
        <tech-solution>
            <p num="0009">  In order to achieve the above object, the color management system of the present invention
                uses one or a plurality of color charts (reflecting objects), and the characteristics of the image
                device and the observer characteristics (individuals) according to the visual color such as the color
                chart The gist is to determine parameters used for color conversion by grasping the difference and the
                color adaptation state to the lighting environment.
            </p>
            <p num="0010">  Here, the “color chart” is a color sample that is a target of color matching, and for
                example, a single color patch colored (printed) on a base material such as paper or resin can be used.
                The number of color charts to be used is arbitrary, but it is preferable to use three or more color
                charts because good color conversion accuracy can be obtained. Also, the color chart color is arbitrary,
                but chromatic colors are preferred over achromatic colors to obtain good color conversion accuracy, and
                when multiple color charts are used, the colors are selected so that the hue is not biased It is
                preferable to do. An “image device” is a device that inputs or outputs an image. Typically, the present
                invention can be suitably applied to color management between displays. However, in principle, the
                present invention can be applied to color management with other image devices (digital cameras, image
                scanners, etc.) where additive color mixing is established. However, the present invention is
                applicable.
            </p>
            <p num="0011">  Specifically, the color management system according to the present invention provides a
                second observation of a color observed under a first condition including a first observer, a first
                illumination light, and a first image device. A color management system for adjusting to a color
                observed under a second condition comprising a person, a second illumination light, and a second image
                device, wherein i is a predetermined number of i (i is an integer of 1 or more) A color chart, a storage
                unit that stores a second color value that is a value of an image signal for reproducing appearance of
                the same color as the color chart under the second condition, and the color chart and color A color
                adjustment image for comparison is output to the first image device, and the color adjustment image and
                the color adjustment image have the same color appearance under the first condition. A color adjustment
                image output unit for adjusting the color of the image by the first observer; and the first item. A
                color value acquisition unit that acquires, as a first color value, an image signal value of the color
                adjustment image when the color chart and the color appearance are the same below, and is acquired by
                the color value acquisition unit A parameter calculation unit that calculates a parameter used for color
                conversion from the first color value and the second color value stored in the storage unit; A color
                conversion unit that converts and outputs the converted image to the first image device.
            </p>
            <p num="0012">  The “color chart” serving as a reference and the “second color value” serving as a color
                matching target are given in advance. The “second color value” reproduces the same color appearance as
                the “color chart” under the “second condition” consisting of the “second observer, the second
                illumination light, and the second image device”. Image signal values for the second observer, the
                individual differences of the second observer, the chromatic adaptation state of the second observer
                under the second illumination light, and the device characteristics of the second image device
                Everything has a reflected value. As the second color value, a value actually obtained by an observation
                experiment under the second condition can be used, but a value obtained by calculation assuming the
                second condition can also be used. . The second color value data may be stored in the storage unit at
                the time of system setup, or may be provided via a network or an external recording medium.
            </p>
            <p num="0013">According to the system of the present invention, the user (= first observer) compares the
                color chart and the color adjustment image output to the first image device so that both appear to have
                the same color. By simply adjusting the color of the color adjustment image, the “first color value” can
                be obtained. This “first color value” is defined by the individual differences of the first observer,
                the color adaptation state of the first observer under the first illumination light, and the device
                characteristics of the first image device. Has a reflected value. Therefore, by obtaining a parameter
                used for color conversion from the first color value and the second color value, “equal color
                appearance” can be realized without depending on any of the observer, the illumination light, and the
                image device. Color conversion is possible.
            </p>
            <p num="0014">  In addition, since the user only has to adjust the color of the color adjustment image so
                that the color chart and the color adjustment image have the same appearance, there is no need for
                special measurement equipment. No special knowledge about color management is required, and anyone can
                easily calibrate image devices.
            </p>
            <p num="0015">  The system of the present invention further includes a gamma correction unit that
                gamma-corrects the image signal so that the value of the image signal and the output luminance of the
                first image device are approximately proportional to each of the color channels constituting the image
                signal. It is preferable to provide. For example, in the case of an RGB display, gamma correction is
                performed so that the output luminance changes linearly for each of the R channel, the G channel, and
                the B channel. Thus, by making the gamma characteristic of each color channel linear, an improvement in
                the accuracy of color conversion can be expected. In particular, the effect is remarkable when the color
                conversion is a linear mapping.
            </p>
            <p num="0016">  Moreover, it is preferable that the system of the present invention further includes a gamma
                value determining unit that determines a gamma value used in the gamma correction unit. For example, the
                gamma value determination unit includes a target image in which pixels of the maximum value Smax and
                pixels of the minimum value Smin of the color channel S are mixed at a ratio of α: 1−α (0 ≦ α ≦ 1). A
                target image output unit that outputs to the device, and outputs the single color image of the color
                channel S to the first image device, and the image signal of the single color image so that the
                luminance of the target image and the single color image are the same. Based on the correspondence
                between the monochromatic image output unit for adjusting the value of the first observer, the value of
                the image signal of the monochromatic image when the luminance is the same as that of the target image,
                and the ratio α. And a gamma value calculator for calculating the gamma value.
            </p>
            <p num="0017">  Since the color stimulus of the target image is mixed in the visual system due to the effect
                of juxtaposed additive color mixing, the relative luminance of the color stimulus of the target image
                with respect to the color stimulus having the maximum value Smax can be expressed by α. Therefore, the
                user (= first observer) can grasp the value of the image signal corresponding to the relative luminance
                α only by adjusting the luminance of the target image and the monochrome image to be the same, and gamma
                The value can be calculated. This gamma value also has a value reflecting all individual differences of
                the first observer, the chromatic adaptation state under the first illumination light, and the device
                characteristics of the first image device. Therefore, by performing gamma correction with this gamma
                value, further improvement in color conversion accuracy can be expected. In addition, the user only
                needs to adjust the value of the monochromatic image so that the brightness of the target image and the
                monochromatic image are the same, so there is no need for special measurement equipment and expertise in
                color management. Is also unnecessary.
            </p>
            <p num="0018">  The present invention can also be understood as a color management system provided with at
                least a part of the above means, or as a color management method provided with at least a part of the
                above processing. The present invention can also be understood as a color management program that causes
                a computer that outputs an image signal to the first image device to function as the above means, or a
                computer-readable recording medium that records the program.
            </p>
        </tech-solution>
        <advantageous-effects>
            <p num="0019">  ADVANTAGE OF THE INVENTION According to this invention, the technique for implement |
                achieving "the appearance of an equal color" without depending on any of an observer, illumination
                light, and an image device can be provided. Further, according to the present invention, anyone can
                easily calibrate an image device (setting parameters used for color conversion) without using a
                measuring instrument.
            </p>
        </advantageous-effects>
    </summary-of-invention>
    <?BRFSUM description="Brief Summary" end="tail"?>
    <description-of-drawings>
        <p>
            <figref num="1">The figure which shows the structure of a color management system typically.</figref>
            <figref num="2">The figure which shows the example which performs color conversion of conditions A, B, and C
                via the reference condition N.
            </figref>
            <figref num="3">The figure explaining the calculation method of a gamma value.</figref>
            <figref num="4">The figure explaining an experiment example.</figref>
        </p>
    </description-of-drawings>
    <description-of-embodiments>
        <p>  Preferred embodiments of the present invention will be exemplarily described in detail below with reference
            to the drawings. In this embodiment, color matching between two displays will be described as an example.
            However, the present invention can be applied to any image device (for example, a display, a digital camera,
            an image scanner, etc.) in which additive color mixing is established. It is applicable to color management.
        </p>
        <p>  First, after describing the principle of color management according to the present embodiment, a specific
            system configuration for realizing it will be described.
        </p>
        <p>(Principle of color management)
            <br/>
            The inventors pay attention to the “color constancy” of human vision in realizing color management that
            achieves “equal color appearance” independent of any of the observer, illumination light, and image device.
            . Color constancy refers to the property of perceiving the same color for the same reflective object even
            under illumination of different colors.
        </p>
        <p>Assume that there are two different conditions A and N, as shown in FIG. The first condition A is defined by
            “observer O       <sub>A</sub>       , illumination light I       <sub>A</sub>       , display D       <sub>
                A
            </sub>       ”, and the second condition N is defined by “observer O       <sub>N</sub>       , illumination
            light I       <sub>N</sub>       , display D       <sub>N</sub>       ”. Is a condition. Here, it is assumed
            that both observers O       <sub>A</sub>       and O       <sub>N</sub>       are observing the image 3
            displayed on the display under the respective conditions. Then, the color of the state of appearance when
            the observer       <sub>O A</sub>       below were observing the color       <sub>R</sub>
            <sub>A,</sub>       G A,       <sub>B A</sub>       of the display image 3 of the condition A, by using the
            3 × 3 matrix       <sub>m A,</sub>       Formula (1 ). Similarly, the color       <sub>R</sub>       N of
            the observer       <sub>O N</sub>       display image 3 under conditions       <sub>N,</sub>       G N, the
            color of the state of appearance when observing the       <sub>B N,</sub>       using the 3 × 3 matrix       <sub>
                m N,
            </sub>       wherein ( 2).
            <br/>
            <maths id="" num="1">
                <img file="**********.tif" he="48" id="000003" img-content="drawing" img-format="tif" wi="157"/>
            </maths>
            R, G, and B are the values of the R (red) channel, G (green) channel, and B (blue) channel of the display
            image 3, respectively. L ′, M ′, and S ′ are reaction strengths of the L cone, M cone, and S cone taking
            into account the observer's adaptation state (in order to show that the adaptation state is considered,
            Symbols L, M, and S have dashes.)
        </p>
        <p>Now, both observers O       <sub>A</sub>       and O       <sub>N</sub>       use color charts (color
            patches) 2 that are the same reflecting object, and color charts 2 based on visual sensation colors and
            display images 3 on the display under the respective conditions. Assume that color matching is performed. At
            this time, if it is assumed that perfect color constancy is established under both conditions A and N, the
            color appearance on the surface of the same reflective object (that is, color chart 2) becomes equal.
            <br/>
            <maths id="" num="2">
                <img file="**********.tif" he="21" id="000004" img-content="drawing" img-format="tif" wi="157"/>
            </maths>
            The following formula (3) can be obtained from the formulas (1) and (2).
            <br/>
            <maths id="" num="3">
                <img file="**********.tif" he="42" id="000005" img-content="drawing" img-format="tif" wi="157"/>
            </maths>
        </p>
        <p>
            <sub>A</sub>       transformation matrix corresponding to “m       <sub>A</sub>
            <sup>−1</sup>       m       <sub>N</sub>       ” in Equation (3) may be derived, and this transformation
            matrix may be used for color conversion. That is, when an image created under condition N is displayed and
            observed under condition A, the RGB values of the image are color-converted using a conversion matrix
            corresponding to “m       <sub>A</sub>
            <sup>−1</sup>       m       <sub>N</sub>       ”. displayed on the display       <sub>D a</sub>       above.
            Conversely, when an image created under condition A is displayed and observed under condition N, a
            transformation matrix corresponding to the inverse matrix “(m       <sub>A</sub>
            <sup>−1</sup>       m       <sub>N</sub>       )       <sup>−1</sup>       ” of the above transformation
            matrix is used. And color conversion may be performed. Thereby, realization of “equal color appearance”
            between the conditions A to N can be expected.
        </p>
        <p>Since the transformation matrix is composed of 9 parameters of 3 × 3, simply obtain a color matching result
            by visual sensation for three (three colors) color charts, and the simultaneous equations of Equation (3) By
            solving, a transformation matrix can be derived. However, the number of color charts is not limited to
            three, and if there is a color matching result with at least one color chart, a transformation matrix can be
            derived. When the number of color charts is less than three or more than three, a transformation matrix is
            derived by Equation (4) using a pseudo inverse matrix.
            <br/>
            M = pinv (C       <sub>N</sub>       ) · C       <sub>A</sub>       (4)
        </p>
        <p>In Expression (4), C       <sub>A</sub>       is an RGB value (3i × 1 matrix) that the observer has performed
            color matching with i color charts and visual equivalent colors under the condition A, and C       <sub>N
            </sub>       is the condition N Is an RGB value (3i × 9 matrix) obtained by color matching with i color
            charts and visual colors, and M is nine of a transformation matrix corresponding to m       <sub>A</sub>
            <sup>−1</sup>       m       <sub>N.</sub>       The parameters are arranged vertically (1 × 9 matrix). Also,
            pinv (C       <sub>N</sub>       ) represents a pseudo inverse matrix of C       <sub>N.</sub>
            <br/>
            <maths id="" num="4">
                <img file="**********.tif" he="67" id="000006" img-content="drawing" img-format="tif" wi="164"/>
            </maths>
        </p>
        <p>  As described above, the number i of color charts is arbitrary. However, as the number of color charts
            increases, an improvement in color conversion accuracy can be expected by reducing errors in color such as
            visual sensation. Therefore, i is preferably 3 or more. However, since the color matching work increases as
            the number of color charts increases, i is 20 or less, preferably 10 or less. When using a plurality of
            color charts, it is preferable to select ones having different hues. This is because by deriving a
            conversion matrix from the color matching results of various hues, it is possible to expect an improvement
            in color conversion accuracy as a whole color space.
        </p>
        <p>By the way, in general, there are innumerable “conditions” composed of a combination of an observer,
            illumination light, and an image device, and the above-described conversion matrix differs for each pair of
            conditions. However, deriving a transformation matrix for every pair of conditions is not practical both in
            terms of calculation cost and storage capacity for storing the transformation matrix. Therefore, as shown in
            FIG. 2, it is preferable that one condition N is a reference condition, and the other conditions A, B, C,.
            Then, by passing through the condition N, it is theoretically possible to perform color conversion between
            all pairs of conditions. For example, from the condition A to the condition B, direct color conversion can
            be performed using the combined conversion matrix “m       <sub>B</sub>
            <sup>−1</sup>       m       <sub>N</sub>       · (m       <sub>A</sub>
            <sup>−1</sup>       m       <sub>N</sub>       )       <sup>−1</sup>       ” as in the following equation. .
            <br/>
            <maths id="" num="5">
                <img file="**********.tif" he="21" id="000007" img-content="drawing" img-format="tif" wi="164"/>
            </maths>
        </p>
        <p>(Linearized RGB)
            <br/>
            In the color management of this embodiment described above, it is desirable that the gamma characteristics
            of the R, G, B color channels of the display are linear. Since the color conversion is a linear mapping of
            R, G, and B as shown in Equation (3), by making the gamma characteristics of R, G, and B linear, colors
            other than the color that is color-matched by the color chart This is because an improvement in color
            conversion accuracy can be expected. Here, the gamma characteristic of the R channel being linear means that
            when an R single color image is displayed on a display while changing the R value, the R value and the
            luminance of the single color image are substantially proportional.
        </p>
        <p>If the display originally has linear gamma characteristics, or if the display itself has a gamma adjustment
            function and linear gamma characteristics can be set for each color channel, they can be used as they are.
            In other cases, linear R, G, and B values may be obtained by individually performing gamma correction (also
            referred to as tone correction) on the R, G, and B values of the image signal. Below, a gamma value (in
            other words, a gamma characteristic of each color channel of the display) for obtaining a linearized RGB
            value is proposed by visual perception without using a measuring device.
        </p>
        <p>The linearized RGB value corrected by the luminance gamma value γ defined by the observer's relative
            visibility can be expressed by the following equation. S ′ on the left side (S is a symbol representing R,
            G, or B. The same applies hereinafter) is a linearized RGB value. S on the right side represents the RGB
            value of the original signal before gamma correction, Smax represents its maximum value, and γS represents
            the gamma value of the S channel.
            <br/>
            R ′ = (R / Rmax)       <sup>γR</sup>
            <br/>
            G ′ = (G / Gmax)       <sup>γG</sup>       (5)
            <br/>
            B ′ = (B / Bmax)       <sup>γB</sup>
        </p>
        <p>As shown in FIG. 3 (a), the gamma value γS is displayed in a state where a two-color checker stimulus serving
            as a target image and a single color image of the S channel are displayed on the display and the two-color
            checker stimulus is added and mixed. Derived from the result of adjusting the S value of the monochrome
            image with the color (luminance) as the target. The two-color checker stimulus is a grid-like image
            configured such that pixels of the maximum value Smax and pixels of the minimum value Smin of the S channel
            are mixed at a ratio of α: 1−α (0 ≦ α ≦ 1). (However, when α = 0 or 1, it is not a grid-like image but a
            monochromatic image of Smin or Smax). Since the two-color checker stimulus is mixed in the visual system due
            to the effect of the additive color mixture, the relative luminance of the two-color checker stimulus with
            respect to the color stimulus having the maximum value Smax is represented by α, which is the relative area
            of Smax, as shown in the following equation. Will be equal.
            <br/>
            α (Smax / Smax)       <sup>γS</sup>       + (1−α) (Smin / Smax)       <sup>γS</sup>       = α
        </p>
        <p>On the other hand, the relative luminance of the monochrome image is represented by (S / Smax)       <sup>
            γS
        </sup>       . Therefore, as a result of the matching operation based on the two-color checker stimulus and the
            visual sensation of the single-color image, the relative luminances of both are connected by an equation.
            <br/>
            α = (S / Smax)       <sup>γS</sup>
            <br/>
            Taking the logarithm of both sides,
            <br/>
            log α = γ S · log (S / Smax) (6)
            <br/>
            Thus, a gamma value exhibiting nonlinear characteristics can be handled by a linear problem.
        </p>
        <p>  Matching of two-color checker stimulus and single color image is performed for a plurality of α, and if
            these data are linearly approximated by the least square method passing through the origin as shown in FIG.
            The luminance gamma value γS defined by the relative luminous sensitivity of the person can be derived.
        </p>
        <p>Using the gamma values γR, γG, γB derived from the R, G, B color channels derived as described above, the
            gamma correction of the equation (5) is performed, thereby linearizing RGB values R ′, G ′, B ′. Is
            obtained. By performing such gamma correction on the displays D       <sub>A</sub>       and D       <sub>N
                under
            </sub>       the conditions A and N shown in FIG. 1 and linearizing the gamma characteristics of both
            displays in advance, the above-described conversion matrix is derived. Further improvement in the accuracy
            of color matching between conditions A and N can be expected. The color conversion formula in this case is
            expressed as the following formula using R ′, G ′, B ′ instead of R, G, B in formula (3).
            <br/>
            <maths id="" num="6">
                <img file="**********.tif" he="20" id="000008" img-content="drawing" img-format="tif" wi="164"/>
            </maths>
        </p>
        <p>Here, a grid-like two-color checker stimulus is used as the target image. However, if the image is composed
            of Smax pixels and Smin pixels and has the effect of juxtaposed additive color mixing,
            <br/>
            The target image having any pattern may be used without being limited to the lattice shape.
        </p>
        <p>(System configuration)
            <br/>
            Next, a specific configuration of the color management system for realizing the color management described
            above will be described with reference to FIG.
        </p>
        <p>The color management system (hereinafter also simply referred to as “system”) according to the present
            embodiment is roughly configured by a computer program and a physical color chart (color patch). The program
            is provided via a recording medium such as a CD-ROM or a computer network such as the Internet, and is
            installed in a computer connected to an image device to be subjected to color matching. That is, in the
            example of FIG 1, and realize a color management system 1 and the computer program that is connected to a
            display D       <sub>A</sub>       cooperate. The computer can be a general-purpose computer including a
            CPU, a main storage device (for example, a semiconductor memory), an auxiliary storage device (for example,
            a hard disk), an input device (for example, a keyboard and a pointing device), a network IF, and the like.
            The functions of the system 1 are a storage unit 10, a color adjustment image output unit 11, a color value
            acquisition unit 12, a parameter calculation unit 13, a color conversion unit 14, a gamma value
            determination unit 15, and a gamma correction unit 16, which are auxiliary storage devices. Is stored in the
            main storage device and executed by the CPU.
        </p>
        <p>  1 illustrates the configuration in which all the functions of the system 1 are implemented in one local
            computer. However, in the case of a client-server configuration in which a Web server is accessed by a Web
            browser, the functions of the system 1 are illustrated. Part or all of the above (for example, generation of
            a color adjustment image, calculation of a conversion matrix, etc.) may be performed on the server side. In
            the latter case, a color management system is constituted by a plurality of computers.
        </p>
        <p>&lt;Display gamma correction&gt;
            <br/>
            The gamma value determination unit 15 is a function that performs processing for determining a gamma value
            for obtaining the above-described linearized RGB as a pre-process for creating a conversion matrix and color
            conversion. First, the gamma value determination unit 15 displays the two-color checker stimulus and
            monochrome image for brightness adjustment as the target image on the display D       <sub>A</sub>       as
            shown in FIG. 3 (a). Observer O       <sub>A</sub>       is, entering the RGB value or HSV value using an
            input device such as a keyboard, a gamma value determining section 15 changes the monochrome image color
            (luminance) in response to the input value. When the luminance of the observer O       <sub>A</sub>       two-color
            checker stimulation and monochromatic images presses a predetermined key when it is determined that it is
            the same, RGB values of the monochromatic image at that time it is fetched. After obtaining RGB value data
            for a plurality of α, the gamma value determination unit 15 calculates the gamma value according to Equation
            (6). The obtained gamma values γR, γG, γB are registered in the gamma correction unit 16. Gamma correction
            unit 16, the RGB values of the image signals to be displayed on the display       <sub>D A,</sub>       gamma
            correction using the gamma value [gamma] R, .GAMMA.g, the .gamma.B (see equation (5)).
        </p>
        <p>&lt;Creation of conversion matrix and color conversion&gt;
            <br/>
            The storage unit 10, data of RGB values to reproduce the appearance of the same color as the color chart
            under the condition N (second color value), data corresponding to the C       <sub>N</sub>       clogging
            formula (4), a storage Has been. This data (assuming or condition N) under conditions N previously created,
            and is provided to the viewer O       <sub>A</sub>       is the user of the system 1.
        </p>
        <p>Color adjustment image output unit 11 displays the color adjustment image for comparing the color chart and
            color display D       <sub>A.</sub>       Observer O       <sub>A</sub>       is, entering the RGB value or
            HSV value using an input device such as a keyboard, color adjustment image output unit 11, in response to
            the input values to vary the color of the adjustment image. When the observer O       <sub>A</sub>       presses
            a predetermined key when the color appearance of the color chart and color adjustment image is determined to
            become the same, the color value acquisition unit 12, RGB values of the color adjustment image at that time
            ( 1st color value) is stored. When there are a plurality of color charts, the same processing is repeated
            for each color chart.
        </p>
        <p>After that, the parameter calculation unit 13 uses the second color value corresponding to the condition N
            and the first color value corresponding to the condition A according to the equation (3 ′) or the equation
            (4) to use the parameter ( Transformation matrix). The obtained parameters are registered in the color
            conversion unit 14. The color conversion unit 14, an image signal input to the color conversion using the
            above parameters, and outputs to the display D       <sub>A.</sub>
        </p>
        <p>(Advantages of this system)
            <br/>
            According to the system of the present embodiment described above, the user (observer O       <sub>A</sub>       )
            compares the color chart and the color adjustment image output to the display D       <sub>A</sub>       so
            that both appear to have the same color. By simply adjusting the color of the color adjustment image, the
            “first color value” can be obtained. The "first color value" is individual differences of the observer O       <sub>
                A,
            </sub>       chromatic adaptation state under the illumination light I       <sub>A</sub>       of the
            observer O       <sub>A,</sub>       as well as all device characteristics of the display D       <sub>A
            </sub>       is reflected values Have. Therefore, by obtaining a parameter used for color conversion from
            the first color value and the second color value, “equal color appearance” can be realized without depending
            on any of the observer, the illumination light, and the image device. Color conversion is possible.
        </p>
        <p>  In addition, since the user only has to adjust the color of the color adjustment image so that the color
            chart and the color adjustment image have the same appearance, there is no need for special measurement
            equipment. No special knowledge about color management is required, and anyone can easily calibrate image
            devices.
        </p>
        <p>Further, the function of the gamma value determination unit 15 allows the user to grasp the value of the
            image signal corresponding to the relative luminance α only by adjusting the luminance of the target image
            and the monochrome image to be the same. Can be calculated. The gamma value also has individual differences
            of the observer O       <sub>A,</sub>       chromatic adaptation state under the illumination light I       <sub>
                A
            </sub>       of the observer O       <sub>A,</sub>       as well as all device characteristics of the
            display D       <sub>A</sub>       is reflected values. Therefore, by performing gamma correction with this
            gamma value, further improvement in color conversion accuracy can be expected. In addition, the user only
            needs to adjust the value of the monochromatic image so that the brightness of the target image and the
            monochromatic image are the same, so there is no need for special measurement equipment and expertise in
            color management. Is also unnecessary.
        </p>
        <embodiments-example>
            <p>  As illumination light, a CIE-defined A light source was used for condition A, and a CIE-defined D65
                light source was used for condition N. The same display was used. As shown in FIG. 4A, the front surface
                of the display was covered with an image paper having a Munsell value N5, and two openings at 2 ° with
                respect to the viewing distance were provided at the center of the image paper.
            </p>
            <p>  When obtaining the gamma value, a two-color checker stimulus was displayed in the left opening, and a
                monochromatic image for brightness adjustment was displayed in the right opening. Two types of α (1,
                0.75, 0.5, 0.25, 0.125, 0) were subjected to 2-color checker stimulation and monochromatic visual
                perception color matching, and the gamma value was determined from the 6 points of data by the least
                square method.
            </p>
            <p>When obtaining the conversion matrix, a management color chart based on the Munsell color system was set
                between the display surface and the drawing paper in the opening on the left side. A color adjustment
                image was displayed in the right opening. In this experiment, the following 43 management color charts
                were used. All the subjects were normal three-color type color sense persons, and the average value of
                three matchings was adopted as the experimental data.
                <br/>
                -Chromatic colors (40 colors): R, YR, Y, GY, G, BG, B, PB, P, RP
                <br/>
                (H = 5 &amp; 10) (V / C = 6/6 or 4/4)
                <br/>
                Achromatic color (3 colors): N3, N6, N8
            </p>
            <p>If a transformation matrix can be derived, a predicted value can be derived from experimental data. The
                difference between the predicted value and the actually measured value is represented by a distance in
                the three-dimensional R ′, G ′, B ′ color space, and averaged by the number of color charts is ΔE. As a
                result, 1, 2, 3, 5, which are equally spaced with 5R 6/6 as a reference
                <br/>
                In 10,20 color schemes (three colors are split complementary color schemes focusing on hue 5), as shown
                in FIG. 4 (b), the results tend to be saturated in the vicinity of 3-5 colors. Further, ΔE was not
                stable in the case of a biased color scheme.
            </p>
        </embodiments-example>
    </description-of-embodiments>
    <industrial-applicability>
        <p>  The present invention can be applied to color management between various image devices. In particular,
            since no measuring equipment or measurement work is required, it is suitable as simple color management for
            general PC users. As specific applications, for example, the following are assumed.
        </p>
        <p>  (1) Internet shopping site: In order to guarantee the color of the article displayed on the customer's PC,
            the condition on the customer PC side is matched with the condition on the shopping site side. In this case,
            the color management program may be downloaded from the Web server free of charge or for a fee, or the Web
            server may provide a color management function to the client PC. The color chart is assumed to be provided
            by a medium such as direct mail, catalog, magazine, and the like.
        </p>
        <p>  (2) Internet certification, e-learning: When it is necessary to display the same color on each examinee's
            (student) PC, the conditions on the examinee's (student) PC side are set to the predetermined standard
            conditions. Match. In this case as well, as in (1), a program and color chart can be provided.
        </p>
        <p>  (3) Image input device such as a digital camera: A color chart is captured by a digital camera or the like,
            and calibration is performed using a result obtained by performing color matching such as the captured image
            and the color chart.
        </p>
    </industrial-applicability>
    <reference-signs-list>
        <p>1 color management system 2 the color chart 10 storage unit 11 colors adjustment image output unit 12 color
            value acquiring unit 13 parameter calculation unit 14 color conversion section 15 gamma value determining
            unit 16 gamma correction unit       <sub>O</sub>       A,       <sub>O N</sub>       observer       <sub>I
            </sub>       A, I       <sub>N</sub>       illumination light       <sub>D</sub>       A,       <sub>D N
            </sub>       display
        </p>
    </reference-signs-list>
</description>