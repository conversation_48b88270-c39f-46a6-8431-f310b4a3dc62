<description lang="EN" load-source="patent-office" mxw-id="PDES230122787">
    <invention-title id="tilte1" lang="EN">The planing method of subway train conflict Resolution</invention-title>
    <technical-field>
        <p id="p0001" num="0001">Technical field</p>
        <p id="p0002" num="0002">The present invention relates to a kind of planing method of subway train conflict Resolution, particularly relate to a kind of planing method of the double-deck subway train conflict Resolution based on Robust Strategies.</p>
    </technical-field>
    <background-art>
        <p id="p0003" num="0003">Background technology</p>
        <p id="p0004" num="0004">Along with the expanding day of China's big and medium-sized cities scale, Traffic Systems is faced with the increasing pressure, and greatly developing Rail Transit System becomes the important means solving urban traffic congestion.Country's Eleventh Five-Year Plan outline is pointed out, big city with good conditionsi and group of cities area will using track traffic as Priority settings.China is just experiencing a unprecedented track traffic development peak period, and some cities have turned to the construction of net by the construction of line, and urban mass transit network is progressively formed.At Rail traffic network and the intensive complex region of train flow, still the train interval dispensing mode adopting train operation plan to combine based on subjective experience demonstrates its lag gradually, be in particular in: the formulation of (1) train operation plan timetable also reckons without the impact of various enchancement factor, easily cause traffic flow tactics to manage crowded, reduce the security that traffic system is run; (2) train scheduling active side overweights the personal distance kept between single train, not yet rises to the macroscopic aspect of train flow being carried out to strategy management; (3) train allocation process depends on the subjective experience of a line dispatcher more, and the selection randomness of allocating opportunity is comparatively large, lacks scientific theory and supports; (4) dispatcher's less impact considering external interference factor of allotment means of using, robustness and the availability of train programs are poor.</p>
        <p id="p0005" num="0005">The discussion object spininess of existing documents and materials to long-distance transportation by railroad, and still lacks system for the Scientific Regulation scheme of the city underground traffic system under large discharge, high density and closely-spaced service condition.Train Coordinated Control Scheme under complicated road network service condition needs calculate the running status of single vehicles in transportation network in region and optimize on strategic level, and implements collaborative planning to the traffic flow be made up of multiple train; Pre-tactical level solves congestion problems by the subregional critical operational parameters in effective monitoring mechanism adjustment transportation network top, and ensures the operational efficiency of all trains in this region; Tactical level then adjusts according to critical operational parameters the running status of relevant train, obtain single vehicles track optimizing scheme, change the headway management of train factors such as into considering train performance, scheduling rule and external environment in interior variable " microcosmic-macroscopic view-middle sight-microcosmic " Separation control mode from fixing manual type.</p>
    </background-art>
    <disclosure>
        <p id="p0006" num="0006">Summary of the invention</p>
        <p id="p0007" num="0007">The technical problem to be solved in the present invention is to provide the planing method of a kind of robustness and the good subway train conflict Resolution of availability, and the method can strengthen the subject of programs formulation and can effectively prevent subway train from running conflict.</p>
        <p id="p0008" num="0008">The technical scheme realizing the object of the invention is to provide a kind of planing method of subway train conflict Resolution, comprises the steps:</p>
        <p id="p0009" num="0009">Steps A, by subway transportation control center obtain its each sampling instant t infer the train track of each subway train in future time period;</p>
        <p id="p0010" num="0010">Step B, the train track of each subway train in future time period inferred at each sampling instant t obtained based on steps A, setting up the dynamic continuously observer to discrete conflict logic from train, is the conflict situation that discrete observation value is expressed by the continuous dynamic mapping of subway transportation system; When system likely violates traffic control rule, to the Hybrid dynamics behavior implementing monitoring of subway transportation hybrid system, for subway transportation control center provides warning information timely;</p>
        <p id="p0011" num="0011">Step C, when warning information occurs, meet train physical property, region hold stream constraint and track traffic scheduling rule prerequisite under, by setting optimizing index function, Adaptive Control Theory method is adopted to carry out robust dual layer resist to train operation track, and program results is transferred to each train, each train receives and performs train collision avoidance instruction until each train all arrives it free terminal; Its detailed process is as follows:</p>
        <p id="p0012" num="0012">Step C1, analysis result based on step B, determine the traffic flow regulation measure specifically taked, and comprises the travelling speed of adjustment train and/or adjustment train in station time two class measure, and adopt specified place and the opportunity of above regulation measure;</p>
        <p id="p0013" num="0013">
            Termination reference point locations P, collision avoidance policy control time domain Θ, the trajectory predictions time domain of step C2, setting train collision avoidance planning

            <img file="BDA0000691727190000023.GIF" he="56" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="66"/>
        </p>
        <p id="p0014" num="0014">
            Step C3, operation conflict Resolution process model building, be considered as the inside and outside dual planning problem based on both macro and micro aspect by the operation conflict Resolution in above-listed for Rail traffic network workshop, wherein

            <img file="BDA0000691727190000021.GIF" he="106" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="378"/>
            represent outer plan model, i.e. train flow flow-Density and distribution problem on track traffic road network,

            <img file="BDA0000691727190000022.GIF" he="106" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="365"/>
            represent internal layer plan model, namely on track traffic section, the state of single vehicles adjusts problem; F, x

            <sub>1</sub>
            and u

            <sub>1</sub>
            the objective function of outer planning problem, state vector and decision vector respectively, G (x

            <sub>1</sub>
            , u

            <sub>1</sub>
            )≤0 is the constraint condition of outer planning, f, x

            <sub>2</sub>
            and u

            <sub>2</sub>
            the objective function of internal layer planning problem, state vector and decision vector respectively, g (x

            <sub>2</sub>
            , u

            <sub>2</sub>
            )≤0 is the constraint condition of internal layer planning, using the reference input that the outer program results of macroscopic aspect is planned as microcosmic point internal layer;

        </p>
        <p id="p0015" num="0015">
            Step C4, run the modeling of conflict Resolution variable bound, build and comprise adjustable train quantity a, train speed ω and train at variablees such as station time γ in interior both macro and micro constraint condition: the variable bound that wherein t need implement the section k of conflict Resolution can be described as: a

            <sub>k</sub>
            (t)≤a

            <sub>m</sub>
            , ω

            <sub>k</sub>
            (t)≤ω

            <sub>k</sub>
            , γ

            <sub>k</sub>
            (t)≤γ

            <sub>m</sub>
            , a

            <sub>m</sub>
            , ω

            <sub>m</sub>
            , γ

            <sub>m</sub>
            be respectively maximum adjustable train quantity, maximum train running speed and most long line car in the station time, this type of frees the constraint that variable can be subject to the aspects such as traffic flow distribution, train physical property and personal distance;

        </p>
        <p id="p0016" num="0016">Step C5, Multi-objective Robust optimum road network flow allocation plan solves: based on cooperative collision avoidance trajectory planning thought, for different performance index, by selecting different conflict Resolution objective functions, solving the multiple goal traffic flow flowrate optimization allocation plan based on Euler's network model in traffic flow operation macroscopic aspect and respectively controlling section in Rolling Planning interval, only implementing its first Optimal Control Strategy;</p>
        <p id="p0017" num="0017">Step C6, Multi-objective Robust optimum section train operation state adjustment: according to each section or zone flow configuration result, mix the single vehicles controlled quentity controlled variable of evolutionary model and Lagrangian plan model acquisition optimum based on train operation, generate optimum single vehicles running orbit and respectively regulate and control train in Rolling Planning interval, only implement its first Optimal Control Strategy;</p>
        <p id="p0018" num="0018">Step C7, each train receive and perform train collision avoidance instruction;</p>
        <p id="p0019" num="0019">Step C8, in next sampling instant, repeat step C5 to C7 free terminal until each train all arrives it.</p>
        <p id="p0020" num="0020">Further, the specific implementation process of step B is as follows:</p>
        <p id="p0021" num="0021">
            Step B1, construct conflict hypersurface collection of functions based on regulation rule: set up hypersurface collection of functions in order to reflect the contention situation of system, wherein, continuous function h relevant to single train in conflict hypersurface

            <sub>i</sub>
            be I type hypersurface, the continuous function h relevant to two trains

            <sub>iI</sub>
            it is II type hypersurface;

        </p>
        <p id="p0022" num="0022">
            Step B2, set up by train continuous state to the observer of discrete conflict situation, build the safety rule collection d that need meet when train runs in traffic network

            <sub>ij</sub>
            (t)&gt;=d

            <sub>min</sub>
            , wherein d

            <sub>ij</sub>
            t () represents the actual interval of train i and train j in t, d

            <sub>min</sub>
            represent the minimum safety interval between train;

        </p>
        <p id="p0023" num="0023">Step B3, based on person machine system theoretical and complication system hierarchical control principle, according to train operation pattern, build people at the real-time monitoring mechanism of the train of loop, the operation of guarantee system is in safe reachable set, design the discrete watch-dog from conflict to conflict Resolution means, when the discrete observation vector of observer shows that safety rule rally is breached, send corresponding warning information at once.</p>
        <p id="p0024" num="0024">Further, in step C2, stopping reference point locations P is that the next one of train stops website.</p>
        <p id="p0025" num="0025">Further, in step C2, the value of parameter Θ is 300 seconds.</p>
        <p id="p0026" num="0026">
            More further, in step C2,

            <img file="BDA0000691727190000031.GIF" he="44" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="40"/>
            value be 300 seconds.

        </p>
        <p id="p0027" num="0027">Further, the detailed process of step C5 is as follows: order</p>
        <p id="p0028" num="0028">
            <maths>
                <math>
                    <mrow>
                        <msubsup>
                            <mi>d</mi>
                            <mi>it</mi>
                            <mn>2</mn>
                        </msubsup>
                        <mo>=</mo>
                        <msubsup>
                            <mrow>
                                <mo>|</mo>
                                <mo>|</mo>
                                <msub>
                                    <mi>P</mi>
                                    <mi>i</mi>
                                </msub>
                                <mrow>
                                    <mo>(</mo>
                                    <mi>t</mi>
                                    <mo>)</mo>
                                </mrow>
                                <mo>-</mo>
                                <msubsup>
                                    <mi>P</mi>
                                    <mi>i</mi>
                                    <mi>f</mi>
                                </msubsup>
                                <mo>|</mo>
                                <mo>|</mo>
                            </mrow>
                            <mn>2</mn>
                            <mn>2</mn>
                        </msubsup>
                        <mo>=</mo>
                        <msup>
                            <mrow>
                                <mo>(</mo>
                                <msub>
                                    <mi>x</mi>
                                    <mi>it</mi>
                                </msub>
                                <mo>-</mo>
                                <msubsup>
                                    <mi>x</mi>
                                    <mi>i</mi>
                                    <mi>f</mi>
                                </msubsup>
                                <mo>)</mo>
                            </mrow>
                            <mn>2</mn>
                        </msup>
                        <mo>+</mo>
                        <msup>
                            <mrow>
                                <mo>(</mo>
                                <msub>
                                    <mi>y</mi>
                                    <mi>it</mi>
                                </msub>
                                <mo>-</mo>
                                <msubsup>
                                    <mi>y</mi>
                                    <mi>i</mi>
                                    <mi>f</mi>
                                </msubsup>
                                <mo>)</mo>
                            </mrow>
                            <mn>2</mn>
                        </msup>
                        <mo>,</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0029" num="0029">
            Wherein

            <img file="BDA0000691727190000042.GIF" he="79" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="64"/>
            represent the distance between the t current position of train i and next website square, P

            <sub>i</sub>
            (t)=(x

            <sub>it</sub>
            , y

            <sub>it</sub>
            ) represent the two-dimensional coordinate value of t train i,

            <img file="BDA0000691727190000043.GIF" he="98" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="293"/>
            represent the two-dimensional coordinate value of next stop website of train i, so the priority index of t train i can be set as:

        </p>
        <p id="p0030" num="0030">
            <maths>
                <math>
                    <mrow>
                        <msub>
                            <mi>&amp;lambda;</mi>
                            <mi>it</mi>
                        </msub>
                        <mo>=</mo>
                        <mn>100</mn>
                        <mfrac>
                            <msubsup>
                                <mi>d</mi>
                                <mi>it</mi>
                                <mrow>
                                    <mo>-</mo>
                                    <mn>2</mn>
                                </mrow>
                            </msubsup>
                            <mrow>
                                <munderover>
                                    <mi>&amp;Sigma;</mi>
                                    <mrow>
                                        <mi>i</mi>
                                        <mo>=</mo>
                                        <mn>1</mn>
                                    </mrow>
                                    <msub>
                                        <mi>n</mi>
                                        <mi>t</mi>
                                    </msub>
                                </munderover>
                                <msubsup>
                                    <mi>d</mi>
                                    <mi>it</mi>
                                    <mrow>
                                        <mo>-</mo>
                                        <mn>2</mn>
                                    </mrow>
                                </msubsup>
                            </mrow>
                        </mfrac>
                        <mo>,</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0031" num="0031">
            Wherein n

            <sub>t</sub>
            represent train number t section existing conflict, from the implication of priority index, train is nearer apart from next website, and its priority is higher;

        </p>
        <p id="p0032" num="0032">Setting optimizing index</p>
        <p id="p0033" num="0033">
            <maths>
                <math>
                    <mrow>
                        <mfenced close="" open="">
                            <mtable>
                                <mtr>
                                    <mtd>
                                        <msup>
                                            <mi>J</mi>
                                            <mo>*</mo>
                                        </msup>
                                        <mrow>
                                            <mo>(</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <mn>1</mn>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <mn>1</mn>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <mn>1</mn>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>p&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <msub>
                                                    <mi>n</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <msub>
                                                    <mi>n</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <msub>
                                                    <mi>n</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>&amp;Pi;&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>)</mo>
                                        </mrow>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mo>=</mo>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>s</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <mi>&amp;Pi;</mi>
                                        </munderover>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>i</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <msub>
                                                <mi>n</mi>
                                                <mi>t</mi>
                                            </msub>
                                        </munderover>
                                        <msub>
                                            <mi>&amp;lambda;</mi>
                                            <mi>it</mi>
                                        </msub>
                                        <msubsup>
                                            <mrow>
                                                <mo>|</mo>
                                                <mo>|</mo>
                                                <msub>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>s&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                                <mo>-</mo>
                                                <msubsup>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                    <mi>f</mi>
                                                </msubsup>
                                                <mo>|</mo>
                                                <mo>|</mo>
                                            </mrow>
                                            <mn>2</mn>
                                            <mn>2</mn>
                                        </msubsup>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mo>=</mo>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>s</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <mi>&amp;Pi;</mi>
                                        </munderover>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>i</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <msub>
                                                <mi>n</mi>
                                                <mi>t</mi>
                                            </msub>
                                        </munderover>
                                        <msup>
                                            <mrow>
                                                <mo>(</mo>
                                                <msub>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>s&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                                <mo>-</mo>
                                                <msubsup>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                    <mi>f</mi>
                                                </msubsup>
                                                <mo>)</mo>
                                            </mrow>
                                            <mi>T</mi>
                                        </msup>
                                        <msub>
                                            <mi>Q</mi>
                                            <mi>it</mi>
                                        </msub>
                                        <mrow>
                                            <mo>(</mo>
                                            <msub>
                                                <mi>P</mi>
                                                <mi>i</mi>
                                            </msub>
                                            <mrow>
                                                <mo>(</mo>
                                                <mi>t</mi>
                                                <mo>+</mo>
                                                <mi>s&amp;Delta;t</mi>
                                                <mo>)</mo>
                                            </mrow>
                                            <mo>-</mo>
                                            <msubsup>
                                                <mi>P</mi>
                                                <mi>i</mi>
                                                <mi>f</mi>
                                            </msubsup>
                                            <mo>)</mo>
                                        </mrow>
                                    </mtd>
                                </mtr>
                            </mtable>
                        </mfenced>
                        <mo>,</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0034" num="0034">
            Wherein i ∈ I (t) represent train code and I (t)=1,2 ..., n

            <sub>t</sub>
            , P

            <sub>i</sub>
            (t+s Δ t) represents the position vector of train at moment (t+s Δ t), and ∏ represents control time, i.e. the time span of Future Trajectory planning from current time, u

            <sub>i</sub>
            represent the optimal control sequence of train i to be optimized, Q

            <sub>it</sub>
            for positive definite diagonal matrix, its diagonal element is the priority index λ of train i in t

            <sub>it</sub>
            , and

            <maths>
                <math>
                    <mrow>
                        <msub>
                            <mi>Q</mi>
                            <mi>it</mi>
                        </msub>
                        <mo>=</mo>
                        <mfenced close="]" open="[">
                            <mtable>
                                <mtr>
                                    <mtd>
                                        <msub>
                                            <mi>&amp;lambda;</mi>
                                            <mi>it</mi>
                                        </msub>
                                    </mtd>
                                    <mtd>
                                        <mn>0</mn>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mn>0</mn>
                                    </mtd>
                                    <mtd>
                                        <msub>
                                            <mi>&amp;lambda;</mi>
                                            <mi>it</mi>
                                        </msub>
                                    </mtd>
                                </mtr>
                            </mtable>
                        </mfenced>
                        <mo>.</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0035" num="0035">The present invention has positive effect: the planing method of (1) subway train conflict Resolution of the present invention is under the prerequisite meeting track traffic control personal distance, based on the real-time position information of train, according to track traffic regulation rule, alarm is implemented to the conflict that may occur, gives each train planning conflict Resolution track according to train performance data and related constraint condition.</p>
        <p id="p0036" num="0036">(2) the present invention is based on the scene monitoring mechanism of constructed " people is at loop ", effecting reaction can be made in time alternately to train inside continuous variable and the frequent of external discrete event, overcome the shortcoming of conventional open loop monitored off-line scheme.</p>
        <p id="p0037" num="0037">(3) what the dual layer resist scheme of train flow of the present invention can not only reduce Optimal Control Problem solves dimension, the practicality of regulation and control scheme can also be strengthened, the model and algorithm overcome in existing document only pay close attention to train AT STATION to the time of sending out, and lack the defect of control when train is run on concrete railroad section.</p>
        <p id="p0038" num="0038">(4) the present invention is based on controllability and the sensitivity analysis result of Rail traffic network topological structure, can be subway transportation stream allotment the time, allotment place and allotment means selection scientific basis is provided, avoid the randomness that regulation and control scheme is chosen.</p>
    </disclosure>
    <description-of-drawings>
        <p id="p0039" num="0039">Accompanying drawing explanation</p>
        <p id="p0040" num="0040">Fig. 1 is that train operation conflict optimum frees figure;</p>
        <p id="p0041" num="0041">Fig. 2 is the schematic diagram of the double-deck allocation plan of traffic flow.</p>
    </description-of-drawings>
    <mode-for-invention>
        <p id="p0042" num="0042">Embodiment</p>
        <p id="p0043" num="0043">(embodiment 1)</p>
        <p id="p0044" num="0044">The flow-optimized control system of a kind of subway transportation, comprise wire topologies generation module, data transmission module, car-mounted terminal module, control terminal module and track monitoring module, track monitoring module is collected the status information of train and is supplied to control terminal module.</p>
        <p id="p0045" num="0045">Described control terminal module comprises following submodule:</p>
        <p id="p0046" num="0046">Lothrus apterus Track Pick-up module before train operation: according to the Train operation plan table time of running, first set up Modeling Method for Train Dynamics, then sets up train operation conflict according to train operation conflict Coupling point and allocates model in advance, finally generate Lothrus apterus train operation track.</p>
        <p id="p0047" num="0047">Train operation Track Pick-up a middle or short term module: the train real time status information provided according to track monitoring module, utilizes data mining model, infers the running orbit of train in future time period.</p>
        <p id="p0048" num="0048">Train operation situation monitoring module: at each sampling instant t, based on the track estimation result of train, when likely occurring violating the situation of safety rule when between train, provides warning information to its dynamic behaviour implementing monitoring and for control terminal.</p>
        <p id="p0049" num="0049">Train collision avoidance track optimizing module: when train operation situation monitoring module sends warning information, meet train physical property, region hold stream constraint and track traffic scheduling rule prerequisite under, by setting optimizing index function, adopt Adaptive Control Theory method to carry out robust dual layer resist by control terminal module to train operation track, and by data transmission module, program results is transferred to the execution of car-mounted terminal module.Train collision avoidance track optimizing module comprises internal layer planning and outer planning two class planning process.</p>
        <p id="p0050" num="0050">Apply the planing method of the subway train conflict Resolution of the flow-optimized control system of above-mentioned subway transportation, comprise the following steps:</p>
        <p id="p0051" num="0051">Steps A, obtain its train track of each subway train in future time period inferred in each sampling instant by subway transportation control center; Subway transportation control center passes through monitoring and obtains the real-time of each train and historical position information, and the track of train in future time period is inferred according to the real-time of train and historical position information by subway transportation control center; ;</p>
        <p id="p0052" num="0052">Step B, the train track of each subway train in future time period inferred at each sampling instant t obtained based on steps A, setting up the dynamic continuously observer to discrete conflict logic from train, is the conflict situation that discrete observation value is expressed by the continuous dynamic mapping of subway transportation system; When system likely violates traffic control rule, to the Hybrid dynamics behavior implementing monitoring of subway transportation hybrid system, for subway transportation control center provides warning information timely;</p>
        <p id="p0053" num="0053">The specific implementation process of described step B is as follows:</p>
        <p id="p0054" num="0054">
            Step B1, construct conflict hypersurface collection of functions based on regulation rule: set up hypersurface collection of functions in order to reflect the contention situation of system, wherein, continuous function h relevant to single train in conflict hypersurface

            <sub>i</sub>
            be I type hypersurface, the continuous function h relevant to two trains

            <sub>iI</sub>
            it is II type hypersurface;

        </p>
        <p id="p0055" num="0055">
            Step B2, set up by train continuous state to the observer of discrete conflict situation, build the safety rule collection d that need meet when train runs in traffic network

            <sub>ij</sub>
            (t)&gt;=d

            <sub>min</sub>
            , wherein d

            <sub>ij</sub>
            t () represents the actual interval of train i and train j in t, d

            <sub>min</sub>
            represent the minimum safety interval between train;

        </p>
        <p id="p0056" num="0056">Step B3, based on person machine system theoretical and complication system hierarchical control principle, according to train operation pattern, build people at the real-time monitoring mechanism of the train of loop, the operation of guarantee system is in safe reachable set, design the discrete watch-dog from conflict to conflict Resolution means, when the discrete observation vector of observer shows that safety rule rally is breached, send corresponding warning information at once.</p>
        <p id="p0057" num="0057">Step C, see Fig. 1, when warning information occurs, meet train physical property, region hold stream constraint and track traffic scheduling rule prerequisite under, by setting optimizing index function, Adaptive Control Theory method is adopted to carry out robust dual layer resist to train operation track, and program results is transferred to each train, each train receives and performs train collision avoidance instruction until each train all arrives it free terminal; Its detailed process is as follows:</p>
        <p id="p0058" num="0058">Step C1, analysis result based on step B3, determine the traffic flow regulation measure specifically taked, and comprises the travelling speed of adjustment train and/or adjustment train in station time two class measure, and adopt specified place and the opportunity of above regulation measure;</p>
        <p id="p0059" num="0059">
            Termination reference point locations P, collision avoidance policy control time domain Θ, the trajectory predictions time domain of step C2, setting train collision avoidance planning

            <img file="BDA0000691727190000061.GIF" he="46" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="65"/>
        </p>
        <p id="p0060" num="0060">
            Stopping reference point locations P is that the next one of train stops website, and the value of parameter Θ is 300 seconds,

            <img file="BDA0000691727190000062.GIF" he="44" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="40"/>
            value be 300 seconds;

        </p>
        <p id="p0061" num="0061">
            Step C3, operation conflict Resolution process model building, be considered as the inside and outside dual planning problem based on both macro and micro aspect by the operation conflict Resolution in above-listed for Rail traffic network workshop, see Fig. 2, wherein

            <img file="BDA0000691727190000071.GIF" he="106" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="368"/>
            represent outer plan model, i.e. train flow flow-Density and distribution problem on track traffic road network,

            <img file="BDA0000691727190000072.GIF" he="104" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="366"/>
            represent internal layer plan model, namely on track traffic section, the state of single vehicles adjusts problem; F, x

            <sub>1</sub>
            and u

            <sub>1</sub>
            the objective function of outer planning problem, state vector and decision vector respectively, G (x

            <sub>1</sub>
            , u

            <sub>1</sub>
            )≤0 is the constraint condition of outer planning, f, x

            <sub>2</sub>
            and u

            <sub>2</sub>
            the objective function of internal layer planning problem, state vector and decision vector respectively, g (x

            <sub>2</sub>
            , u

            <sub>2</sub>
            )≤0 is the constraint condition of internal layer planning, using the reference input that the outer program results of macroscopic aspect is planned as microcosmic point internal layer;

        </p>
        <p id="p0062" num="0062">
            Step C4, run the modeling of conflict Resolution variable bound, build and comprise adjustable train quantity a, train speed ω and train at variablees such as station time γ in interior both macro and micro constraint condition: the variable bound that wherein t need implement the section k of conflict Resolution can be described as: a

            <sub>k</sub>
            (t)≤a

            <sub>m</sub>
            , ω

            <sub>k</sub>
            (t)≤ω

            <sub>m</sub>
            , γ

            <sub>k</sub>
            (t)≤γ

            <sub>m</sub>
            , a

            <sub>m</sub>
            , ω

            <sub>m</sub>
            , γ

            <sub>m</sub>
            be respectively maximum adjustable train quantity, maximum train running speed and most long line car in the station time, this type of frees the constraint that variable can be subject to the aspects such as traffic flow distribution, train physical property and personal distance;

        </p>
        <p id="p0063" num="0063">Step C5, Multi-objective Robust optimum road network flow allocation plan solves: based on cooperative collision avoidance trajectory planning thought, for different performance index, by selecting different conflict Resolution objective functions, solving the multiple goal traffic flow flowrate optimization allocation plan based on Euler's network model in traffic flow operation macroscopic aspect and respectively controlling section in Rolling Planning interval, only implementing its first Optimal Control Strategy; Its detailed process is as follows: order</p>
        <p id="p0064" num="0064">
            <maths>
                <math>
                    <mrow>
                        <msubsup>
                            <mi>d</mi>
                            <mi>it</mi>
                            <mn>2</mn>
                        </msubsup>
                        <mo>=</mo>
                        <msubsup>
                            <mrow>
                                <mo>|</mo>
                                <mo>|</mo>
                                <msub>
                                    <mi>P</mi>
                                    <mi>i</mi>
                                </msub>
                                <mrow>
                                    <mo>(</mo>
                                    <mi>t</mi>
                                    <mo>)</mo>
                                </mrow>
                                <mo>-</mo>
                                <msubsup>
                                    <mi>P</mi>
                                    <mi>i</mi>
                                    <mi>f</mi>
                                </msubsup>
                                <mo>|</mo>
                                <mo>|</mo>
                            </mrow>
                            <mn>2</mn>
                            <mn>2</mn>
                        </msubsup>
                        <mo>=</mo>
                        <msup>
                            <mrow>
                                <mo>(</mo>
                                <msub>
                                    <mi>x</mi>
                                    <mi>it</mi>
                                </msub>
                                <mo>-</mo>
                                <msubsup>
                                    <mi>x</mi>
                                    <mi>i</mi>
                                    <mi>f</mi>
                                </msubsup>
                                <mo>)</mo>
                            </mrow>
                            <mn>2</mn>
                        </msup>
                        <mo>+</mo>
                        <msup>
                            <mrow>
                                <mo>(</mo>
                                <msub>
                                    <mi>y</mi>
                                    <mi>it</mi>
                                </msub>
                                <mo>-</mo>
                                <msubsup>
                                    <mi>y</mi>
                                    <mi>i</mi>
                                    <mi>f</mi>
                                </msubsup>
                                <mo>)</mo>
                            </mrow>
                            <mn>2</mn>
                        </msup>
                        <mo>,</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0065" num="0065">
            Wherein

            <img file="BDA0000691727190000074.GIF" he="82" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="66"/>
            represent the distance between the t current position of train i and next website square, P

            <sub>i</sub>
            (t)=(x

            <sub>it</sub>
            , y

            <sub>it</sub>
            ) represent the two-dimensional coordinate value of t train i,

            <img file="BDA0000691727190000075.GIF" he="95" img-content="drawing" img-format="gif" inline="yes" orientation="portrait" wi="293"/>
            represent the two-dimensional coordinate value of next stop website of train i, so the priority index of t train i can be set as:

        </p>
        <p id="p0066" num="0066">
            <maths>
                <math>
                    <mrow>
                        <msub>
                            <mi>&amp;lambda;</mi>
                            <mi>it</mi>
                        </msub>
                        <mo>=</mo>
                        <mn>100</mn>
                        <mfrac>
                            <msubsup>
                                <mi>d</mi>
                                <mi>it</mi>
                                <mrow>
                                    <mo>-</mo>
                                    <mn>2</mn>
                                </mrow>
                            </msubsup>
                            <mrow>
                                <munderover>
                                    <mi>&amp;Sigma;</mi>
                                    <mrow>
                                        <mi>i</mi>
                                        <mo>=</mo>
                                        <mn>1</mn>
                                    </mrow>
                                    <msub>
                                        <mi>n</mi>
                                        <mi>t</mi>
                                    </msub>
                                </munderover>
                                <msubsup>
                                    <mi>d</mi>
                                    <mi>it</mi>
                                    <mrow>
                                        <mo>-</mo>
                                        <mn>2</mn>
                                    </mrow>
                                </msubsup>
                            </mrow>
                        </mfrac>
                        <mo>,</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0067" num="0067">
            Wherein n

            <sub>t</sub>
            represent train number t section existing conflict, from the implication of priority index, train is nearer apart from next website, and its priority is higher;

        </p>
        <p id="p0068" num="0068">Setting optimizing index</p>
        <p id="p0069" num="0069">
            <maths>
                <math>
                    <mrow>
                        <mfenced close="" open="">
                            <mtable>
                                <mtr>
                                    <mtd>
                                        <msup>
                                            <mi>J</mi>
                                            <mo>*</mo>
                                        </msup>
                                        <mrow>
                                            <mo>(</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <mn>1</mn>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <mn>1</mn>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <mn>1</mn>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>p&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <msub>
                                                    <mi>n</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <msub>
                                                    <mi>n</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>,</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>.</mo>
                                            <mo>,</mo>
                                            <msubsup>
                                                <mi>u</mi>
                                                <msub>
                                                    <mi>n</mi>
                                                    <mi>t</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>&amp;Pi;&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                            </msubsup>
                                            <mo>)</mo>
                                        </mrow>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mo>=</mo>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>s</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <mi>&amp;Pi;</mi>
                                        </munderover>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>i</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <msub>
                                                <mi>n</mi>
                                                <mi>t</mi>
                                            </msub>
                                        </munderover>
                                        <msub>
                                            <mi>&amp;lambda;</mi>
                                            <mi>it</mi>
                                        </msub>
                                        <msubsup>
                                            <mrow>
                                                <mo>|</mo>
                                                <mo>|</mo>
                                                <msub>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>s&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                                <mo>-</mo>
                                                <msubsup>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                    <mi>f</mi>
                                                </msubsup>
                                                <mo>|</mo>
                                                <mo>|</mo>
                                            </mrow>
                                            <mn>2</mn>
                                            <mn>2</mn>
                                        </msubsup>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mo>=</mo>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>s</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <mi>&amp;Pi;</mi>
                                        </munderover>
                                        <munderover>
                                            <mi>&amp;Sigma;</mi>
                                            <mrow>
                                                <mi>i</mi>
                                                <mo>=</mo>
                                                <mn>1</mn>
                                            </mrow>
                                            <msub>
                                                <mi>n</mi>
                                                <mi>t</mi>
                                            </msub>
                                        </munderover>
                                        <msup>
                                            <mrow>
                                                <mo>(</mo>
                                                <msub>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                </msub>
                                                <mrow>
                                                    <mo>(</mo>
                                                    <mi>t</mi>
                                                    <mo>+</mo>
                                                    <mi>s&amp;Delta;t</mi>
                                                    <mo>)</mo>
                                                </mrow>
                                                <mo>-</mo>
                                                <msubsup>
                                                    <mi>P</mi>
                                                    <mi>i</mi>
                                                    <mi>f</mi>
                                                </msubsup>
                                                <mo>)</mo>
                                            </mrow>
                                            <mi>T</mi>
                                        </msup>
                                        <msub>
                                            <mi>Q</mi>
                                            <mi>it</mi>
                                        </msub>
                                        <mrow>
                                            <mo>(</mo>
                                            <msub>
                                                <mi>P</mi>
                                                <mi>i</mi>
                                            </msub>
                                            <mrow>
                                                <mo>(</mo>
                                                <mi>t</mi>
                                                <mo>+</mo>
                                                <mi>s&amp;Delta;t</mi>
                                                <mo>)</mo>
                                            </mrow>
                                            <mo>-</mo>
                                            <msubsup>
                                                <mi>P</mi>
                                                <mi>i</mi>
                                                <mi>f</mi>
                                            </msubsup>
                                            <mo>)</mo>
                                        </mrow>
                                    </mtd>
                                </mtr>
                            </mtable>
                        </mfenced>
                        <mo>,</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0070" num="0070">
            Wherein i ∈ I (t) represent train code and I (t)=1,2 ..., n

            <sub>t</sub>
            , P

            <sub>i</sub>
            (t+s Δ t) represents the position vector of train at moment (t+s Δ t), and ∏ represents control time, i.e. the time span of Future Trajectory planning from current time, u

            <sub>i</sub>
            represent the optimal control sequence of train i to be optimized, Q

            <sub>it</sub>
            for positive definite diagonal matrix, its diagonal element is the priority index λ of train i in t

            <sub>it</sub>
            , and

            <maths>
                <math>
                    <mrow>
                        <msub>
                            <mi>Q</mi>
                            <mi>it</mi>
                        </msub>
                        <mo>=</mo>
                        <mfenced close="]" open="[">
                            <mtable>
                                <mtr>
                                    <mtd>
                                        <msub>
                                            <mi>&amp;lambda;</mi>
                                            <mi>it</mi>
                                        </msub>
                                    </mtd>
                                    <mtd>
                                        <mn>0</mn>
                                    </mtd>
                                </mtr>
                                <mtr>
                                    <mtd>
                                        <mn>0</mn>
                                    </mtd>
                                    <mtd>
                                        <msub>
                                            <mi>&amp;lambda;</mi>
                                            <mi>it</mi>
                                        </msub>
                                    </mtd>
                                </mtr>
                            </mtable>
                        </mfenced>
                        <mo>;</mo>
                    </mrow>
                </math>
            </maths>
        </p>
        <p id="p0071" num="0071">Step C6, Multi-objective Robust optimum section train operation state adjustment: according to each section or zone flow configuration result, mix the single vehicles controlled quentity controlled variable of evolutionary model and Lagrangian plan model acquisition optimum based on train operation, generate optimum single vehicles running orbit and respectively regulate and control train in Rolling Planning interval, only implement its first Optimal Control Strategy;</p>
        <p id="p0072" num="0072">Step C7, each train receive and perform train collision avoidance instruction;</p>
        <p id="p0073" num="0073">Step C8, in next sampling instant, repeat step C5 to C7 free terminal until each train all arrives it.</p>
        <p id="p0074" num="0074">Obviously, above-described embodiment is only for example of the present invention is clearly described, and is not the restriction to embodiments of the present invention.For those of ordinary skill in the field, can also make other changes in different forms on the basis of the above description.Here exhaustive without the need to also giving all embodiments.And these belong to spirit institute's apparent change of extending out of the present invention or change and are still among protection scope of the present invention.</p>
    </mode-for-invention>
</description>
