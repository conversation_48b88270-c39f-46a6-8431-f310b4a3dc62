<description lang="EN" load-source="patent-office"
             mxw-id="PDES15918272">  <?RELAPP description="Other Patent Relations" end="lead"?>
    <heading>CROSS-REFERENCE TO RELATED APPLICATION</heading>
    <p num="p-0002">This application is related to U.S. patent application Ser. No. 09/624,149 filed Jul. 24, 2000.</p>
    <?RELAPP description="Other Patent Relations" end="tail"?>  <?BRFSUM description="Brief Summary" end="lead"?>
    <heading>FIELD OF THE INVENTION</heading>
    <p num="p-0003">The present invention pertains to an apparatus for and a method of applying both amplitude
        predistortion and phase predistortion to a modulated baseband signal. More particularly, the present invention
        pertains to an apparatus for and a method of generating an amplitude modulated radio frequency signal by
        amplitude predistorting its baseband signal, using the inverse hyperbolic tangent of a value based on the
        envelope of the baseband in-phase and quadrature components, and phase predistorting the baseband signal, using
        the hyperbolic tangent of that value.
    </p>
    <heading>BACKGROUND OF THE INVENTION</heading>
    <p num="p-0004">Environments such as commercial airliners frequently have several radios that operate at different
        frequencies. Not only must these radios avoid interference with each other, but also they must meet spectrum
        mask requirements imposed by regulatory agencies, such as the United States Federal Communications Commission.
        The output from the solid state power amplifier of such a radio often includes distortion that can be
        characterized by a hyperbolic tangent function. Both amplitude distortion and phase distortion may occur. The
        transmit spectrum of such a radio signal can spread near the desired signal band if the envelope of the
        transmitted signal is not constant, particularly if the transmitter power amplifier is being driven into soft
        saturation. While spurious emissions might be reduced by predistorting of the radio frequency signal envelope
        just before transmission to the output power amplifier, this requires analog multipliers. Even then, if noise is
        picked up in the multiplier circuit, that noise will modulate the desired signal and pass through to the output.
    </p>
    <p num="p-0005">One approach to overcoming power amplifier nonlinearity utilizes the function f(x)=2x/(1+x    <sup>
        2
    </sup>    ) for amplitude predistortion and the function ph(x)=(πf(x))/6=2πx/6(    <b>1+x</b>
        <sup>2</sup>    ) for phase predistortion, where x is the instantaneous value of the envelope. Another approach
        to overcoming amplitude distortion is to utilize the “cuber” function f(x)=x+x    <sup>3</sup>    /3, where
        again x is the instantaneous value of the envelope. These approaches have been found to provide less than
        optimum linearity in the power amplifier output.
    </p>
    <heading>SUMMARY OF THE INVENTION</heading>
    <p num="p-0006">The present invention is an apparatus for and a method of amplitude and phase distorting a modulated
        radio frequency signal such that after passing of the distorted signal through a non-linear power amplifier,
        undesirable spurious emissions in the resulting spectrum are reduced. In accordance with the present invention,
        a complex amplitude modulated baseband signal, having an in-phase component I and a quadrature component Q, is
        sampled to obtain k samples I    <sub>k</sub>    of the in-phase component and k samples Q    <sub>k</sub>    of
        the quadrature component, and the magnitude of the envelope of the baseband samples is determined. A distortion
        factor based on the product of the hyperbolic tangent (“tanh”) and the inverse hyperbolic tangent or
        archyperbolic tangent (“atanh”) of a scaled value of the complex baseband sample magnitude is used to multiply
        each sample of the in-phase component and of the quadrature component so as to provide predistorted components.
        These predistorted components are combined and used to provide a distorted radio frequency (“RF”) signal which
        is applied to the power amplifier. The power amplifier distortion cancels the distortion in the radio frequency
        signal so that the power amplifier provides a substantially undistorted output signal.
    </p>
    <p num="p-0007">The scaling factor is obtained by combining a portion of the output signal envelope with the
        undistorted envelope in a feedback circuit. The feedback circuit preferably computes the mean square error
        between the undistorted envelope and the output signal envelope. Preferably, to assure that the mean square
        error is computed correctly, both envelopes are normalized. The mean square error is adjusted by a fixed gain
        control and integrated, and the result used to scale the undistorted envelope prior to determination of the
        hyperbolic tangent and archyperbolic tangent functions.
    </p>
    <p num="p-0008">The envelope of the baseband signal is thus subjected to amplitude and phase predistortion prior to
        upconversion to the radio frequency signal. This avoids impressing pick-up noise on the transmitted envelope. It
        is possible to do the predistortion prior to intermediate frequency (IF) and RF bandpass filtering of the radio
        frequency signal since such filtering has a wide bandwidth, allowing the distorted signal spectrum to pass
        through the power amplifier.
    </p>
    <p num="p-0009">Preferably, the predistortion apparatus of the present invention is implemented in a gate array,
        such as a field programmable gate array.
    </p>
    <?BRFSUM description="Brief Summary" end="tail"?>  <?brief-description-of-drawings description="Brief Description of Drawings" end="lead"?>
    <description-of-drawings>
        <heading>BRIEF DESCRIPTION OF THE DRAWINGS</heading>
        <p num="p-0010">These and other aspects and advantages of the present invention are more apparent from the
            following detailed description and claims, particularly when considered in conjunction with the accompanying
            drawings in which like parts bear like reference numerals. In the drawings:
        </p>
        <p num="p-0011">
            <figref idrefs="DRAWINGS">FIG. 1</figref>       is a block diagram of an apparatus for generating an
            amplitude and phase predistorted radio frequency signal in accordance with a preferred embodiment of the
            present invention;
        </p>
        <p num="p-0012">
            <figref idrefs="DRAWINGS">FIG. 2</figref>       is a block diagram of one preferred embodiment of a circuit
            suitable for use in the apparatus of       <figref idrefs="DRAWINGS">FIG. 1</figref>      ;
        </p>
        <p num="p-0013">
            <figref idrefs="DRAWINGS">FIG. 3</figref>       is a graph of results from a simulation comparing the
            present invention with the prior art;
        </p>
        <p num="p-0014">
            <figref idrefs="DRAWINGS">FIGS. 4A-4D</figref>       plot performance in a simulation of the present
            invention and the prior art; and
        </p>
        <p num="p-0015">
            <figref idrefs="DRAWINGS">FIGS. 5A-5D</figref>       show the output spectra from a simulation of power
            amplifiers in accordance with the present invention and the prior art.
        </p>
    </description-of-drawings>
    <?brief-description-of-drawings description="Brief Description of Drawings" end="tail"?>  <?DETDESC description="Detailed Description" end="lead"?>
    <heading>DETAILED DESCRIPTION OF PREFERRED EMBODIMENTS</heading>
    <p num="p-0016">
        <figref idrefs="DRAWINGS">FIG. 1</figref>     depicts an apparatus for generating an amplitude and phase
        predistorted radio frequency signal in accordance with a preferred embodiment of the present invention. A signal
        source     <b>10</b>     provides a complex baseband signal xe    <sup>jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        , where x is the envelope of the signal and, for example, may be an Edge GSM or a D8PSK signal. The signal
        includes an in-phase component I and a quadrature component Q that are normalized and sampled at, for example,
        10.5 kilosamples per second (KSPS). From source     <b>10</b>    , the samples are filtered in filter circuit     <b>
        12
    </b>     to produce smooth transitions between phase symbols. The samples I    <sub>k</sub>    of the in-phase
        component and the samples Q    <sub>k</sub>    of the quadrature component are applied from filter circuit     <b>
        12
    </b>     to a calculation circuit     <b>16</b>     which calculates the magnitude of the scaled complex baseband
        envelope sample, for example by determining the square root of the sum of the squares of the scaled in-phase
        component sample and the scaled quadrature component sample.
    </p>
    <p num="p-0017">
        <figref idrefs="DRAWINGS">FIG. 2</figref>     is a block diagram of one preferred embodiment of a calculation
        circuit for determining an approximation of the magnitude of each complex sample k of the baseband signal. In     <figref
            idrefs="DRAWINGS">FIG. 2
    </figref>     the samples I    <sub>k</sub>    of the in-phase component and the samples Q    <sub>k</sub>    of the
        quadrature component are applied to a first detection circuit     <b>18</b>     which determines the maximum of
        these samples by determining for each sample pair whether the I    <sub>k</sub>    sample or the Q    <sub>k
    </sub>    sample is the larger. The I    <sub>k</sub>    and the Q    <sub>k</sub>    samples are also applied to a
        second detection circuit     <b>20</b>     which determines the minimum of these samples by determining for each
        sample pair whether the I    <sub>k</sub>    sample or the Q    <sub>k</sub>    sample is the smaller. The
        detected maximum value (“max    <sub>k</sub>    ”) and the detected minimum value (“min    <sub>k</sub>    ”)
        for each sample pair are applied to calculating circuit     <b>22</b>     which computes the value y    <sub>k
    </sub>    =½ (min    <sub>k</sub>    /max    <sub>k</sub>    )    <sup>2</sup>    .
    </p>
    <p num="p-0018">The y    <sub>k</sub>    output from calculating circuit     <b>22</b>     is applied as an input to
        each of five multiplier circuits     <b>24</b>    ,     <b>26</b>    ,     <b>28</b>    ,     <b>30</b>     and     <b>
            32
        </b>    . The y    <sub>k</sub>    output is also applied to a second input of multiplier     <b>24</b>    . As
        a consequence, multiplier     <b>24</b>     provides as an output the value y    <sub>k</sub>
        <sup>2</sup>    . This y    <sub>k</sub>
        <sup>2</sup>    output from multiplier     <b>24</b>     is applied to the second input of multiplier     <b>
            26
        </b>     and to a negative input to summation circuit     <b>34</b>    . The output of multiplier     <b>26</b>     is
        thus the value y    <sub>k</sub>
        <sup>3</sup>    . This output is applied to the second input of multiplier     <b>28</b>     and to a positive
        input of summation circuit     <b>34</b>    . Multiplier     <b>28</b>     accordingly provides the output y    <sub>
            k
        </sub>
        <sup>4</sup>    which is used as the second input to multiplier     <b>30</b>     and which is applied to a
        negative input to summation circuit     <b>34</b>    . Multiplier     <b>30</b>     then provides the output y    <sub>
            k
        </sub>
        <sup>5</sup>    to the second input of multiplier     <b>32</b>     and to a positive input to summation circuit     <b>
            34
        </b>    . Multiplier     <b>32</b>     provides the output y    <sup>6</sup>    to a negative input to summation
        circuit     <b>34</b>    .
    </p>
    <p num="p-0019">Summation circuit     <b>34</b>     divides the sum of its inputs by 2, thus providing as its output
        the value ½(−y    <sub>k</sub>
        <sup>2</sup>    +y    <sub>k</sub>
        <sup>3</sup>    −y    <sub>k</sub>
        <sup>4</sup>    +y    <sub>k</sub>
        <sup>5</sup>    −y    <sub>k</sub>
        <sup>6</sup>    ). This signal is applied as an input to summation circuit     <b>36</b>    , which also
        receives as inputs the y    <sub>k</sub>    signal from calculation circuit     <b>22</b>     and the constant
        1. The output of summation circuit     <b>36</b>     is thus the value {    <b>1+y</b>
        <sub>k</sub>    +½(−y    <sub>k</sub>
        <sup>2</sup>    +y    <sub>k</sub>
        <sup>3</sup>    −y    <sub>k</sub>
        <sup>4</sup>    +y    <sub>k</sub>
        <sup>5</sup>    −y    <sub>k</sub>
        <sup>6</sup>    )}. This is equal to the value {(1+y    <sub>k</sub>    )/2+     <b>1</b>    /    <b>2</b>    (1+y    <sub>
            k
        </sub>    −y    <sub>k</sub>
        <sup>2</sup>    +y    <sub>k</sub>
        <sup>3</sup>    −y    <sub>k</sub>
        <sup>4</sup>    +y    <sub>k</sub>
        <sup>5</sup>    −y    <sub>k</sub>
        <sup>6</sup>    )}. This signal is applied from summation circuit     <b>36</b>     to one input of multiplier     <b>
            38
        </b>    , which receives the max    <sub>k</sub>    signal from detection circuit     <b>18</b>     at its
        second input. Consequently, the output of multiplier     <b>38</b>     is (max    <sub>k</sub>    )×{(1+y    <sub>
            k
        </sub>    )/2+½(1+y    <sub>k</sub>    −y    <sub>k</sub>
        <sup>2</sup>    +y    <sub>k</sub>
        <sup>3</sup>    −y    <sub>k</sub>
        <sup>4</sup>    +y    <sub>k</sub>
        <sup>5</sup>    −y    <sub>k</sub>
        <sup>6</sup>    )} which is an approximation of (I    <sup>k</sup>
        <sup>2</sup>    +Q    <sub>k</sub>
        <sup>2</sup>    )    <sup>1/2</sup>    and thus an approximation of the magnitude x    <sub>k</sub>    of the
        sample k.
    </p>
    <p num="p-0020">The output from the apparatus of     <figref idrefs="DRAWINGS">FIG. 1</figref>     is provided by
        power amplifier     <b>64</b>     to antenna     <b>66</b>    . Radio frequency coupler     <b>70</b>     couples
        a portion of that output to envelope detector     <b>72</b>    . The detected envelope is applied to
        analog-to-digital converter     <b>73</b>     which samples at a high sampling rate, shown in     <figref
                idrefs="DRAWINGS">FIG. 1
        </figref>     as a sampling rate of     <b>50</b>     megasamples per second (MSPS). The output of
        analog-to-digital converter     <b>73</b>     is normalized by normalizing circuit     <b>74</b>     so that its
        maximum valve equals 1. The output of calculation circuit     <b>16</b>     is applied through delay circuit     <b>
            76
        </b>     to a positive input of summing circuit     <b>78</b>    , while the output from normalizing circuit     <b>
            74
        </b>     is applied to a negative input of the summing circuit. The input to summing circuit     <b>78</b>     from
        calculation circuit     <b>16</b>     represents the envelope before distortion, while the input to summing
        circuit     <b>78</b>     from normalizing circuit     <b>74</b>     represents the envelope after distortion.
        Delay circuit     <b>76</b>     assures that each undistorted sample is summed with the normalized output
        resulting from that same sample. The resulting signal from summing circuit     <b>78</b>     is applied to one
        input of multiplier     <b>80</b>     which receives a weighting factor of−λ at its second input. The output
        from multiplier     <b>80</b>     is applied to one input of multiplying circuit     <b>82</b>     which
        receives the output from normalizing circuit     <b>74</b>     at its second input. The output from multiplying
        circuit     <b>82</b>     is applied through low pass filter     <b>84</b>     to sampler     <b>86</b>     which
        applies samples of that output at periodic intervals of, for example, one minute to integrator     <b>88</b>    .
        The output of integrator     <b>88</b>     is a scaling factor C and is applied to one input of multiplying
        circuit     <b>90</b>     which receives the x    <sub>k</sub>    outputs from calculation circuit     <b>16</b>     at
        its second input. The output of multiplier circuit     <b>90</b>     is thus Cx    <sub>k</sub>    .
    </p>
    <p num="p-0021">The Cx    <sub>k</sub>    output from multiplier circuit     <b>90</b>     is applied as an input to
        calculation circuit     <b>40</b>     which determines the value of (atanh (Cx    <sub>k</sub>    ))/Cx    <sub>
            k
        </sub>    ). By way of an example, calculation circuit     <b>40</b>     might be a lookup table having values
        to 16 bits for determining a value x    <sub>k</sub>
        <sup>2</sup>    /3+x    <sub>k</sub>
        <sup>4</sup>    /5+x    <sub>k</sub>
        <sup>6</sup>    /7+ . . . which is an approximation of the value {(atanh (x    <sub>k</sub>    ))/x    <sub>k
        </sub>    }−1. The output of the lookup table then is applied to one input of a summation circuit which receives
        the constant 1 at its second input so as to provide an approximation of (atanh (x    <sub>k</sub>    ))/x    <sub>
            k
        </sub>    . It is preferred that calculation circuit     <b>40</b>    , when in the form of a lookup table,
        compute the value of the segment {(atanh (x    <sub>k</sub>    ))/x    <sub>k</sub>    }−1, and that the
        constant 1 be added by a summation circuit in order to provide the desired accuracy while maintaining the lookup
        table of a moderate size.
    </p>
    <p num="p-0022">The x    <sub>k</sub>    output from calculation circuit     <b>16</b>     is also applied as an
        input to multiplier     <b>92</b>     which receives the value π/6 at its second input. The C x    <sub>k</sub>    output
        from multiplier circuit     <b>90</b>     is applied to calculation circuit     <b>94</b>     which calculates
        the value tanh(Cx    <sub>k</sub>    ) and applies that value to an input of multiplier     <b>96</b>    .
        Calculation circuit     <b>94</b>     might be a lookup table, for example. The second input of multiplier     <b>
            96
        </b>     receives the value πx    <sub>k</sub>    /6 from multiplier     <b>92</b>    . The output of multiplier     <b>
            96
        </b>     is thus (πx    <sub>k</sub>    tanh(Cx    <sub>k</sub>    ))/6=Φ    <sub>k</sub>    . This value is
        applied to lookup table     <b>98</b>     which provides as outputs the values and Q    <sub>k</sub>    ′=−sin(Φ    <sub>
            k
        </sub>    ). These values are applied to inputs of multiplier pair     <b>100</b>     which receives the output
        of lookup table     <b>40</b>     at its second input.
    </p>
    <p num="p-0023">The output of multiplier circuit     <b>100</b>     is thus the distortion factor {(atanh(Cx    <sub>
        k
    </sub>    ))/Cx    <sub>k</sub>    }e    <sup>−jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        =D    <sub>k</sub>    . This output is applied to one input of multiplier pair     <b>44</b>    . The samples I    <sub>
            k
        </sub>    of the in-phase component and the samples Q    <sub>k</sub>    of the quadrature component are also
        applied to multiplier pair     <b>44</b>    . Each sample of the in-phase component and the quadrature component
        is thus modified by the respective distortion factor D    <sub>k</sub>    , so that the output of multiplier
        pair     <b>44</b>     is x    <sub>k</sub>    e    <sup>jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        {(atanh (Cx    <sub>k</sub>    ))/Cx    <sub>k</sub>    }e    <sup>−jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        =D    <sub>k</sub>    x    <sub>k</sub>    e    <sup>−jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        . These samples of the modified signal are resampled in resampling circuit     <b>46</b>     at the same
        sampling rate as in analog-to-digital converter     <b>73</b>    , shown in     <figref idrefs="DRAWINGS">FIG.
            1
        </figref>     as a resampling rate at 50 MSPS.
    </p>
    <p num="p-0024">The resampled output from resampling circuit     <b>46</b>     is applied to multiplier pair     <b>
        48
    </b>    . Signal generator     <b>50</b>     provides an intermediate frequency signal of a frequency less than half
        the sampling rate of resampling circuit     <b>46</b>    , shown in     <figref idrefs="DRAWINGS">FIG. 1
        </figref>     as a frequency of 17 MHz. Sampling circuit     <b>52</b>     samples the sine and cosine outputs
        from signal generator     <b>50</b>     at the same sampling rate as resampling circuit     <b>46</b>    , shown
        in     <figref idrefs="DRAWINGS">FIG. 1</figref>     as a sampling rate of 50 MSPS. These sampled sine and
        cosine signals are applied to multiplier pair     <b>48</b>     so that the multiplier pair provides as outputs
        the intermediate frequency signals D    <sub>k</sub>    ×I    <sub>k</sub>    sin 17 MHz and D    <sub>k</sub>    ×I    <sub>
            k
        </sub>    cos 17 MHz. These signals are added in summation circuit     <b>54</b>    , and the resulting
        predistorted, upconverted intermediate frequency signal is applied on line 56 to digital-to-analog converter     <b>
            58
        </b>     which samples at the same 50 MSPS rate as resampling circuit     <b>46</b>    .
    </p>
    <p num="p-0025">The output from digital-to-analog converter     <b>58</b>     is applied to band pass filter     <b>
        60
    </b>     which is centered at the 17 MHz frequency of signal source     <b>50</b>     and which has a bandwidth
        sufficient to avoid distortion of the predistorted envelope, for example a bandwidth of 1 MHz. The output from
        bandpass filter     <b>60</b>     is upconverted to a radio frequency in upconverter     <b>62</b>     and
        passed through driver amplifier     <b>68</b>     and power amplifier     <b>64</b>     to antenna     <b>66</b>    .
        If desired, a radio frequency attenuator could be utilized, rather than upconverter     <b>62</b>     and driver
        amplifier     <b>68</b>    . Power amplifier     <b>64</b>     has a transfer function C and hyperbolic tangent
        distortion so that the output of power amplifier     <b>64</b>     is bctanh (xe    <sup>jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        e    <sup>−jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        e    <sup>jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        tanh    <sup>−1</sup>    (cx))/cx=bcxe    <sup>jφ</sup>
        <sup>
            <sub2>k</sub2>
        </sup>
        , where b is the power amplifier gain.
    </p>
    <p num="p-0026">The feedback circuit of     <figref idrefs="DRAWINGS">FIG. 1</figref>     results in the signal C
        that is applied from integrator     <b>88</b>     to multiplier     <b>90</b>     converging to the current
        value of the transfer function C of output amplifier     <b>64</b>    . It is possible to set the gain of the
        feedback loop so that it converges in just a few iterations. The value of the feedback gain −λ which guarantees
        stable conversion is upper bounded by the mean square value of the feedback envelope after being normalized by
        circuit     <b>74</b>    .
    </p>
    <p num="p-0027">Predistorting the digital envelope of the baseband signal before upconversion to the radio
        frequency, followed by digital-to-analog conversion, in accordance with the present invention avoids impressing
        of analog pickup noise directly on the transmitted envelope, as would occur if the envelope correction were
        performed on the radio frequency analog signal. Implementation of the present invention does not require
        significant hardware. It can be accomplished in software or firmware. Implementation on a gate array, such as a
        field programmable gate array, is convenient.
    </p>
    <p num="p-0028">
        <figref idrefs="DRAWINGS">FIG. 3</figref>     is a plot of power amplifier output as a function of signal input
        for (1) a computer simulated system in accordance with the present invention with the scaling factor C=0.7, (2)
        a computer simulated system utilizing the cuber function f(x) =x+x    <sup>3</sup>    /3, and (3) a computer
        simulated system utilizing the functions f(x) =2 x/(1 +x    <sup>2</sup>    ) and ph(x)=2πx/6(1 +x    <sup>2
    </sup>    ), showing the superiority of the present invention.
    </p>
    <p num="p-0029">
        <figref idrefs="DRAWINGS">FIGS. 4A-4D</figref>     are quadrature amplitude modulation plots.     <figref
            idrefs="DRAWINGS">FIG. 4A
    </figref>     plots the computer simulated output of a linear power amplifier.     <figref idrefs="DRAWINGS">FIG.
        4B
    </figref>     plots the computer simulated output of a non-linear power amplifier with no predistortion, but with
        hyperbolic tangent nonlinearity in phase and amplitude.     <figref idrefs="DRAWINGS">FIG. 4C</figref>     plots
        the computer simulated output of such a nonlinear power amplifier with predistortion based on the cuber function
        f(x)=x+x    <sup>3</sup>    /3.     <figref idrefs="DRAWINGS">FIG. 4D</figref>     plots the computer simulated
        output of such a nonlinear power amplifier with predistortion in accordance with the present invention. As can
        be seen, the plot for the present invention in     <figref idrefs="DRAWINGS">FIG. 4D</figref>     is
        substantially the same as the plot for a linear power amplifier in     <figref idrefs="DRAWINGS">FIG. 4A
    </figref>    , while the plots of     <figref idrefs="DRAWINGS">FIGS. 4B and 4C</figref>     are not, again showing
        the superiority of the present invention.
    </p>
    <p num="p-0030">
        <figref idrefs="DRAWINGS">FIG. 5A</figref>     shows the computer simulated output spectrum of a linear power
        amplifier.     <figref idrefs="DRAWINGS">FIG. 5B</figref>     is the computer simulated output spectrum of a
        nonlinear power amplifier.     <figref idrefs="DRAWINGS">FIG. 5C</figref>     is the computer simulated output
        spectrum of such a nonlinear power amplifier with predistortion based on the cuber function f(x)=x+x    <sup>3
    </sup>    /3.     <figref idrefs="DRAWINGS">FIG. 5D</figref>     is the computer simulated output spectrum of such a
        nonlinear power amplifier with predistortion in accordance with the present invention with the scaling factor
        C=0.7. The simulated output spectrum of the present invention most nearly matches that of a linear power
        amplifier, once more showing the superiority of the present invention.
    </p>
    <p num="p-0031">Although the present invention has been described with reference to preferred embodiments, various
        alterations, rearrangements, and substitutions could be made, and still the result would be within the scope of
        the invention.
    </p>
    <?DETDESC description="Detailed Description" end="tail"?>
</description>