<description lang="EN" load-source="patent-office" mxw-id="PDES301463871">
    <invention-title id="en-title1" lang="EN">Desk lamp and system</invention-title>
    <technical-field>
        <p id="p0001">Technical Field</p>
        <p id="p0002" num="0001">The utility model relates to the field of lighting, in particular to desk lamp and system.</p>
    </technical-field>
    <background-art>
        <p id="p0003">Background</p>
        <p id="p0004" num="0002">Under the background of the era of internet of things, intelligent life and interconnected life become mainstream in the field of home furnishing. The user can carry out remote real time control to equipment, and this requires more equipment to be able to insert the internet, and traditional desk lamp work is comparatively independent, and the integrated level is low, and the on-off light control must come before the lamp, can not satisfy modern people to efficient requirement.</p>
        <p id="p0005" num="0003">In view of this, the present application is presented.</p>
    </background-art>
    <disclosure>
        <p id="p0006">SUMMERY OF THE UTILITY MODEL</p>
        <p id="p0007" num="0004">The utility model discloses a desk lamp and system aims at solving the unable problem that realizes long-range breaking of current desk lamp.</p>
        <p id="p0008" num="0005">The utility model discloses a first embodiment provides a desk lamp, include: the device comprises a controller, a wireless module, a power supply module and a bulb;</p>
        <p id="p0009" num="0006">the controller is electrically connected with the wireless module, the controller is electrically connected with the bulb through a control circuit, the power supply module is electrically connected with the controller, the wireless module and the bulb, and the wireless module is used for carrying out wireless communication with a terminal so as to receive a control instruction of the terminal.</p>
        <p id="p0010" num="0007">Preferably, the power module comprises an adapter, a charge and discharge management circuit and an energy storage module;</p>
        <p id="p0011" num="0008">the adapter is electrically connected with the input end of the charging and discharging management circuit, the output end of the charging and discharging management circuit is electrically connected with the energy storage module, the control end of the charging and discharging management circuit is electrically connected with the IIC interface of the controller, and the energy storage module is electrically connected with the power supply input end of the controller.</p>
        <p id="p0012" num="0009">Preferably, the control circuit comprises a relay;</p>
        <p id="p0013" num="0010">the coil of the relay is electrically connected with the output end of the controller, the output end of the charge and discharge management circuit is electrically connected with the normally open contact of the relay, and the normally open contact of the relay is electrically connected with the bulb.</p>
        <p id="p0014" num="0011">Preferably, the controller is of the model STM32L 072.</p>
        <p id="p0015" num="0012">Preferably, the wireless module is a WIFI module.</p>
        <p id="p0016" num="0013">A second embodiment of the utility model provides a system, reach as above arbitrary one including the terminal a desk lamp, wherein, the terminal through with wireless module carries out the communication, and then with the controller is mutual.</p>
        <p id="p0017" num="0014">Based on the utility model provides a pair of desk lamp and system, the controller passes through wireless module receives the control command at terminal to control command output to control circuit's control end, with control the switch of bulb has solved the problem of unable remote switch desk lamp among the prior art.</p>
    </disclosure>
    <description-of-drawings>
        <p id="p0018">Drawings</p>
        <p id="p0019" num="0015">Fig. 1 an embodiment of the present invention provides a desk lamp module schematic.</p>
    </description-of-drawings>
    <mode-for-invention>
        <p id="p0020">Detailed Description</p>
        <p id="p0021" num="0016">To make the objects, technical solutions and advantages of the embodiments of the present invention clearer, the drawings of the embodiments of the present invention are combined to clearly and completely describe the technical solutions of the embodiments of the present invention, and obviously, the described embodiments are some embodiments of the present invention, not all embodiments. Based on the embodiments in the present invention, all other embodiments obtained by a person skilled in the art without creative work belong to the protection scope of the present invention. Thus, the following detailed description of the embodiments of the present invention, presented in the accompanying drawings, is not intended to limit the scope of the invention, as claimed, but is merely representative of selected embodiments of the invention. Based on the embodiments in the present invention, all other embodiments obtained by a person skilled in the art without creative work belong to the protection scope of the present invention.</p>
        <p id="p0022" num="0017">In the description of the present invention, it is to be understood that the terms "center", "longitudinal", "lateral", "length", "width", "thickness", "upper", "lower", "front", "rear", "left", "right", "vertical", "horizontal", "top", "bottom", "inner", "outer", "clockwise", "counterclockwise", and the like indicate orientations or positional relationships based on the orientations or positional relationships shown in the drawings, and are only for convenience of description and to simplify the description, but do not indicate or imply that the device or element referred to must have a particular orientation, be constructed and operated in a particular orientation, and therefore should not be construed as limiting the present invention.</p>
        <p id="p0023" num="0018">In the present invention, unless otherwise expressly stated or limited, the terms "mounted," "connected," and "fixed" are to be construed broadly and may, for example, be fixedly connected, detachably connected, or integrally formed; can be mechanically or electrically connected; either directly or indirectly through intervening media, either internally or in any other relationship. The specific meaning of the above terms in the present invention can be understood according to specific situations by those skilled in the art.</p>
        <p id="p0024" num="0019">In the present disclosure, unless expressly stated or limited otherwise, the first feature "on" or "under" the second feature may comprise direct contact between the first and second features, or may comprise contact between the first and second features not directly. Also, the first feature being "on," "above" and "over" the second feature includes the first feature being directly on and obliquely above the second feature, or merely indicating that the first feature is at a higher level than the second feature. A first feature being "under," "below," and "beneath" a second feature includes the first feature being directly under and obliquely below the second feature, or simply meaning that the first feature is at a lesser elevation than the second feature.</p>
        <p id="p0025" num="0020">The following detailed description of the embodiments of the present invention will be made with reference to the accompanying drawings.</p>
        <p id="p0026" num="0021">The utility model discloses a desk lamp and system aims at solving the unable problem that realizes long-range breaking of current desk lamp.</p>
        <p id="p0027" num="0022">Referring to fig. 1, a first embodiment of the present invention provides a desk lamp, including: the device comprises a controller 1, a wireless module 2, a power supply module and a bulb 4;</p>
        <p id="p0028" num="0023">the controller 1 is electrically connected with the wireless module 2, the controller 1 is electrically connected with the bulb 4 through the control circuit 3, the power supply module is electrically connected with the controller 1, the wireless module 2 and the bulb 4, and the wireless module 2 is used for carrying out wireless communication with a terminal so as to receive a control instruction of the terminal.</p>
        <p id="p0029" num="0024">It should be noted that, in the prior art, the circuit is generally switched on or off by a fixed switch to control the on/off of the lamp 4, however, as technology advances, the simple on/off of the circuit cannot meet the requirement of high efficiency.</p>
        <p id="p0030" num="0025">In this embodiment, the wireless module 2 is configured on the controller 1 to receive a control instruction transmitted from the terminal, and then output the control instruction to the control loop, so as to implement a loop for turning on and off the bulb 4 remotely, specifically, the wireless module 2 is electrically connected to the controller 1 through an SDIO interface, in this embodiment, the wireless module 2 may be a WIFI module, wherein the WIFI module may be an AP6181 WIFI single frequency module, and may support various operating systems such as Android, Linux, and RTOS. It can provide wireless modem function and can directly utilize sequence spread spectrum and OFDM/CCK technology, integrate IEEE 802.11b/g/n MAC, baseband, radio frequency and power amplifier, power management device, SDIO 2.0 or SPI interface. Of course, in other embodiments, the wireless module 2 may also be a 4G/5G or NB-iot module, which is not limited herein, but these solutions are all within the protection scope of the present invention.</p>
        <p id="p0031" num="0026">It should be understood that the table lamp is provided with the WIFI module, which can send out a wireless signal, the terminal can be connected to the wireless signal sent out by the WIFI module, and then sends a control instruction to the controller 1, and the controller 1 can turn on or off the loop of the bulb 4 according to the control instruction.</p>
        <p id="p0032" num="0027">In this embodiment, the power module includes an adapter 6, a charging and discharging management circuit 5, and an energy storage module 7;</p>
        <p id="p0033" num="0028">the adapter 6 is electrically connected with the input end of the charge and discharge management circuit 5, the output end of the charge and discharge management circuit 5 is electrically connected with the energy storage module 7, the control end of the charge and discharge management circuit 5 is electrically connected with the IIC interface of the controller 1, and the energy storage module 7 is electrically connected with the power input end of the controller 1.</p>
        <p id="p0034" num="0029">It should be noted that the adapter 6 is used for connecting an external power supply, and is connected to the energy storage module 7 through the charge and discharge management circuit 5, wherein the charge management circuit may include a power management chip, the power management chip may support an input operating voltage of 3.5V to 24V, an output voltage of 1.024V to 19.2V, and may support USB2.0, USB 3.0, USB 3.1(C type), and USB _ PD input current settings, and an integrated ADC is used for monitoring voltage, current, and power. The IIC interface is integrated for external communication, and has battery overvoltage protection and MOSFET inductor overcurrent protection, in other embodiments, other types of power management chips can be adopted, and are not limited specifically, wherein the energy storage module 7 can be a lithium battery with a capacity of 3000mAh and an output voltage of 12V, but is not limited thereto.</p>
        <p id="p0035" num="0030">In the present embodiment, the control circuit 3 includes a relay;</p>
        <p id="p0036" num="0031">the coil of the relay is electrically connected with the output end of the controller 1, the output end of the charge and discharge management circuit 5 is electrically connected with the normally open contact of the relay, and the normally open contact of the relay is electrically connected with the bulb 4.</p>
        <p id="p0037" num="0032">It should be noted that the normally open contact of the relay is used for being connected to the output end of the energy storage module 7 and the bulb 4, and it can be understood that the normally open contact is equivalent to a switch, and is connected to the power supply and the bulb 4, so that the normally open contact and the bulb 4 form a loop, where the controller 1 can output a TTL level signal to a coil of the relay, specifically, when the coil receives a high level, the loop is on, and when the coil receives a low level, the loop is off. In this embodiment, the relay may be of the type HF49FD, which supports 5A maximum switching current, 250VAC/30VDC maximum switching voltage, 1250VA/150W maximum switching power, 5 × 10^4 times battery durability, high sensitivity, and power consumption of only 0.12W.</p>
        <p id="p0038" num="0033">In this embodiment, the controller 1 may be of a model STM32L 072.</p>
        <p id="p0039" num="0034">It should be noted that, the controller 1 consumes ultra-low power, and its highest operating frequency may be 48MHz, and is configured with 256K flash memory, 64K bytes SRAM, and multiple 12-bit analog-to-digital converters, and has multiple bidirectional I/O ports, and supports timers, ADCs, DACs, SPIs, USARTs, and SWD and JTAG interfaces, and of course, in other embodiments, the controller 1 may also be other types of models, which is not limited herein, but these schemes are all within the protection scope of the present invention.</p>
        <p id="p0040" num="0035">In this embodiment, the controller 1 may further be electrically connected to a voice recognition module, and pre-recorded voices, specifically, for example, "turn on a light", "turn off a light", are stored in the controller 1; after the voice recognition module recognizes the command of turning on and turning off the light, the controller 1 outputs a corresponding control signal to the control circuit 3 to turn on and off the circuit of the bulb 4.</p>
        <p id="p0041" num="0036">A second embodiment of the utility model provides a system, reach as above arbitrary one including the terminal a desk lamp, wherein, the terminal through with wireless module 2 carries out the communication, and then with controller 1 carries out alternately.</p>
        <p id="p0042" num="0037">Based on the utility model provides a pair of desk lamp and system, controller 1 passes through wireless module 2 receives the control command at terminal to control command output to control circuit 3's control end, with control the problem of unable remote switch desk lamp among the prior art has been solved to the switch of bulb 4.</p>
        <p id="p0043" num="0038">Above only the utility model discloses an it is preferred embodiment, the utility model discloses a scope of protection not only limits in above-mentioned embodiment, and the all belongs to the utility model discloses a technical scheme under the thinking all belongs to the utility model discloses a scope of protection.</p>
    </mode-for-invention>
</description>