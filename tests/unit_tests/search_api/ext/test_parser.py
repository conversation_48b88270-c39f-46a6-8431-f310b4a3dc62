# Third party imports
import pytest

import app.ext.boolean_search.boolean_operators as operators
import app.ext.boolean_search.expression as expression
from app.ext.boolean_search import parser_utils
# Local imports
from app.ext.boolean_search.exceptions import ParsingException, LexerException


class TestParserTerm:
    def test_parser_empty_query(self, boolean_parser_family):
        query = ''
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_rejects_query_without_term(self, boolean_parser_family):
        query = 'SomeText'
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_rejects_alone_names(self, boolean_parser_family):
        query = 'IPC=1 OR 2'  # "2" has no term so should fail
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_incorrect_term(self, boolean_parser_family):
        query = 'WRONG=John'
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_incorrect_name(self, boolean_parser_family):
        query = 'CPC=%^$%'
        with pytest.raises(LexerException):
            boolean_parser_family.parser.parse(query)

    def test_parser_unicode(self, boolean_parser_family):
        query = 'TITLE=Würmer am schönen Ährensee'
        expected = 'default_term:Würmer am schönen Ährensee'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert expected == str(parsed_query)


class TestParserIPC:
    def test_parser_term_ipc_case(self, boolean_parser_publication):
        query = 'Ipc=John'
        with pytest.raises(ParsingException):
            boolean_parser_publication.parser.parse(query)

    def test_parser_single_term_ipc(self, boolean_parser_family):
        query = 'IPC=1'
        expected = 'default_term:1'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_compound_term_ipc(self, boolean_parser_family):
        query = 'IPC=1 AND IPC=2'
        expected = 'default_term:1 AND default_term:2'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert type(parsed_query.left_expression) is expression.Term
        assert type(parsed_query.right_expression) is expression.Term
        assert expected == str(parsed_query)


class TestParserCPC:
    def test_parser_term_cpc_case(self, boolean_parser_family):
        query = 'Cpc=John'
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_single_term_cpc(self, boolean_parser_family):
        query = 'CPC=1'
        expected = 'default_term:1'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_compound_term_cpc(self, boolean_parser_publication):
        query = 'CPC=1 AND CPC=2'
        expected = 'default_term:1 AND default_term:2'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_publication.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert type(parsed_query.left_expression) is expression.Term
        assert type(parsed_query.right_expression) is expression.Term
        assert expected == str(parsed_query)


class TestParserFullText:
    def test_parser_full_text(self, boolean_parser_family):
        query = 'TEXT=Good morning'
        expected = 'default_term:Good morning'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_full_text_parenthesis(self, boolean_parser_family):
        query = 'TEXT=(Good morning OR Good evening)'
        expected = '(description:Good morning OR description:Good evening)' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Parenthesis
        assert expected == str(parsed_query)


class TestParserDate:
    def test_parser_date(self, boolean_parser_family):
        query = 'PUBLICATION_DATE=1999-03-01 OR PRIORITY_DATE=1999'
        expected = 'default_term:1999-03-01 OR default_term:1999'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert expected == str(parsed_query)


class TestParserPatentNumber:
    def test_parser_date(self, boolean_parser_family):
        query = 'RAW_PUBLICATION_NUMBER=EP2174077 OR ALSO_PUBLISHED_AS=2001087'
        expected = 'default_term:EP2174077 OR default_term:2001087'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert expected == str(parsed_query)


class TestParserApplicationNumber:
    def test_parser_application_numbers(self, boolean_parser_family):
        query = 'APPLICATION_NUMBERS=AU5169585A'
        expected = 'default_term:AU5169585A'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert expected == str(parsed_query)

    def test_parser_multiple_application_numbers(self, boolean_parser_family):
        query = 'APPLICATION_NUMBERS=AU5169585A OR APPLICATION_NUMBERS=CA497893A'
        expected = 'default_term:AU5169585A OR default_term:CA497893A'.replace('default_term',
                                                                               parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert expected == str(parsed_query)

    def test_parser_application_number(self, boolean_parser_publication):
        query = 'APPLICATION_NUMBERS=AU5169585A'
        expected = 'default_term:AU5169585A'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_publication.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert expected == str(parsed_query)


class TestParserApplicant:
    def test_parser_term_applicant_case(self, boolean_parser_family):
        query = 'Applicant=John'
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_single_term_applicant(self, boolean_parser_family):
        query = 'APPLICANTS=John'
        expected = 'default_term:John'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_compound_term_applicant(self, boolean_parser_family):
        query = 'APPLICANTS=John Doe'
        expected = 'default_term:John Doe'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)


class TestParserQuotes:
    def test_parser_unclosed_quotes(self, boolean_parser_family):
        query = 'APPLICANTS="John Deere'
        with pytest.raises(LexerException):
            boolean_parser_family.parser.parse(query)

    def test_parser_term_quotes(self, boolean_parser_family):
        query = 'APPLICANTS="Stonkus and Sons GmbH"'
        expected = 'default_term:"Stonkus and Sons GmbH"'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_term_quotes_compound(self, boolean_parser_family):
        query = 'APPLICANTS=("Stonkus and Sons GmbH" OR "John Deere" OR "Procter and Gamble")'
        expected = '(description:"Stonkus and Sons GmbH" OR description:"John Deere" OR ' \
                   'description:"Procter and Gamble")' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert expected == str(parsed_query)

    def test_parser_term_quotes_special_chars(self, boolean_parser_family):
        query = 'APPLICANTS=("BIO-ON")'
        expected = '(description:"BIO-ON")'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert expected == str(parsed_query)


class TestParserAndOperator:
    def test_parser_and_operator(self, boolean_parser_family):
        query = 'APPLICANTS=John AND APPLICANTS=Doe'
        expected = 'default_term:John AND default_term:Doe'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert type(parsed_query.operator) is operators.And
        assert type(parsed_query.left_expression) is expression.Term
        assert type(parsed_query.right_expression) is expression.Term
        assert type(parsed_query.left_expression.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_and_operator_case(self, boolean_parser_publication):
        query = 'APPLICANTS=John And Doe'
        expected = 'default_term:John And Doe'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_publication.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)


class TestParserOrOperator:
    def test_parser_or_operator(self, boolean_parser_family):
        query = 'APPLICANTS=John OR APPLICANTS=Doe'
        expected = 'default_term:John OR default_term:Doe'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert type(parsed_query.operator) is operators.Or
        assert type(parsed_query.left_expression) is expression.Term
        assert type(parsed_query.right_expression) is expression.Term
        assert type(parsed_query.left_expression.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_or_operator_case(self, boolean_parser_family):
        query = 'APPLICANTS=John Or Doe'
        expected = 'default_term:John Or Doe'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)


class TestParserNotOperator:
    def test_parser_not_operator(self, boolean_parser_publication):
        query = 'NOT APPLICANTS=John'
        expected = '(NOT default_term:John)'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_publication.parser.parse(query)
        assert type(parsed_query) is expression.Not
        assert type(parsed_query.expression) is expression.Term
        assert expected == str(parsed_query)

    def test_parser_not_operator_case(self, boolean_parser_family):
        query = 'Not APPLICANT=John'
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_not_op(self, boolean_parser_family):
        query = 'APPLICANTS<>VOLKSWAGEN'
        query_ = 'NOT(APPLICANTS=VOLKSWAGEN)'
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query_ = boolean_parser_family.parser.parse(query_)
        assert str(parsed_query) == str(parsed_query_)

    def test_parser_not_op_parenthesis(self, boolean_parser_family):
        query = 'APPLICANTS<>(VOLKSWAGEN)'
        query_ = 'NOT(APPLICANTS=VOLKSWAGEN)'
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query_ = boolean_parser_family.parser.parse(query_)
        assert str(parsed_query) == str(parsed_query_)


class TestParserParenthesis:
    def test_parser_missing_parenthesis(self, boolean_parser_family):
        query = '(WRONG=John'
        with pytest.raises(ParsingException):
            boolean_parser_family.parser.parse(query)

    def test_parser_single_term_parenthesis(self, boolean_parser_family):
        query = 'CPC=(John)'
        expected = '(default_term:John)'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Parenthesis
        assert expected == str(parsed_query)

    def test_parser_full_expression_parenthesis(self, boolean_parser_family):
        query = '(CPC=John)'
        expected = '(default_term:John)'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Parenthesis
        assert type(parsed_query.expression) is expression.Term
        assert expected == str(parsed_query)

    def test_parser_double_parenthesis(self, boolean_parser_family):
        query = '(CPC=(John))'
        expected = '((default_term:John))'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Parenthesis
        assert type(parsed_query.expression) is expression.Term
        assert type(parsed_query.expression.expression) is expression.Parenthesis
        assert expected == str(parsed_query)

    def test_parser_many_parenthesis(self, boolean_parser_family):
        query = '(CPC=(((John))))'
        expected = '((((default_term:John))))'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Parenthesis
        assert type(parsed_query.expression) is expression.Term
        assert type(parsed_query.expression.expression) is expression.Parenthesis
        assert type(parsed_query.expression.expression.expression) is expression.Parenthesis
        assert type(parsed_query.expression.expression.expression.expression) is expression.Parenthesis
        assert expected == str(parsed_query)

    def test_parser_not_parenthesis(self, boolean_parser_family):
        query = 'NOT (CPC=John)'
        expected = '(NOT (default_term:John))'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Not
        assert type(parsed_query.expression) is expression.Parenthesis
        assert type(parsed_query.expression.expression) is expression.Term
        assert expected == str(parsed_query)

    def test_parser_not_double_parenthesis(self, boolean_parser_family):
        query = '(NOT (CPC=John))'
        expected = '((NOT (default_term:John)))'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Parenthesis
        assert type(parsed_query.expression) is expression.Not
        assert type(parsed_query.expression.expression) is expression.Parenthesis
        assert expected == str(parsed_query)

    def test_parser_boolean_operator_parenthesis(self, boolean_parser_family):
        query = '(APPLICANTS=John OR APPLICANTS=Doe)'
        expected = '(default_term:John OR default_term:Doe)'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Parenthesis
        assert type(parsed_query.expression) is expression.BooleanExpression
        assert type(parsed_query.expression.operator) is operators.Or
        assert type(parsed_query.expression.left_expression) is expression.Term
        assert type(parsed_query.expression.right_expression) is expression.Term
        assert expected == str(parsed_query)

    def test_parser_boolean_operator_double_parenthesis(self, boolean_parser_family):
        query = '(CPC=(John OR Doe))'
        expected = '((default_term:John OR default_term:Doe))'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Parenthesis
        assert type(parsed_query.expression) is expression.Term
        assert type(parsed_query.expression.expression) is expression.Parenthesis
        assert expected == str(parsed_query)


class TestParserComplex:
    def test_parser_complex_query(self, boolean_parser_family):
        query = 'APPLICANTS=(BAYERISCHE AND SIEMENS) AND (NOT (IPC=A07C7/02 AND CPC=(A377/02))) ' \
                'OR APPLICANTS=Dennemeyer Octimine'
        expected = '(default_term:BAYERISCHE AND default_term:SIEMENS) AND ((NOT (default_term:A07C7/02 AND ' \
                   '(default_term:A377/02)))) OR default_term:Dennemeyer Octimine'.replace('default_term',
                                                                                           parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert str(parsed_query) == expected
        assert type(parsed_query) is expression.BooleanExpression

        # APPLICANTS=(BAYERISCHE AND SIEMENS) AND (NOT (IPC=(A07C7/02 AND CPC=(A377/02))) ' \
        #            'OR APPLICANTS=Dennemeyer Octimine
        applicants = parsed_query.left_expression
        assert type(applicants) is expression.Term

        # Left main tree
        # APPLICANTS=(BAYERISCHE AND SIEMENS)
        applicants_parenthesis = applicants.expression
        assert type(applicants_parenthesis) is expression.Parenthesis

        # APPLICANTS=BAYERISCHE AND SIEMENS
        applicants_term = applicants_parenthesis.expression
        assert type(applicants_term) is expression.BooleanExpression
        assert type(applicants_term.operator) is operators.And

        # BAYERISCHE
        assert type(applicants_term.left_expression) is expression.Name
        # SIEMENS
        assert type(applicants_term.right_expression) is expression.Name

        # Right main tree
        # (NOT (IPC=A07C7/02 AND CPC=(A377/02))) OR APPLICANTS=Dennemeyer Octimine'
        or_op = parsed_query.right_expression
        assert type(or_op) is expression.BooleanExpression
        assert type(or_op.operator) is operators.Or

        # Left subtree
        # (NOT (IPC=A07C7/02 AND CPC=(A377/02)))
        parenthesis = or_op.left_expression
        assert type(parenthesis) is expression.Parenthesis

        # NOT (IPC=A07C7/02 AND CPC=(A377/02))
        not_op = parenthesis.expression
        assert type(not_op) is expression.Not

        # (IPC=A07C7/02 AND CPC=(A377/02))
        parenthesis = not_op.expression
        assert type(parenthesis) is expression.Parenthesis

        # IPC=A07C7/02 AND CPC=(A377/02)
        and_op = parenthesis.expression
        assert type(and_op) is expression.BooleanExpression
        assert type(and_op.operator) is operators.And

        # A07C7/02
        ipc_term = and_op.left_expression
        assert type(ipc_term) is expression.Term

        # CPC=(A377/02)
        cpc_term = and_op.right_expression
        assert type(cpc_term) is expression.Term

        # (A377/02)
        parenthesis = cpc_term.expression
        assert type(parenthesis) is expression.Parenthesis

        # A377/02
        name = parenthesis.expression
        assert type(name) is expression.Name

        # Right subtree
        # OR APPLICANTS=Dennemeyer Octimine
        applicants_term = or_op.right_expression
        assert type(applicants_term) is expression.Term

        # Dennemeyer Octimine
        name = applicants_term.expression
        assert type(name) is expression.Name

    def test_analytics(self, boolean_parser_family):
        query = 'MARKET_COVERAGE=01 AND NOT (CITATION_BACKWARD_COUNT=1 OR CITATION_FORWARD_COUNT=1) ' \
                'AND TECHNOLOGY_BROADNESS=0 AND RISK=0'
        parsed_query = boolean_parser_family.parser.parse(query)
        expected = 'description:01 AND (NOT (description:1 OR description:1)) AND description:0 AND description:0'
        assert expected == str(parsed_query)


class TestComparisonOperators:
    def test_simple_comparison(self, boolean_parser_family):
        query = "IMPACT>3 AND RISK<=1"
        parsed_query = boolean_parser_family.parser.parse(query)
        expected = 'description:>3 AND description:<=1'
        assert expected == str(parsed_query)

    def test_comparison_parenthesis(self, boolean_parser_family):
        query = "(RECENCY>=15 OR CONSISTENCY<1) AND (PUBLICATION_DATE<1999 OR PRIORITY_DATE>=2001-01-01)"
        parsed_query = boolean_parser_family.parser.parse(query)
        expected = '(description:>=15 OR description:<1) AND (description:<1999 OR description:>=2001-01-01)'
        assert expected == str(parsed_query)

    def test_parser_application_date(self, boolean_parser_publication):
        query = 'APPLICATION_DATE<2000-12-12 OR PUBLICATION_NUMBER=AU5169585A1'
        expected = 'description:<2000-12-12 OR description:AU5169585A1'
        parsed_query = boolean_parser_publication.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert expected == str(parsed_query)

    def test_parser_publication_date(self, boolean_parser_publication):
        query = 'PUBLICATION_DATE<2000-12-12 OR PUBLICATION_NUMBER=AU5169585A1'
        expected = 'description:<2000-12-12 OR description:AU5169585A1'
        parsed_query = boolean_parser_publication.parser.parse(query)
        assert type(parsed_query) is expression.BooleanExpression
        assert expected == str(parsed_query)


class TestParserProximityOperator:
    def test_parser_unclosed_quotes(self, boolean_parser_family):
        query = 'ABSTRACT="John Deere~3'
        with pytest.raises(LexerException):
            boolean_parser_family.parser.parse(query)

    def test_parser_unopened_quotes(self, boolean_parser_family):
        query = 'ABSTRACT=John Deere"~3'
        with pytest.raises(LexerException):
            boolean_parser_family.parser.parse(query)

    def test_parser_special_characters(self, boolean_parser_family):
        query = 'ABSTRACT="John @#$%^ Deere"~3 "John @#$%^ Deere"^3 John@#$%^Deere~3 John@#$%^Deere^3'
        with pytest.raises(LexerException):
            boolean_parser_family.parser.parse(query)

    @pytest.mark.parametrize('query', [
        'TEXT="exhaust^10 gas"~3',
        'TEXT="exhaust^~5 gas"~2'
    ])
    def test_parser_proximity_operators_inside_quotes(self, boolean_parser_family, query):
        boolean_parser_family.parser.parse(query)

    def test_parser_double_quotes_01(self, boolean_parser_family):
        query = 'ABSTRACT="John Deere"'
        expected = 'default_term:"John Deere"'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_double_quotes_02(self, boolean_parser_family):
        query = 'ABSTRACT="John Deere"3'
        expected = 'default_term:"John Deere" 3'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_double_quotes_03(self, boolean_parser_family):
        query = 'ABSTRACT="John Deere" "John Smith"'
        expected = 'default_term:"John Deere" "John Smith"'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_double_quotes_04(self, boolean_parser_family):
        query = 'ABSTRACT="John Deere" "John Smith" Mary'
        expected = 'default_term:"John Deere" "John Smith" Mary'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_term_without_quotes(self, boolean_parser_family):
        query = 'ABSTRACT=Stonkus~3'
        expected = 'default_term:Stonkus~3'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_term_without_quotes_01(self, boolean_parser_family):
        query = 'ABSTRACT=Stonkus~3 Stonkus^4'
        expected = 'default_term:Stonkus~3 Stonkus^4'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_term_without_quotes_02(self, boolean_parser_family):
        query = 'ABSTRACT=Sons GmbH Stonkus~3 John Deere Stonkus^4 John Deere'
        expected = 'default_term:Sons GmbH Stonkus~3 John Deere Stonkus^4 John Deere' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_term_quotes_01(self, boolean_parser_family):
        query = 'ABSTRACT="Stonkus and Sons GmbH"~3 "John Deere"^4'
        expected = 'default_term:"Stonkus and Sons GmbH"~3 "John Deere"^4' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_term_quotes_02(self, boolean_parser_family):
        query = 'ABSTRACT=Procter and Gamble "Stonkus and Sons GmbH"~3 Procter ' \
                'and Gamble "John Deere"^4 Procter and Gamble'
        expected = 'default_term:Procter and Gamble "Stonkus and Sons GmbH"~3 Procter ' \
                   'and Gamble "John Deere"^4 Procter and Gamble'.replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert type(parsed_query) is expression.Term
        assert type(parsed_query.expression) is expression.Name
        assert expected == str(parsed_query)

    def test_parser_term_without_quotes_compound(self, boolean_parser_family):
        query = 'APPLICANTS=(John~6 OR Deer^4 OR Procter and Gamble)'
        expected = '(default_term:John~6 OR default_term:Deer^4 OR default_term:Procter and Gamble)' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert expected == str(parsed_query)

    def test_parser_term_quotes_compound(self, boolean_parser_family):
        query = 'APPLICANTS=("Stonkus and Sons GmbH"~3 OR Stonkus~3 "John Deere"^4 OR ' \
                '"Procter and Gamble"~5 John^6 Procter and Gamble)'
        expected = '(default_term:"Stonkus and Sons GmbH"~3 OR default_term:Stonkus~3 "John Deere"^4 OR ' \
                   'default_term:"Procter and Gamble"~5 John^6 Procter and Gamble)' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert expected == str(parsed_query)

    def test_parser_non_text_field_01(self, boolean_parser_family):
        query = 'TECH_AREAS="Electrical Engineering"'
        expected = 'default_term:"Electrical Engineering"' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert expected == str(parsed_query)

    def test_parser_non_text_field_02(self, boolean_parser_family):
        query = 'TECH_AREAS=Electrical Engineering'
        expected = 'default_term:Electrical Engineering' \
            .replace('default_term', parser_utils.DEFAULT_TERM)
        parsed_query = boolean_parser_family.parser.parse(query)
        assert expected == str(parsed_query)


class TestParserFieldNameAlias:
    # tuple of (list_of_alias, expected term)
    test_data = dict(
        TEXT=(['TACD'], 'good morning'),
        applicants=(['AN', 'ALL_AN'], 'John'),
        publication_date=(['PBD'], '1989-01-01'),
        market_coverage=(['NUMBER_OF_AUTHORITIES'], '3')
    )

    def test_parser_name_alias(self, boolean_parser_family):
        for name, (aliases, test_term) in self.test_data.items():
            expected = f'(default_term:{test_term})'.replace('default_term', parser_utils.DEFAULT_TERM)
            for alias in aliases:
                query = f'({alias}={test_term})'
                parsed_query = boolean_parser_family.parser.parse(query)
                assert type(parsed_query) is expression.Parenthesis
                assert type(parsed_query.expression) is expression.Term
                assert expected == str(parsed_query)

    @pytest.mark.parametrize('authority', [
        'CN', 'AN', '(JP OR CN OR US)', '(CN OR JP)'
    ])
    def test_parser_handles_conflicting_aliased_terms(self, boolean_parser_family, authority):
        query = f'AUTHORITIES={authority}'
        boolean_parser_family.parser.parse(query)


class TestFamilyAndPublicationParsers:
    def test_parse_family_only_fields(self, boolean_parser_family, boolean_parser_publication):
        query = 'PRIORITY_DATE=01.01.2000'
        parsed_query = boolean_parser_family.parse(query)
        assert type(parsed_query) is expression.Term
        with pytest.raises(ParsingException):
            boolean_parser_publication.parse(query)

    def test_parse_publication_only_fields(self, boolean_parser_family, boolean_parser_publication):
        query = 'PUBLICATION_NUMBER=AU5169585A1'
        parsed_query = boolean_parser_publication.parse(query)
        assert type(parsed_query) is expression.Term
        with pytest.raises(ParsingException):
            boolean_parser_family.parse(query)
