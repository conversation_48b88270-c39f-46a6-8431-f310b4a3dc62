# Local imports
import pytest

from app.ext.boolean_search.semantic_analyzer import CPCAnalyzer, FamilyLegalStatusAnalyzer, \
    IPCAnalyzer, KeywordAnalyzer, PublicationLegalStatusAnalyzer, TextAnalyzer, DateAnalyzer, PatentNumberAnalyzer, \
    PartyAnalyzer, PublicationKindAnalyzer
from app.ext.elastic_search.field_mappings import FAMILIES, PUBLICATIONS

MIN_WILDCARD_INDEX = 3


@pytest.fixture
def ipc_analyzer(es_extension):
    return IPCAnalyzer(es_extension, MIN_WILDCARD_INDEX)


@pytest.fixture
def cpc_analyzer(es_extension):
    return CPCAnalyzer(es_extension, MIN_WILDCARD_INDEX)


class TestIPCAnalyzer:

    def test_ipc_analyzer_wrong_ipc(self, ipc_analyzer, boolean_parser_family):
        query = 'IPC=1'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert error.value == '1'

    def test_ipc_analyzer_wrong_ipc_wildcard(self, ipc_analyzer, boolean_parser_family):
        query = 'IPC=A4*7C7/02'
        expected = 'A4*7C7/02'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert error.value == expected

    def test_ipc_analyzer_correct_ipc(self, ipc_analyzer, boolean_parser_family):
        query = 'IPC=A47C7/02'
        expected = r'A47C7\/02'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_ipc_analyzer_only_first_letter(self, es_extension, boolean_parser_family):
        query = 'IPC=H*'
        expected = r'/H.*/'
        ipc_analyzer = IPCAnalyzer(es_extension, 1)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_ipc_analyzer_long_ipc(self, ipc_analyzer, boolean_parser_family):
        query = 'IPC=C07C257/10'
        expected = r'C07C257\/10'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_ipc_analyzer_correct_ipc_wildcard(self, ipc_analyzer, boolean_parser_family):
        query = 'IPC=A47C7*/02'
        expected = r'/A47C7.*\/02/'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_ipc_main_group_expansion(self, ipc_analyzer, boolean_parser_family):
        query = 'IPC=A47C7/00*'
        expected = r'/A47C7\/.*/'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_ipc_subgroup_expansion(self, ipc_analyzer, boolean_parser_family):
        query = 'IPC=Y02T10/10*'
        expected = r'(Y02T10\/10 OR Y02T10\/12 OR Y02T10\/30 OR Y02T10\/40)'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = ipc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected


class TestCPCAnalyzer:
    def test_cpc_analyzer_wrong_cpc(self, cpc_analyzer, boolean_parser_family):
        query = 'CPC=1'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = cpc_analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert error.value == '1'

    def test_cpc_analyzer_wrong_cpc_wildcard(self, cpc_analyzer, boolean_parser_family):
        query = 'CPC=H0*1L2925/1'
        expected = 'H0*1L2925/1'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = cpc_analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert error.value == expected

    def test_cpc_analyzer_correct_cpc(self, cpc_analyzer, boolean_parser_family):
        query = 'CPC=H01L2925/1'
        expected = r'H01L2925\/1'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = cpc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_cpc_analyzer_only_first_letter(self, es_extension, boolean_parser_family):
        query = 'CPC=H*'
        expected = r'/H.*/'
        cpc_analyzer = CPCAnalyzer(es_extension, 1)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = cpc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_cpc_analyzer_correct_cpc_wildcard(self, cpc_analyzer, boolean_parser_family):
        query = 'CPC=H01L2925/1*'
        expected = r'/H01L2925\/1.*/'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = cpc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_cpc_main_group_expansion(self, cpc_analyzer, boolean_parser_family):
        query = 'CPC=A47C7/00*'
        expected = r'/A47C7\/.*/'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = cpc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_cpc_subgroup_expansion(self, cpc_analyzer, boolean_parser_family):
        query = 'CPC=Y02T10/10*'
        expected = r'(Y02T10\/10 OR Y02T10\/12 OR Y02T10\/30 OR Y02T10\/40)'
        parsed_query = boolean_parser_family.parser.parse(query)
        error = cpc_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected


class TestKeywordAnalyzer:
    def test_keyword_analyzer_wrong_keyword_wildcard(self, boolean_parser_family):
        query = 'APPLICANTS=Jo*hn Doe'
        expected = 'Jo*hn Doe'
        keyword_analyzer = KeywordAnalyzer(MIN_WILDCARD_INDEX)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = keyword_analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert error.value == expected

    def test_keyword_analyzer_correct_keyword_wildcard(self, boolean_parser_family):
        query = 'APPLICANTS=John*'
        expected = '/JOHN.*/'
        keyword_analyzer = KeywordAnalyzer(MIN_WILDCARD_INDEX)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = keyword_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_keyword_analyzer_correct_compound_keyword(self, boolean_parser_family):
        query = 'APPLICANTS=John Doe'
        expected = '"JOHN DOE"'
        keyword_analyzer = KeywordAnalyzer(MIN_WILDCARD_INDEX)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = keyword_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_keyword_analyzer_escape_special_chars(self, boolean_parser_family):
        query = 'APPLICANTS=POPPE & POTTHOFF*'
        expected = '/POPPE \\& POTTHOFF.*/'
        keyword_analyzer = KeywordAnalyzer(MIN_WILDCARD_INDEX)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = keyword_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_keyword_analyzer_quotes(self, boolean_parser_family):
        query = 'APPLICANTS="BIO-ON"'
        expected = '"BIO-ON"'
        keyword_analyzer = KeywordAnalyzer(MIN_WILDCARD_INDEX)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = keyword_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected


class TestDateAnalyzer:
    def test_date_analyzer(self, boolean_parser_family):
        query = 'PUBLICATION_DATE=2013-04-27'
        expected = '2013-04-27'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_date_analyzer_invalid_format(self, boolean_parser_family):
        query = 'PUBLICATION_DATE=04/27/2010'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error

    def test_date_analyzer_month_only(self, boolean_parser_family):
        query = 'PUBLICATION_DATE=2013-04'
        expected = '[2013-04 TO 2013-05]'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_date_analyzer_month_overflow(self, boolean_parser_family):
        query = 'PUBLICATION_DATE=2010-12'
        expected = '[2010-12 TO 2011-01]'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_date_analyzer_year(self, boolean_parser_family):
        query = 'PRIORITY_DATE=2010'
        expected = '[2010 TO 2011]'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_date_analyzer_comparison_day(self, boolean_parser_family):
        query = 'PUBLICATION_DATE>2013-04-27'
        expected = '2013-04-27'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_date_analyzer_comparison_month(self, boolean_parser_family):
        query = 'PUBLICATION_DATE<2013-04'
        expected = '2013-04'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_date_analyzer_comparison_year(self, boolean_parser_family):
        query = 'PUBLICATION_DATE<2013'
        expected = '2013'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_date_analyzer_comparison_year_equals(self, boolean_parser_family):
        query = 'PUBLICATION_DATE<=2013'
        expected = '2014'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected
        assert parsed_query.expression.operator == '<'

    def test_date_analyzer_comparison_month_equals(self, boolean_parser_family):
        query = 'PUBLICATION_DATE<=2013-05'
        expected = '2013-06'
        date_analyzer = DateAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = date_analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected
        assert parsed_query.expression.operator == '<'


class TestTextAnalyzer:
    def test_text_analyzer_wrong_text_wildcard(self, boolean_parser_family):
        query = 'TITLE=Lo*rem Ipsum'
        expected = 'Lo*rem Ipsum'
        text_analyzer = TextAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = text_analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert error.value == expected

    def test_text_analyzer_ignores_wildcard_at_the_end(self, boolean_parser_family):
        query = 'TITLE=Lorem Ipsum*'
        text_analyzer = TextAnalyzer(PUBLICATIONS)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = text_analyzer.analyze(parsed_query.expression)
        assert not error

    def test_text_analyzer_correct_text_compound(self, boolean_parser_family):
        query = 'TITLE=Lorem Ipsum'
        expected = '(Lorem Ipsum)'
        text_analyzer = TextAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = text_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_text_analyzer_number(self, boolean_parser_family):
        query = 'IMPACT=2.0'
        expected = '2.0'
        text_analyzer = TextAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query.expression.parser_term = parsed_query.parser_term
        parsed_query.expression.es_term = parsed_query.es_term
        error = text_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_text_analyzer_number_comparison(self, boolean_parser_family):
        query = 'IMPACT<=2.0'
        expected = '2.0'
        text_analyzer = TextAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query.expression.parser_term = parsed_query.parser_term
        parsed_query.expression.es_term = parsed_query.es_term
        error = text_analyzer.analyze(parsed_query.expression)
        assert not error
        assert parsed_query.expression.value == expected

    def test_text_analyzer_invalid_comparison(self, boolean_parser_family):
        query = 'TITLE<=2'
        text_analyzer = TextAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query.expression.parser_term = parsed_query.parser_term
        error = text_analyzer.analyze(parsed_query.expression)
        assert error


class TestPublicationKindAnalyzer:
    def test_publication_kind_regex_success(self, boolean_parser_family):
        query = 'PUBLICATION_KIND=U'
        expected = '/.*U/'
        analyzer = PublicationKindAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected
        assert parsed_query.expression.es_term == 'also_published_as'

        query = 'PUBLICATION_KIND=A1'
        expected = '/.*A1/'
        analyzer = PublicationKindAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected
        assert parsed_query.expression.es_term == 'also_published_as'

    def test_publication_kind_regex_failure(self, boolean_parser_family):
        query = 'PUBLICATION_KIND=AA'
        expected = 'AA'
        analyzer = PublicationKindAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert parsed_query.expression.value == expected

        query = 'PUBLICATION_KIND=A11'
        expected = 'A11'
        analyzer = PublicationKindAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        error = analyzer.analyze(parsed_query.expression)
        assert error is not None
        assert parsed_query.expression.value == expected


class TestPatentNumberAnalyzer:
    def test_patent_number_regex(self, boolean_parser_family):
        query = 'RAW_PUBLICATION_NUMBER=EP54547'
        expected = '/EP-0*54547-.{0,2}/'
        analyzer = PatentNumberAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected

    def test_patent_number_substring(self, boolean_parser_family):
        query = 'ALSO_PUBLISHED_AS=201304'
        expected = '/.*201304.*/'
        analyzer = PatentNumberAnalyzer()
        parsed_query = boolean_parser_family.parser.parse(query)
        error = analyzer.analyze(parsed_query.expression)
        assert error is None
        assert parsed_query.expression.value == expected


class TestLegalStatusAnalyzer:

    valid = 'active OR active_reinstated OR granted OR in_force'
    pending = 'pending'
    invalid = (
        'expired OR expired_fee_related OR expired_lifetime OR withdrawn OR '
        'withdrawn_after_issue OR abandoned OR ceased OR revoked OR not_in_force'
    )
    unknown = 'unknown'

    @pytest.mark.parametrize('query, expected', [
        ('LEGAL_STATUS=VALID', f"legal_statuses:(({valid}) OR ({pending}))"),
        ('LEGAL_STATUS=ALIVE', f"legal_statuses:(({valid}) OR ({pending}))"),  # Synonim for valid
        ('LEGAL_STATUS=DEAD', f"legal_statuses:(({invalid}) AND NOT(({valid}) OR ({pending})))"),  # Synonim for invalid
        (
            'LEGAL_STATUS=UNKNOWN',
            (
                f"(legal_statuses:(({unknown}) AND NOT(({valid}) OR ({pending})))"
                " OR (NOT(_exists_:legal_statuses)))"
            )
        ),
        ('LEGAL_STATUS=WRONG', None),
    ])
    def test_right_values_families(self, boolean_parser_family, query, expected):
        analyzer = FamilyLegalStatusAnalyzer(FAMILIES)
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query.expression.es_term = parsed_query.es_term
        parsed_query.expression.parser_term = parsed_query.parser_term
        error = analyzer.analyze(parsed_query.expression)
        if expected:
            assert error is None
            assert str(parsed_query.expression) == expected
        else:
            assert error

    @pytest.mark.parametrize('query, expected', [
        (
            'LEGAL_STATUS=VALID',
            (
                f"grant_legal_status:({valid})"
                f" OR (legal_status:({valid}) AND NOT(_exists_:grant_legal_status))"
            )
        ),
        (
            'LEGAL_STATUS=INVALID',
            (
                f"grant_legal_status:({invalid})"
                f" OR (legal_status:({invalid}) AND NOT(_exists_:grant_legal_status))"
            )
        ),
        (
            'LEGAL_STATUS=PENDING',
            (
                f"grant_legal_status:({pending})"
                f" OR (legal_status:({pending}) AND NOT(_exists_:grant_legal_status))"
            )
        ),
        (
            'LEGAL_STATUS=UNKNOWN',
            (
                f"grant_legal_status:({unknown})"
                f" OR ((legal_status:({unknown})"
                " OR (NOT(_exists_:legal_status))) AND NOT(_exists_:grant_legal_status))"
            )
        ),
        ('LEGAL_STATUS=WRONG', None),
    ])
    def test_right_values_publications(self, boolean_parser_publication, query, expected):
        analyzer = PublicationLegalStatusAnalyzer(PUBLICATIONS)
        parsed_query = boolean_parser_publication.parser.parse(query)
        parsed_query.expression.es_term = parsed_query.es_term
        parsed_query.expression.parser_term = parsed_query.parser_term
        error = analyzer.analyze(parsed_query.expression)
        if expected:
            assert error is None
            assert str(parsed_query.expression) == expected
        else:
            assert error


class TestPartyAnalyzer:

    @staticmethod
    def _analyze(boolean_parser, query):
        analyzer = PartyAnalyzer()
        parsed_query = boolean_parser.parser.parse(query)
        return analyzer.analyze(parsed_query.expression)

    @pytest.mark.parametrize('query', [
        'APPLICANTS=*ATOMI*',
        'APPLICANTS=**ATOMIC INC***',
        'INVENTORS=*atomi*',
        'INVENTORS=A',
        'INVENTORS=ATT',
        'INVENTORS=ATTR*',
        'INVENTORS=*ATTR',
    ])
    def test_should_accept_valid_input(self, boolean_parser_family, query):
        error = self._analyze(boolean_parser_family, query)
        assert not error

    @pytest.mark.parametrize('query', [
        'APPLICANTS=*AT*',
        'APPLICANTS=*A*',
        'APPLICANTS=*AT',
        'INVENTORS=*at*inc',
        'INVENTORS=AT*',
        'APPLICANTS=AT*TT',
    ])
    def test_should_reject_invalid_input(self, boolean_parser_family, query):
        error = self._analyze(boolean_parser_family, query)
        assert error


class TestFinalizer:
    @pytest.mark.parametrize('query, expected', [
        (
                'CPC=H01L01/1 AND TEXT=vehicle',
                'cpc:H01L01\\/1 AND (((title:(vehicle)) OR (abstract:(vehicle))) OR '
                '((claims:(vehicle)) OR (description:(vehicle))))'
        ),
        (
                'CPC=H01L01/1 AND TEXT=electric vehicle',
                'cpc:H01L01\\/1 AND '
                '(((title:(electric vehicle)) OR '
                '(abstract:(electric vehicle))) OR '
                '((claims:(electric vehicle)) OR '
                '(description:(electric vehicle))))'
        ),
        (
                'CPC=H01L01/1 AND TITLE=electric vehicle AND '
                'ABSTRACT=electric vehicle AND CLAIMS=electric vehicle AND '
                'DESCRIPTION=electric vehicle',
                'cpc:H01L01\\/1 AND '
                'title:(electric vehicle) AND '
                'abstract:(electric vehicle) AND '
                'claims:(electric vehicle) AND '
                'description:(electric vehicle)'
        ),
        (
                'CPC=H01L01/1 AND TITLE=electric vehicle AND '
                'ABSTRACT=electric vehicle AND CLAIMS=electric vehicle AND '
                'DESCRIPTION=electric vehicle',
                'cpc:H01L01\\/1 AND '
                'title:(electric vehicle) AND '
                'abstract:(electric vehicle) AND '
                'claims:(electric vehicle) AND '
                'description:(electric vehicle)'
        ),
    ])
    def test_expanding_full_text_query(self, boolean_parser_family, semantic_analyzer_family, query, expected):
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query, errors = semantic_analyzer_family.finalize(parsed_query)
        assert not errors
        assert str(parsed_query) == expected

    @pytest.mark.parametrize('query, expected', [
        (
            'TAC=vehicle',
            '(((title:(vehicle)) OR (abstract:(vehicle))) OR (claims:(vehicle)))'
        ),
        (
            'TAC=(vehicle OR car)',
            '((((title:(vehicle) OR title:(car))) OR '
            '((abstract:(vehicle) OR abstract:(car)))) OR '
            '((claims:(vehicle) OR claims:(car))))'
        ),
    ])
    def test_expanding_tac_query(self, boolean_parser_family, semantic_analyzer_family, query, expected):
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query, errors = semantic_analyzer_family.finalize(parsed_query)
        assert not errors
        assert str(parsed_query) == expected

    @pytest.mark.parametrize('query, expected', [
        (
                'TITLE=(invention OR device AND control) AND APPLICANTS=(POPPE POTTHOFF)',
                '(title:(invention) OR title:(device) AND title:(control)) AND ((applicants_cleaned:"POPPE POTTHOFF" '
                'OR applicants_ifi:"POPPE POTTHOFF"))'
        ),
        (
                'TITLE=("invention OR device" AND control) AND APPLICANTS=(POPPE POTTHOFF)',
                '(title:("invention OR device") AND title:(control)) AND ((applicants_cleaned:"POPPE POTTHOFF"'
                ' OR applicants_ifi:"POPPE POTTHOFF"))'
        ),
        (
                'TITLE=("invention OR device" OR sensor AND control) AND APPLICANTS=(POPPE POTTHOFF)',
                '(title:("invention OR device") OR title:(sensor) AND title:(control)) AND '
                '((applicants_cleaned:"POPPE POTTHOFF" OR applicants_ifi:"POPPE POTTHOFF"))'
        ),
        (
                'TITLE=("invention OR device" OR sensor module AND control) AND APPLICANTS=(POPPE POTTHOFF)',
                ('(title:("invention OR device") OR title:(sensor module) AND title:(control)) '
                 'AND ((applicants_cleaned:"POPPE POTTHOFF" OR applicants_ifi:"POPPE POTTHOFF"))')
        ),
        (
                'TITLE=("invention OR device" sensor module AND control)',
                '(title:("invention OR device" sensor module) AND title:(control))'
        )
    ])
    def test_expanding_text_query(self, boolean_parser_family, semantic_analyzer_family, query, expected):
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query, errors = semantic_analyzer_family.finalize(parsed_query)
        assert not errors
        assert str(parsed_query) == expected

    def test_full_text_error(self, boolean_parser_family, semantic_analyzer_family):
        query = "TEXT=vehi*cle"
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query, errors = semantic_analyzer_family.finalize(parsed_query)
        assert errors

    @pytest.mark.parametrize('query, expected', [
        (
                'CPC=H01L01/1 AND TEXT=vehicle',
                '(cpc:H01L01\\/1 AND (((title:(vehicle)) OR (abstract:(vehicle))) '
                'OR ((claims:(vehicle)) OR (description:(vehicle)))))'
        ),
        (
                'APPLICANTS=John OR CPC=H01L01/1 AND TEXT=vehicle',
                '(applicants_cleaned:"JOHN" OR applicants_ifi:"JOHN") OR (cpc:H01L01\\/1 AND (((title:(vehicle)) '
                'OR (abstract:(vehicle))) OR ((claims:(vehicle)) OR (description:(vehicle)))))'
        ),
        (
                'APPLICANTS=John OR (CPC=H01L01/1 AND TEXT=vehicle)',
                '(applicants_cleaned:"JOHN" OR applicants_ifi:"JOHN") OR (cpc:H01L01\\/1 AND (((title:(vehicle)) '
                'OR (abstract:(vehicle))) OR ((claims:(vehicle)) OR (description:(vehicle)))))'
        ),
        (
                'APPLICANTS=John OR APPLICANTS=Doe AND PUBLICATION_DATE>2000',
                '(applicants_cleaned:"JOHN" OR applicants_ifi:"JOHN") OR '
                '((applicants_cleaned:"DOE" OR applicants_ifi:"DOE") AND publn_date:>2000)'
        ),
    ])
    def test_mathematical_precedence_full_text_query(self, boolean_parser_family, semantic_analyzer_family, query,
                                                     expected):
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query, errors = semantic_analyzer_family.finalize(parsed_query, True)
        assert not errors
        assert str(parsed_query) == expected


class TestFulltextDetection:

    def test_detect_fulltext_fields(self, boolean_parser_family):
        query = 'DESCRIPTION=test'
        parsed_query = boolean_parser_family.parser.parse(query)
        assert parsed_query.has_fulltext()

    def test_detect_other_fields_as_non_fulltext(self, boolean_parser_family):
        query = 'TITLE=test OR PRIORITY_DATE>2020-01-01'
        parsed_query = boolean_parser_family.parser.parse(query)
        assert not parsed_query.has_fulltext()

    def test_detect_fulltext_fields_with_other_fields(self, boolean_parser_family):
        query = 'TITLE=test OR (DESCRIPTION=test AND PRIORITY_DATE>2020-01-01)'
        parsed_query = boolean_parser_family.parse(query)
        assert parsed_query.has_fulltext()


class TestPublicationAnalyzer:
    def test_publication_analyzer(self, boolean_parser_publication, semantic_analyzer_publication):
        query = "APPLICATION_NUMBER=EP654564A OR DOCDB_FAMILY_ID=3"
        expected = 'application_number:/EP-654564-A/ OR docdb_family_id:3'
        parsed_query = boolean_parser_publication.parser.parse(query)
        parsed_query, errors = semantic_analyzer_publication.finalize(parsed_query)
        assert not errors
        assert str(parsed_query) == expected

    def test_publication_analyzer_text(self, boolean_parser_publication, semantic_analyzer_publication):
        query = "TEXT=vehicle"
        expected = '(((title:(vehicle)) OR (abstract:(vehicle))) ' \
                   'OR ((claims:(vehicle)) OR (description:(vehicle))))'
        parsed_query = boolean_parser_publication.parser.parse(query)
        parsed_query, errors = semantic_analyzer_publication.finalize(parsed_query)
        assert not errors
        assert str(parsed_query) == expected

    def test_publication_analyzer_extra_fields(self, boolean_parser_publication, semantic_analyzer_publication):
        query = "PUBLICATION_KIND=A1 OR ALSO_PUBLISHED_AS=EP0004545 OR PUBLICATION_AUTHORITY=AR OR AUTHORITIES=BR"
        expected = 'publication_number:/.*A1/ OR publication_number:/EP-0*4545-.{0,2}/ ' \
                   'OR publication_number:/AR-.*/ OR publication_number:/BR-.*/'
        parsed_query = boolean_parser_publication.parser.parse(query)
        parsed_query, errors = semantic_analyzer_publication.finalize(parsed_query)
        assert not errors
        assert str(parsed_query) == expected
