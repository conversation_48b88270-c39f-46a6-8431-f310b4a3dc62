import time


class TestEvaluator:
    """
    Unit tests for the in-memory boolean evaluation engine
    """
    TEST_DATA = [
        {},
        {"inventors_octimine": None, "cpc": None, "publn_auth": None, "also_published_as": None, "priority_date": None,
         "risk": None, "consistency": None, "title": None, "owner_ids": None},

        {"inventors_octimine": "", "cpc": "", "publn_auth": "", "also_published_as": "", "priority_date": "",
         "risk": "", "consistency": "", "title": "", "owner_ids": []},

        {"inventors_octimine": 3, "cpc": 5, "publn_auth": -1, "also_published_as": -1, "priority_date": -1,
         "risk": [], "consistency": [], "title": []},

        {"inventors_octimine": ["MAX MUSTERMANN"], "cpc": ["A01H6/02"], "publn_auth": "DE",
         "also_published_as": ["EP-5487-A1"], "priority_date": "2007-01-15", "risk": 2, "consistency": 1.5,
         "title": "Antibody Molecules To LAG-3 And Uses Thereof", "owner_ids": [1, 2]},

        {"inventors_octimine": ["MAX MUSTER"], "cpc": ["A01"], "publn_auth": "D",
         "also_published_as": ["EP-005487-B1"], "priority_date": "2007-01-16", "risk": -1, "consistency": -1.5,
         "title": "High antibody levels in bloodstream measurement", "owner_ids": [11, 21]},

        {"inventors_octimine": "MAX MUSTERMANN", "cpc": "A01H", "publn_auth": ["DE"],
         "also_published_as": "EP-5487-A1", "priority_date": "2006-12-31", "risk": "2", "consistency": "1.6",
         "title": "Antibodies for covid-19", "owner_ids": [11, 21]},

        {"inventors_octimine": ["JOHANNES BIEDERMANN", "MAX MUSTERMANN"], "cpc": ["A01H6/02", "B01B1/005"],
         "publn_auth": "US", "also_published_as": ["US-1995425-A1"], "priority_date": "2007-02-01", "risk": "1",
         "consistency": "2", "title": "ANTIBODY MOLECULES TO LAG-3 AND USES THEREOF", "owner_ids": [22]},

        {"inventors_octimine": ["JOHANNES BIEDERMANN"], "cpc": ["B01B1/005"], "publn_auth": "WO,DE",
         "also_published_as": ["EP-005487-A1", "US-1995425-A1"], "priority_date": "2007-02-31", "risk": 3,
         "consistency": .4, "title": "Byte-By-Byte Type Processor Circuit"},
    ]

    def execute_query(self, query, boolean_parser, semantic_analyzer):
        parsed_query = boolean_parser.parser.parse(query)
        parsed_query, errors = semantic_analyzer.finalize(parsed_query)
        assert not errors
        parsed_query.prepare()
        return sum(parsed_query.evaluate(doc) for doc in self.TEST_DATA)

    def test_keyword_field(self, boolean_parser_family, semantic_analyzer_family):
        query = 'INVENTORS=Max Mustermann'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 3

    def test_keyword_field_inverted(self, boolean_parser_family, semantic_analyzer_family):
        query = 'INVENTORS<>Max Mustermann'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 6

    def test_keyword_regexp(self, boolean_parser_family, semantic_analyzer_family):
        query = 'CPC=A01*'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 4

    def test_keyword_exact_match(self, boolean_parser_family, semantic_analyzer_family):
        query = 'CPC=A01H6'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 0

    def test_keyword_regexp_inverted(self, boolean_parser_family, semantic_analyzer_family):
        query = 'CPC<>A01*'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 5

    def test_keyword_single(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PUBLICATION_AUTHORITY=DE'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 2

    def test_keyword_single_inverted(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PUBLICATION_AUTHORITY<>DE'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 7

    def test_patent_number(self, boolean_parser_family, semantic_analyzer_family):
        query = 'ALSO_PUBLISHED_AS=EP-5487'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 4

    def test_date(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PRIORITY_DATE=2007-01-15'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 1

    def test_date_inverted(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PRIORITY_DATE<>2007-01-15'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 8

    def test_date_smaller(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PRIORITY_DATE<2007-01-15'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 1

    def test_date_larger(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PRIORITY_DATE>=2007-01-15'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 3

    def test_date_month(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PRIORITY_DATE=2007-01'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 2

    def test_date_year(self, boolean_parser_family, semantic_analyzer_family):
        query = 'PRIORITY_DATE=2007'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 3

    def test_number(self, boolean_parser_family, semantic_analyzer_family):
        query = 'RISK=2'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 2

    def test_number_less_than(self, boolean_parser_family, semantic_analyzer_family):
        query = 'RISK<=2'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 4

    def test_number_not_equal(self, boolean_parser_family, semantic_analyzer_family):
        query = 'CONSISTENCY<>1.5'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 8

    def test_number_greater_than(self, boolean_parser_family, semantic_analyzer_family):
        query = 'CONSISTENCY>1'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 3

    def test_text_simple(self, boolean_parser_family, semantic_analyzer_family):
        query = 'TITLE=Antibodies'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 4

    def test_text_multiple(self, boolean_parser_family, semantic_analyzer_family):
        query = 'TITLE=Antibody molecules'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 2

        query = 'TITLE="Antibody molecules"'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 2

        query = 'TITLE="Antibody molecules" "Processor Circuits"'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 0

        query = 'TITLE="Antibody   molecules" "  bloodstream  "'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 0

        query = 'TITLE=Antibodies "  Processor Circuits "'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 0

    def test_owner_ids(self, boolean_parser_family, semantic_analyzer_family):
        query = 'OWNER_IDS=1'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 3

        query = 'OWNER_IDS=2'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 4

        query = 'OWNER_IDS=(1 OR 2)'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 4

        query = 'OWNER_IDS=(1 AND 2)'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 3

    def test_complex_query(self, boolean_parser_family, semantic_analyzer_family):
        query = 'INVENTORS=MAX M* AND CPC=A01* AND PUBLICATION_AUTHORITY=DE ' \
                'AND (ALSO_PUBLISHED_AS=(EP-5487 OR US-5487))'\
                'AND PRIORITY_DATE>2007-01-01 AND (RISK>=2 OR CONSISTENCY<0) ' \
                'AND (ABSTRACT=molecule OR (TITLE=molecule AND CONSISTENCY<2))'
        assert self.execute_query(query, boolean_parser_family, semantic_analyzer_family) == 1

    def test_large_dataset(self, boolean_parser_family, semantic_analyzer_family):
        query = 'IPC=A47C7/02 AND APPLICANTS=John Doe OR (IMPACT>=3 AND PUBLICATION_DATE<1789-01-31)'
        doc1 = {"ipc": "A47C7/02", "applicants_ifi": "JOHN DOE", "impact": 3, "publn_date": "1788-05-18"}
        doc2 = {"ipc": "A47C7/03", "applicants_ifi": "JOHN DOE", "impact": 3, "publn_date": "1788-05-18"}
        doc3 = {"ipc": "A47C7/02", "applicants_ifi": "Audrius", "impact": 3, "publn_date": "1788-05-18"}
        doc4 = {"ipc": "A47C7/02", "applicants_ifi": "Audrius", "impact": 2, "publn_date": "1788-05-18"}
        doc5 = {"ipc": "A47C7/02", "applicants_ifi": "JOHN DOE", "impact": 3, "publ_date": "1988-05-18"}
        doc6 = {"ipc": "A47C7/02", "applicants_ifi": "Audrius", "impact": 2, "publn_date": "1788-05-18"}
        docs = (doc1, doc2, doc3, doc4, doc5, doc6) * 10000
        parsed_query = boolean_parser_family.parser.parse(query)
        parsed_query, errors = semantic_analyzer_family.finalize(parsed_query)
        assert not errors
        parsed_query.prepare()
        ts = time.time()
        for i, doc in enumerate(docs):
            parsed_query.evaluate(doc)
        assert time.time() - ts < 1000
