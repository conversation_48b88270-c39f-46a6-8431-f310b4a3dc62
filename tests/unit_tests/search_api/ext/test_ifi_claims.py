from http import HTTPStatus
import pytest
from app.ext.ifi_claims.ifi_claims_response import IFIClaimsResponse
from app.ext.ifi_claims.ifi_claims_cache import IFIClaimsFileSystemCache


class TestIFIClaims:

    @pytest.mark.parametrize("read_data, file_size, expected_status", [
        ("", 0, HTTPStatus.NO_CONTENT),
        (b"<img-binary>", 5, HTTPStatus.OK),
    ])
    def test_ifi_claims_response_from_file(self, mocker, read_data, file_size, expected_status):
        mocker.patch('os.path.getsize', return_value=file_size)
        mocker.patch("builtins.open", mocker.mock_open(read_data=read_data))
        ifi_response = IFIClaimsResponse.from_file("test", "png")
        assert ifi_response.status_code == expected_status

    @pytest.mark.parametrize("is_cached, expected_response, cache_timeout", [
        (True, IFIClaimsResponse(b"<img-binary>", "image/png", HTTPStatus.OK), 3600 * 24 * 365),
        (True, IFIClaimsResponse(None, "image/png", HTTPStatus.NO_CONTENT), 3600 * 24),
        (False, None, None)
    ])
    def test_ifi_claims_cache_check(self, mocker, is_cached, expected_response, cache_timeout):
        cache = IFIClaimsFileSystemCache('/tmp/ifi_cache')
        mocker.patch.object(cache, "_is_cached", return_value=is_cached)
        mocker.patch.object(IFIClaimsResponse, "from_file", return_value=expected_response)
        response = cache.check("EP-000000000-A1", "pdf")
        assert response == expected_response
        if response:
            assert response.cache_timeout == cache_timeout

    @pytest.mark.parametrize("response, should_be_stored", [
        (IFIClaimsResponse(b"<img-binary>", "image/png", HTTPStatus.OK), True),
        (IFIClaimsResponse(None, None, HTTPStatus.NO_CONTENT), True),
        (IFIClaimsResponse(None, None, HTTPStatus.NOT_FOUND), True),
        (IFIClaimsResponse(None, None, HTTPStatus.TOO_MANY_REQUESTS), False),
        (IFIClaimsResponse(None, None, HTTPStatus.INTERNAL_SERVER_ERROR), False),
    ])
    def test_ifi_claims_cache_store(self, mocker, response, should_be_stored):
        cache = IFIClaimsFileSystemCache('/tmp/ifi_cache')
        store_into_file = mocker.patch.object(cache, '_store_into_file')
        cache.store("EP-000000000-A1", "pdf", response)
        store_into_file.assert_called_once() if should_be_stored else store_into_file.assert_not_called()
