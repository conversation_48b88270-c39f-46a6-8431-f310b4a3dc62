import pytest

from app.extensions import rate_limiter
from app.ext.rate_limiter.flask_rate_limiter import RateLimitException
from tests.unit_tests.search_api import data


class TestRateLimiter:
    def test_api_access_limit_disabled(self, mocker, app):
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.get_jwt_claims',
                     return_value={'user_id': data.user_id})
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.RateLimiter._get_throttle',
                     return_value=None)
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.RateLimiter.get_user_quota',
                     return_value=None)
        for _ in range(100):
            rate_limiter.check_api_access_limit()

    def test_api_access_limit(self, mocker, app):
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.get_jwt_claims',
                     return_value={'user_id': data.user_id})
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.RateLimiter._get_throttle',
                     return_value=2)
        with pytest.raises(RateLimitException):
            rate_limiter.check_api_access_limit()
            rate_limiter.check_api_access_limit()
            rate_limiter.check_api_access_limit()
            rate_limiter.check_api_access_limit()
            rate_limiter.check_api_access_limit()

    def test_api_access_limit_for_context(self, mocker, app):
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.get_jwt_claims',
                     return_value={'user_id': data.user_id})
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.RateLimiter._get_throttle',
                     return_value=2)
        rate_limiter.check_api_access_limit('test-1')
        rate_limiter.check_api_access_limit('test-2')
        rate_limiter.check_api_access_limit('test-1')
        rate_limiter.check_api_access_limit('test-2')
        with pytest.raises(RateLimitException):
            rate_limiter.check_api_access_limit('test-1')
        with pytest.raises(RateLimitException):
            rate_limiter.check_api_access_limit('test-2')

    def test_search_limit(self, mocker, app):
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.get_jwt_claims',
                     return_value={'user_id': data.user_id})
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.RateLimiter.get_user_quota',
                     return_value=2)
        with pytest.raises(RateLimitException):
            rate_limiter.check_search_rate_limit()
            rate_limiter.check_search_rate_limit()
            rate_limiter.check_search_rate_limit()

    def test_search_unlimited(self, mocker, app):
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.get_jwt_claims',
                     return_value={'user_id': data.user_id})
        mocker.patch('app.ext.rate_limiter.flask_rate_limiter.RateLimiter.get_user_quota',
                     return_value=-1)
        for _ in range(10):
            rate_limiter.check_search_rate_limit()
