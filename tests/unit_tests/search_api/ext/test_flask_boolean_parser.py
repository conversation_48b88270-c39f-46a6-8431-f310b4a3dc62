# Third party imports
import pytest

# Local imports
from app.ext.boolean_search.exceptions import BooleanSearchError


class TestFlaskBooleanParser:
    def test_boolean_parser_errors(self, flask_boolean_parser):
        query = 'IPC=A47C7/ AND APPLICANTS=<PERSON>'
        with pytest.raises(BooleanSearchError):
            str(flask_boolean_parser.parse(query))

    def test_boolean_parser_application_date(self, flask_boolean_parser):
        query = 'APPLICATION_DATE=2000-12-12'
        expected = r'application_date:2000-12-12'
        analyzed_query = str(flask_boolean_parser.parse(query, doc_type="PUBLICATION"))
        assert analyzed_query == expected

    def test_boolean_parser_successful(self, flask_boolean_parser):
        query = 'IPC=A47C7/02 AND APPLICANTS=John <PERSON>'
        expected = r'ipc:A47C7\/02 AND (applicants_cleaned:"JOHN DOE" OR applicants_ifi:"JOHN DOE")'
        analyzed_query = str(flask_boolean_parser.parse(query))
        assert analyzed_query == expected

    def test_boolean_parser_application_date_combination(self, flask_boolean_parser):
        query = 'APPLICATION_DATE=2000-12-12 AND IPC=A47C7/02 AND APPLICANTS=John Doe'
        expected = (r'application_date:2000-12-12 AND ipc:A47C7\/02 AND'
                    r' (applicants_cleaned:"JOHN DOE" OR applicants_ifi:"JOHN DOE")')
        analyzed_query = str(flask_boolean_parser.parse(query, doc_type="PUBLICATION"))
        assert analyzed_query == expected

    def test_comparison_operators(self, flask_boolean_parser):
        query = "IMPACT>3 AND RISK<=1"
        expected = 'impact:>3 AND risk:<=1'
        analyzed_query = str(flask_boolean_parser.parse(query))
        assert analyzed_query == expected

    def test_comparison_operators_text_field(self, flask_boolean_parser):
        query = "TITLE > 15"
        with pytest.raises(BooleanSearchError):
            str(flask_boolean_parser.parse(query))

    @pytest.mark.parametrize('query, expected', [
        (
                "TEXT=(liquid nitrogen AND (extraction OR production))",
                '((((title:(liquid nitrogen) AND (title:(extraction) OR title:(production)))) '
                'OR ((abstract:(liquid nitrogen) AND (abstract:(extraction) OR '
                'abstract:(production))))) OR (((claims:(liquid nitrogen) AND '
                '(claims:(extraction) OR claims:(production)))) OR ((description:(liquid '
                'nitrogen) AND (description:(extraction) OR description:(production))))))'
        ),
        (
                "TEXT=Blah",
                '(((title:(Blah)) OR (abstract:(Blah))) OR '
                '((claims:(Blah)) OR (description:(Blah))))'
        ),
        (
                'TITLE=3656931868 OR TEXT=test "process"~2',
                'title:(3656931868) OR (((title:test "process"~2) OR (abstract:test "process"~2)) OR '
                '((claims:test "process"~2) OR (description:test "process"~2)))'
        )
    ])
    def test_all_text_fields(self, flask_boolean_parser, query, expected):
        analyzed_query = str(flask_boolean_parser.parse(query))
        assert analyzed_query == expected

    @pytest.mark.parametrize('query, expected', [
        (
            "(AN=Google OR TACD<>Lorem Ipsum)",
            ('((applicants_cleaned:"GOOGLE" OR applicants_ifi:"GOOGLE") OR (NOT ((((title:(Lorem Ipsum)) OR '
             '(abstract:(Lorem Ipsum))) OR ((claims:(Lorem Ipsum)) OR (description:(Lorem '
             'Ipsum)))))))')
        ),
        ("OWNERS=TEST", '(assignees_cleaned:"TEST" OR assignees_ifi:"TEST")'),
        ("OWNERS_ORIGINAL=TEST", 'assignees_original:"TEST"'),
    ])
    def test_synonyms(self, flask_boolean_parser, query, expected):
        analyzed_query = str(flask_boolean_parser.parse(query))
        assert analyzed_query == expected

    @pytest.mark.parametrize('query, expected', [
        ("OWNER_IDS=1", "owner_ids:(11 OR 12 OR 13 OR 1)"),
        ("OWNER_IDS=(1 OR 2)", "(owner_ids:(11 OR 12 OR 13 OR 1) OR owner_ids:(21 OR 22 OR 23 OR 2))"),
    ])
    def test_individual_fields(self, flask_boolean_parser, query, expected):
        analyzed_query = str(flask_boolean_parser.parse(query))
        assert analyzed_query == expected
