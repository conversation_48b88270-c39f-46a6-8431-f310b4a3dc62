# Third party imports
import pytest
import json
# Local imports
from app.ext.semantic_search.consumer import Consumer
from app.ext.semantic_search.exceptions import QueueConnectionError
from app.ext.semantic_search.messages import ERROR_PRODUCER_TIMEOUT, ERROR_PRODUCER_RESPONSE, \
    ERROR_PRODUCER_TIMEOUT_DETAILED
from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.mocks.consumer import PropertiesMock, ConsumerMock, BlockingConnectionMock


class TestConsumer:
    def test_consumer_wrong_queue_username(self):
        with pytest.raises(QueueConnectionError):
            ConsumerMock(username=data.wrong_queue_username)._connect()

    def test_consumer_wrong_queue_password(self):
        with pytest.raises(QueueConnectionError):
            ConsumerMock(password=data.wrong_queue_password)._connect()

    def test_consumer_wrong_queue_host(self):
        with pytest.raises(QueueConnectionError):
            ConsumerMock(host=data.wrong_queue_host)._connect()

    def test_consumer_wrong_queue_port(self):
        with pytest.raises(QueueConnectionError):
            ConsumerMock(port=data.wrong_queue_port)._connect()

    def test_consumer_queue_connection_successful(self):
        consumer = ConsumerMock(username=data.queue_username, password=data.queue_password, host=data.queue_host,
                                port=data.queue_port)
        consumer._connect()
        assert consumer.connection

    def test_consumer_on_timeout(self):
        consumer = ConsumerMock(username=data.queue_username, password=data.queue_password, host=data.queue_host,
                                port=data.queue_port)
        consumer._connect()
        consumer.on_timeout()
        assert consumer.error.message == ERROR_PRODUCER_TIMEOUT

    def test_consumer_on_response_different_corr_id(self):
        consumer = ConsumerMock(username=data.queue_username, password=data.queue_password, host=data.queue_host,
                                port=data.queue_port)
        consumer._connect()
        consumer.corr_id = 0
        consumer.on_response(0, "Basic method", PropertiesMock(consumer.corr_id + 1), '')  # Force return

    def test_consumer_on_response_malformed_producer_response(self):
        consumer = ConsumerMock(username=data.queue_username, password=data.queue_password, host=data.queue_host,
                                port=data.queue_port)
        consumer._connect()
        consumer.corr_id = 0
        body = ''
        consumer.on_response(0, "Basic method", PropertiesMock(consumer.corr_id), body)
        assert consumer.error.message == ERROR_PRODUCER_RESPONSE

    def test_consumer_on_response_producer_error(self):
        consumer = ConsumerMock(username=data.queue_username, password=data.queue_password, host=data.queue_host,
                                port=data.queue_port)
        consumer._connect()
        consumer.corr_id = 0
        error_message = "Something bad happened"
        body = dict(error=('%s' % error_message))
        consumer.on_response(0, "Basic method", PropertiesMock(consumer.corr_id), body)
        assert consumer.error.message == error_message

    def test_consumer_on_response_producer_response_successful(self):
        consumer = ConsumerMock(username=data.queue_username, password=data.queue_password, host=data.queue_host,
                                port=data.queue_port)
        consumer.corr_id = 0
        body = data.create_producer_response(0, 5)
        consumer.on_response(0, "Basic method", PropertiesMock(consumer.corr_id), body)
        response_from_first_producer = consumer.response.pop(0)
        first_doc = response_from_first_producer[0]
        assert first_doc['docdb_family_id'] == 1
        assert first_doc['similarity_index'] == 1

    def test_consumer_on_timeout_with_partial_responses(self, mocker):
        mocker.patch('app.ext.semantic_search.consumer.pika.BlockingConnection', BlockingConnectionMock)
        consumer = Consumer(username=data.queue_username, password=data.queue_password, host=data.queue_host,
                            port=data.queue_port, exchange='test', producer_timeout='10')
        consumer.corr_id = 0
        body = json.dumps({
            'error_code': '0-0-0-0-0',
            'total_servers': 2,
            'server_number': 1,
            'results': [],
            'token': 'x',
            'user_id': 0
        }).encode('utf-8')
        consumer.on_response(0, 'Basic method', PropertiesMock(consumer.corr_id), body)
        consumer.on_timeout()
        assert consumer.error.message == ERROR_PRODUCER_TIMEOUT_DETAILED.format(completed='2-1', missing='2-2')
