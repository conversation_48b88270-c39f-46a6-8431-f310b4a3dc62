from http import HTTPStatus
from tests.unit_tests.search_api import data
from tests.unit_tests.search_api.assertions import assert_status
from octimine_common.tests.data import access_token
from tests.unit_tests.search_api.data import PATENT_NUMBER_PATH, DOC_ID_PATH
from tests.unit_tests.search_api.mocks.requests import MockResponse


class TestListAttachments:
    def test_list_attachments(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(data.ifi_attachments, HTTPStatus.OK))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/list?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert 2 == len(rv.json["attachments"])

    def test_list_with_images(self, client, mocker):
        mocker.patch('requests.get', side_effect=[MockResponse(data.ifi_attachments, HTTPStatus.OK),
                                                  MockResponse(None, HTTPStatus.OK), MockResponse(None, HTTPStatus.OK)])
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/list?document_id=123&include_images=1&thumbnails=0', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert 2 == len(rv.json["attachments"])
        assert 'bytes' in rv.json["attachments"][1]['attachments'][0]['image']

    def test_list_attachments_wrong_document(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(data.ifi_attachments, HTTPStatus.OK))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=0))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/list?patent_number=EP248454', headers=headers)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    def test_list_attachments_ifi_error(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse({}, HTTPStatus.BAD_REQUEST))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=1))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/list?patent_number=EP248454', headers=headers)
        assert_status(rv, HTTPStatus.BAD_REQUEST)


class TestPdf:
    def test_pdf_success(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/pdf?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)

    def test_pdf_json_response(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token, Accept='application/json')
        rv = client.get('/search/attachment/pdf?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert 'data' in rv.json

    def test_document_not_found(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=0)))
        headers = dict(Authorization='Bearer %s' % access_token, Accept='application/pdf')
        rv = client.get('/search/attachment/pdf?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    def test_invalid_request_1(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/pdf', headers=headers)
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_invalid_request_2(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/pdf?document_id=123&patent_number=EP345223A1', headers=headers)
        assert_status(rv, HTTPStatus.BAD_REQUEST)

    def test_no_attachment(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.NO_CONTENT))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=1))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/pdf?patent_number=EP2345431A1', headers=headers)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    def test_ifi_error(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.FORBIDDEN))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=1))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/pdf?patent_number=EP2345431A1', headers=headers)
        assert_status(rv, HTTPStatus.FORBIDDEN)

    def test_pdf_caching(self, client, mocker, patch_config):
        patch_config({'CACHE_FOLDER': '/tmp/ifi_cache'})
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK, b'123'))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/pdf?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        rv = client.get('/search/attachment/pdf?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)


class TestImage:
    def test_ab_image_success(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/image?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert 'X-Source-Document' in rv.headers

    def test_ab_image_json_response(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token, Accept='application/json')
        rv = client.get('/search/attachment/image?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        assert 'data' in rv.json
        assert 'source-document' in rv.json['data']

    def test_fetch_image_success(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=1))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/image?patent_number=EP200755&path=/path/to/image&thumbnail=1',
                        headers=headers)
        assert_status(rv, HTTPStatus.OK)

    def test_no_image(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.NO_CONTENT))
        mocker.patch(PATENT_NUMBER_PATH, return_value=data.get_es_search_result(page=1))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/image?patent_number=EP2345431A1', headers=headers)
        assert_status(rv, HTTPStatus.NOT_FOUND)

    def test_ifi_error(self, client, mocker):
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.TOO_MANY_REQUESTS))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/image?document_id=123&path=/path/to/image', headers=headers)
        assert_status(rv, HTTPStatus.TOO_MANY_REQUESTS)

    def test_image_caching(self, client, mocker, patch_config):
        patch_config({'CACHE_FOLDER': '/tmp/ifi_cache'})
        mocker.patch('requests.get', return_value=MockResponse(None, HTTPStatus.OK, b'123'))
        mocker.patch(DOC_ID_PATH, return_value=data.as_multiget_result(data.get_es_search_result(page=1)))
        headers = dict(Authorization='Bearer %s' % access_token)
        rv = client.get('/search/attachment/image?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)
        rv = client.get('/search/attachment/image?document_id=123', headers=headers)
        assert_status(rv, HTTPStatus.OK)
