# ![img.png](logo.png)

# Octimine API
Octimine is an AI-powered patent search and analytics platform based in Munich, Germany. 
It uses machine learning and natural language processing (NLP) to provide fast, accurate, 
and intelligent patent searches, helping businesses, researchers, and legal professionals 
analyze intellectual property (IP) data. Users can quickly find relevant patents, detect 
prior art, and gain insights into technology trends. The platform is designed to enhance 
decision-making in R&D, IP strategy, and competitive analysis by offering smart 
recommendations and intuitive visualizations.

## 📖 Table of Contents  

- [✨ Features](#-features)  
- [📥 Installation](#-installation)  
- [🚀 Usage](#-usage)  
- [🔑 Access Token](#-access-token)
- [📜 Generating migration scripts](#-generating-migration-scripts)
- [🔐 SSO](#-sso)

## ✨ Features
### 💡 Intuitive & Intelligent  
Octimine's user-friendly interface is designed for everyone—from students to IP professionals. No special training is required to search, analyze, and collaborate on patent information effortlessly.  

### ⚡ Lightning-Fast Insights  
Powered by AI-driven patent search and monitoring technology, Octimine delivers rapid and accurate results. Whether you're conducting prior art searches, analyzing patent landscapes, or monitoring technology trends, Octimine provides up-to-date insights in seconds.  

### 🤝 Seamless Team Collaboration  
Forget messy email threads with PDF attachments—Octimine streamlines communication within your team. With task management, annotation sharing, and a searchable document database, collaboration has never been easier.  

### 🔍 Actionable Intelligence  
Octimine’s powerful search, monitoring, and analytics tools help you gain a clear view of your industry, identify key trends, and make data-driven decisions for your IP strategy. Whether you're an independent inventor or part of a large organization, Octimine keeps you ahead of the curve.  

## 📥 Installation
Octimine API is a python based project that uses poetry to manage its dependencies and docker to manage 
infrastructure around it for your local environment (databases, caches, middle-ware and the like...). As a tool to manage python versions, we recommend to use pyenv. Currently, we use **Python 3.12**, so please make sure you use the correct version.
Below, we provide a collection
of useful links and bash commands to setup your environment

### Instruction Websites:
[Docker for Linux](https://docs.docker.com/desktop/setup/install/linux/)\
[Pipx](https://github.com/pypa/pipx)\
[Poetry](https://python-poetry.org/docs/)\
[Curl](https://gcore.com/learning/how-to-install-curl-on-ubuntu/)\
[Pyenv](https://github.com/pyenv/pyenv?tab=readme-ov-file#1-automatic-installer-recommended)

### Preparing a local environment

First, make sure curl is installed:
```bash
    curl --version
```
If an error appears, run
```bash
    sudo apt update
    sudo apt install curl
```
Next, you can use curl to install pyenv with the following command: 
```bash
    curl -fsSL https://pyenv.run | bash
```
Then, to set up your bash to work with pyenv, run:
```bash
    echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
    echo '[[ -d $PYENV_ROOT/bin ]] && export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
    echo 'eval "$(pyenv init - bash)"' >> ~/.bashrc
```
If you have ~/.profile, ~/.bash_profile or ~/.bash_login, add the commands there as well. If you have none of these, create a ~/.profile and add the commands there.
- to add to ~/.profile: 
```bash 
    echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.profile
    echo '[[ -d $PYENV_ROOT/bin ]] && export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.profile
    echo 'eval "$(pyenv init - bash)"' >> ~/.profile
```
- to add to ~/.bash_profile: 
````bash
    echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bash_profile
    echo '[[ -d $PYENV_ROOT/bin ]] && export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bash_profile
    echo 'eval "$(pyenv init - bash)"' >> ~/.bash_profile
````
After that, use pyenv to install a specific python version, in this example 3.12:
```bash
    pyenv install 3.12
```
Then, Install additional OS libraries required from some python packages
```bash
    sudo apt install -y libxml2-dev libxslt-dev libxmlsec1-dev pkg-config
```

After that, we need to install Pipx to later use that for the installation of Poetry:
```bash
    sudo apt update
    sudo apt install pipx
```
And to set the PATH variable, use one of the following options. The first is locally, the latter is for global options:
```bash
    sudo pipx ensurepath
```
 
```bash
    sudo pipx ensurepath --global # optional to allow pipx actions with --global argument
```
With pipx we can install poetry:
```bash
    pipx install "poetry<2.0"
```
Once poetry is installed, it can be used to install the required dependencies with:
```bash
    poetry install --with dev
```
The last step is to set up docker for pulling our infrastructure images. To do so you need to login to the docker registry. Doing that requires you to create a gpg key if you don't have one:

```bash 
    gpg --full-generate-key
```
You can list all your gpg keys:
```bash
    gpg --list-keys
```
with your key, you can init the required key:
```commandline
    pass init -p {subfolder} {your-gpg-id}
```
After that, you can login into our gitlab docker registry. You need to use credentials that are provided by the company:
```bash
    docker login registry.gitlab.com
```

## 🚀 Usage
To create your local instance of Octimine API, you can use the instructions provided below:

If you are working from a terminal, you can use 
```bash
     source $(poetry env info --path)/bin/activate
```
to activate the poetry environment

- Spin up app infrastructure (databases, search-engines, etc...) with docker compose

```bash
      docker compose up -d
 ```

- Export flask variables

  - Unix Bash (Linux, Mac, etc.)

    ```bash
    export FLASK_ENV=development
    export FLASK_APP=run.py
    ```

  - Windows PowerShell

    ```bash
    $env:FLASK_ENV = "development"
    $env:FLASK_APP = "run.py"
    ```

  - Windows CMD

    ```bash
    set FLASK_ENV=development
    set FLASK_APP=run.py
    ```

- Create database tables with initial data

    ```bash
    flask db upgrade
    ```

- Run flask application

  ```bash
  flask run --port=5000
  ```

- Run celery worker (for background tasks) and beat (for periodic tasks)

  ```bash
  celery -A task_executor:celery worker -B
  ````

- In case of 503 errors on semantic search endpoint:

  ```bash
  docker compose restart mock-search-engine
  ```
## 🔑 Access Token
How to acquire an access token: 
There are already some users created for you:

- [<EMAIL>](<EMAIL>)
- [<EMAIL>](<EMAIL>)
- [<EMAIL>](<EMAIL>)
- [<EMAIL>](<EMAIL>)
- [<EMAIL>](<EMAIL>)
- [<EMAIL>](<EMAIL>)

To query the API, you will need an access token. You can get one in the following way

- Execute the following command

    ```bash
    curl --location --request POST 'http://127.0.0.1:5000/auth/login' \
    --header 'Content-Type: application/json' \
    --data-raw '{
    "password": "Abcd+12345",
    "email": "<EMAIL>"
    }'
    ```

- Get the `access_token` from the response payload
- Plug this into the Bearer Authentication header for your API requests

# 📜 Generating migration scripts

For generating migration scripts after updating/creating DB models, run the following command:

```bash
flask db migrate
```

This will generate a new migration script under `migrations/versions` folder.
Please adjust it accordingly to only include what is needed

## 🔐 SSO

### SAML

To configure a SAML SSO/SLO connector we will need to set up a connector using the following payload template:

```json
{
  "company_id": "<company_id>",
  "params": {
    "strict": true,
    "debug": false,
    "email_nameid_supported": true,
    "attribute_names": {
      "first_name": "first_name",
      "last_name": "last_name",
      "email": "email"
    },
    "idp": {
      "entityId": "<idp_entity_id>",
      "singleSignOnService": {
        "url": "<idp_sso_url>",
        "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      },
      "singleLogoutService": {
        "url": "<idp_slo_url>",
        "binding": "urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
      },
      "x509cert": "<idp_x509_cert>"
    }
  }
}
```

- `<company_id>`: ID of the company for which we want to create the SAML connector
- `<idp_entity_id>`: Entity id of the IdP (Identity Provider).
- `<idp_sso_url>`: URL of the Single-Sign-On service of the IdP.
- `<idp_slo_url>`: URL of the Single-Logout service of the IdP.
- `<idp_x509_cert>`: Base-64 encoded x509 certificate (without line breaks) of the IdP

#### Steps

1. In Octimine, create an empty SAML connector using the endpoint `/auth/saml/connectors`: `{"company_id": "<company_id>", "params", {}}`
    - Query the metadata endpoint `/auth/saml/metadata?connector_id=<connector_id>` and keep the returned info somewhere.
2. In the IdP, Create an application that will integrate the SAML connector in Octimine.
    - Supply the configuration values for the SP (Service Provider) with the info returned from the metadata endpoint
    - Create the following custom attributes. We will need them to create new accounts on the fly:
        1. `first_name`: Should map to the first name of the user
        2. `last_name`: Should map to the last name of the user
    - After this process is finished you should have access to the following information which we will need the following steps:
        1. `<idp_entity_id>`
        2. `<idp_sso_url>`
        3. `<idp_slo_url>`
        4. `<idp_x509_cert>`

3. In Octimine, Update the connector's `params` field following the template provided at start filled with the correct values.

#### Troubleshooting

If the IdP does not support `email` as format for `nameid`:

- change `email_nameid_supported` to `false` in connector params
- configure `email` to be present in custom attributes.

If the IdP does not support specifying custom attribute names

- modify values in `attribute_names` to match attribute names coming from IdP
