{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended", "docker:disable"], "lockFileMaintenance": {"enabled": true, "schedule": "before 4am on friday"}, "packageRules": [{"matchUpdateTypes": ["minor"], "groupName": "Minor dependencies", "groupSlug": "minor"}, {"matchUpdateTypes": ["patch"], "groupName": "Patch dependencies", "groupSlug": "patch"}, {"matchPackageNames": ["xmlsec"], "allowedVersions": "<=1.3.13"}, {"matchPackageNames": ["huggingface-hub"], "allowedVersions": "<0.26.0"}, {"matchDepNames": ["python"], "enabled": false}], "major": {"dependencyDashboardApproval": true}}