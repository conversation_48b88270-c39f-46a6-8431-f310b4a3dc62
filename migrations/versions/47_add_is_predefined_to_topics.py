"""Add is_predefined to topics

Revision ID: 1460d7d0f550
Revises: 83cdcf8e48c0
Create Date: 2024-12-04 11:27:09.417695

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '1460d7d0f550'
down_revision = '83cdcf8e48c0'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('topics', sa.Column('is_predefined', sa.<PERSON>(), nullable=True))
    op.execute('UPDATE topics SET is_predefined = false')
    op.alter_column('topics', 'is_predefined', existing_type=sa.<PERSON><PERSON>an(), nullable=False)
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('topics', 'is_predefined')
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
