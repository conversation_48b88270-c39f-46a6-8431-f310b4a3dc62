"""Initial database

Revision ID: 78c613a0baa9
Revises: 
Create Date: 2023-03-16 13:57:23.805020

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '78c613a0baa9'
down_revision = 'f0d8df3f12f9'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('applicant_aliases',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.String(length=16), nullable=False),
    sa.Column('applicant', sa.String(length=256), nullable=False),
    sa.Column('alias', sa.String(length=256), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('boolean_templates',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('type', sa.Enum('STANDARD', 'ADVANCED', name='booleantemplatetype'), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_boolean_templates_company_id'), 'boolean_templates', ['company_id'], unique=False)
    op.create_index(op.f('ix_boolean_templates_user_id'), 'boolean_templates', ['user_id'], unique=False)
    op.create_table('collaborations',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('resource_id', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('collaborator_id', sa.Integer(), nullable=False),
    sa.Column('collaborator_type', sa.Enum('USER', 'GROUP', name='collaboratortype'), nullable=False),
    sa.Column('resource_type', sa.Enum('COLLECTION', 'PATENT', 'COPIED_MONITOR', 'LANDSCAPE', 'PATENT_COMMENT', 'PATENT_TAG', 'REPLY_ON_TAGGED_COMMENT', name='collaborationresourcetype'), nullable=False),
    sa.Column('permission', sa.Enum('READONLY', 'READ_WRITE', name='collaborationpermission'), nullable=False),
    sa.Column('status', sa.Enum('NEW', 'READ', 'UNSHARED', name='collaborationstatus'), nullable=False),
    sa.Column('shared_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_collaborations_collaborator_id'), 'collaborations', ['collaborator_id'], unique=False)
    op.create_index(op.f('ix_collaborations_owner_id'), 'collaborations', ['owner_id'], unique=False)
    op.create_table('documents_comments',
    sa.Column('comment', sa.Text(), nullable=False),
    sa.Column('color', sa.String(length=6), nullable=False),
    sa.Column('parent_comment_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.Integer(), nullable=True),
    sa.Column('field', sa.Enum('abstract', 'claims', 'description', name='documentannotationbasefieldenum'), nullable=False),
    sa.Column('text', sa.Text(), nullable=False),
    sa.Column('start_pos', sa.Integer(), nullable=False),
    sa.Column('end_pos', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['parent_comment_id'], ['documents_comments.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_documents_comments_company_id'), 'documents_comments', ['company_id'], unique=False)
    op.create_index(op.f('ix_documents_comments_user_id'), 'documents_comments', ['user_id'], unique=False)
    op.create_table('folders',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=256), nullable=False),
    sa.Column('description', sa.String(length=256), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'name')
    )
    op.create_index(op.f('ix_folders_user_id'), 'folders', ['user_id'], unique=False)
    op.create_table('labels',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('shortcut', sa.String(length=1), nullable=True),
    sa.Column('color', sa.String(length=6), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_labels_company_id'), 'labels', ['company_id'], unique=False)
    op.create_index(op.f('ix_labels_user_id'), 'labels', ['user_id'], unique=False)
    op.create_table('landscape_profiles',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('status', sa.Enum('CREATED', 'FETCHING_PORTFOLIO', 'DRAFT', 'COMPUTING', 'COMPLETED', 'FAILED', name='landscapeprofilestatus'), nullable=False),
    sa.Column('status_details', sa.String(length=256), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=256), nullable=False),
    sa.Column('category', sa.String(length=256), nullable=True),
    sa.Column('processed_input', sa.JSON(), nullable=False),
    sa.Column('submitted_patent_numbers_count', sa.Integer(), nullable=True),
    sa.Column('submitted_boolean_results_count', sa.Integer(), nullable=True),
    sa.Column('submitted_semantic_results_count', sa.Integer(), nullable=True),
    sa.Column('submitted_family_ids_count', sa.Integer(), nullable=True),
    sa.Column('matched_documents_count', sa.Integer(), nullable=True),
    sa.Column('covered_authorities_count', sa.Integer(), nullable=True),
    sa.Column('top3_cpc_codes', sa.JSON(), nullable=False),
    sa.Column('most_frequent_cpc_codes', sa.JSON(), nullable=False),
    sa.Column('environmental_classification_codes', sa.JSON(), nullable=False),
    sa.Column('mean_patent_age_in_years', sa.Float(), nullable=True),
    sa.Column('high_risk_patents_count', sa.Integer(), nullable=True),
    sa.Column('high_impact_patents_count', sa.Integer(), nullable=True),
    sa.Column('high_recency_patents_count', sa.Integer(), nullable=True),
    sa.Column('highly_cited_patents_count', sa.Integer(), nullable=True),
    sa.Column('highly_cited_patents_threshold', sa.Integer(), nullable=True),
    sa.Column('active_patents_count', sa.Integer(), nullable=True),
    sa.Column('inactive_patents_count', sa.Integer(), nullable=True),
    sa.Column('environmental_patents_count', sa.Integer(), nullable=True),
    sa.Column('oldest_document', sa.Integer(), nullable=True),
    sa.Column('oldest_patent', sa.String(length=50), nullable=True),
    sa.Column('oldest_priority_date', sa.DateTime(), nullable=True),
    sa.Column('newest_document', sa.Integer(), nullable=True),
    sa.Column('newest_patent', sa.String(length=50), nullable=True),
    sa.Column('newest_priority_date', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_landscape_profiles_user_id'), 'landscape_profiles', ['user_id'], unique=False)
    op.create_table('monitor_profile',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.String(length=16), nullable=False),
    sa.Column('active', sa.Boolean(), nullable=False),
    sa.Column('internal', sa.Boolean(), nullable=True),
    sa.Column('name', sa.String(length=256), nullable=False),
    sa.Column('company', sa.String(length=256), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('delivery_method', sa.Enum('Text', 'PDF', 'Excel', 'CSV', name='monitordeliverymethod'), nullable=True),
    sa.Column('delivery_frequency', sa.Enum('Weekly', 'BiWeekly', 'Monthly', 'Quarterly', name='monitordeliveryfrequency'), nullable=False),
    sa.Column('ipc_codes', sa.JSON(), nullable=True),
    sa.Column('boolean_query', sa.JSON(), nullable=True),
    sa.Column('boolean_query_active', sa.Boolean(), nullable=False),
    sa.Column('citation_query', sa.JSON(), nullable=True),
    sa.Column('citation_query_active', sa.Boolean(), nullable=False),
    sa.Column('semantic_query', sa.JSON(), nullable=True),
    sa.Column('semantic_query_active', sa.Boolean(), nullable=False),
    sa.Column('machine_learning_profile', sa.JSON(), nullable=True),
    sa.Column('machine_learning_active', sa.Boolean(), nullable=False),
    sa.Column('machine_learning_status', sa.Enum('Pending', 'Error', 'Creating', 'Training', 'Ready', name='machinelearningstatus'), nullable=False),
    sa.Column('machine_learning_message', sa.Text(), nullable=True),
    sa.Column('machine_learning_training_info', sa.JSON(), nullable=True),
    sa.Column('machine_learning_dataset_last_update', sa.DateTime(), nullable=True),
    sa.Column('machine_learning_last_trained', sa.DateTime(), nullable=True),
    sa.Column('legal_status_active', sa.Boolean(), nullable=False),
    sa.Column('legal_status_profile', sa.JSON(), nullable=True),
    sa.Column('legal_status_snapshot', sa.JSON(), nullable=True),
    sa.Column('legal_status_summary', sa.JSON(), nullable=True),
    sa.Column('shared_users', sa.Text(), nullable=True),
    sa.Column('shared_groups', sa.Text(), nullable=True),
    sa.Column('scheduled_until', sa.DateTime(), nullable=True),
    sa.Column('email_enabled', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_monitor_profile_user_id'), 'monitor_profile', ['user_id'], unique=False)
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('type', sa.Enum('SHARE', 'COMMENT', name='notificationtype'), nullable=False),
    sa.Column('resource_type', sa.Enum('MONITOR_PROFILE', 'LANDSCAPE_PROFILE', 'MONITOR_RUN', 'TASK', 'COLLECTION', 'PATENT', name='resourcetype'), nullable=True),
    sa.Column('resource_id', sa.Integer(), nullable=True),
    sa.Column('url', sa.String(length=512), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('from_user_id', sa.Integer(), nullable=False),
    sa.Column('to_user_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('NEW', 'READ', name='notificationstatus'), nullable=False),
    sa.Column('body', sa.String(length=512), nullable=False),
    sa.Column('params', sa.JSON(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notifications_from_user_id'), 'notifications', ['from_user_id'], unique=False)
    op.create_index(op.f('ix_notifications_to_user_id'), 'notifications', ['to_user_id'], unique=False)
    op.create_table('read_document',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('last_read', sa.DateTime(), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_read_document_user_id'), 'read_document', ['user_id'], unique=False)
    op.create_table('search_history',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('search_hash', sa.String(length=65), nullable=True),
    sa.Column('number_of_hits', sa.Integer(), nullable=True),
    sa.Column('search_type', sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'MACHINE_LEARNING', name='searchtype'), nullable=True),
    sa.Column('search_input', sa.Text().with_variant(mysql.MEDIUMTEXT(), 'mysql'), nullable=True),
    sa.Column('search_input_hint', sa.String(length=100), nullable=True),
    sa.Column('search_filters', sa.JSON(), nullable=True),
    sa.Column('patent_numbers', sa.JSON(), nullable=True),
    sa.Column('search_fields', sa.JSON(), nullable=True),
    sa.Column('max_results', sa.Integer(), nullable=False),
    sa.Column('source_language', sa.String(length=5), nullable=True),
    sa.Column('level', sa.Integer(), nullable=True),
    sa.Column('direction', sa.VARCHAR(length=20), nullable=True),
    sa.Column('document_type', sa.VARCHAR(length=16), nullable=True),
    sa.Column('citation_phase', sa.VARCHAR(length=16), nullable=True),
    sa.Column('citation_category', sa.VARCHAR(length=16), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_search_history_user_id'), 'search_history', ['user_id'], unique=False)
    op.create_table('synonyms',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.String(length=16), nullable=False),
    sa.Column('name', sa.String(length=256), nullable=False),
    sa.Column('synset', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('task',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('author_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.Integer(), nullable=True),
    sa.Column('resource_id', sa.Integer(), nullable=True),
    sa.Column('resource_type', sa.Enum('DOCUMENT', 'COLLECTION', 'MONITOR_RUN', name='taskresourcetypeenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('deadline', sa.DateTime(), nullable=True),
    sa.Column('subject', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=1000), nullable=True),
    sa.Column('task_type', sa.Enum('STAR_RATING', 'YES_NO_ANSWER', 'TEXT_REPLY', 'LABELS', name='taskanswertypeenum'), nullable=False),
    sa.Column('status', sa.Enum('OPEN', 'CLOSED', 'DONE', name='taskstatusenum'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_author_id'), 'task', ['author_id'], unique=False)
    op.create_index(op.f('ix_task_resource_id'), 'task', ['resource_id'], unique=False)
    op.create_index(op.f('ix_task_status'), 'task', ['status'], unique=False)
    op.create_table('collections',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=256), nullable=False),
    sa.Column('description', sa.String(length=256), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('share_code', sa.String(length=256), nullable=True),
    sa.Column('source_type', sa.Enum('SEMANTIC_SEARCH_HISTORY', 'BOOLEAN_SEARCH_HISTORY', 'CITATION_SEARCH_HISTORY', 'MONITOR_RUN', name='sourcetype'), nullable=True),
    sa.Column('source_id', sa.Integer(), nullable=True),
    sa.Column('folder_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['folder_id'], ['folders.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('share_code')
    )
    op.create_index(op.f('ix_collections_user_id'), 'collections', ['user_id'], unique=False)
    op.create_table('documents_labels',
    sa.Column('label_id', sa.Integer(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('company_id', sa.Integer(), nullable=True),
    sa.Column('field', sa.Enum('abstract', 'claims', 'description', name='documentannotationbasefieldenum'), nullable=False),
    sa.Column('text', sa.Text(), nullable=False),
    sa.Column('start_pos', sa.Integer(), nullable=False),
    sa.Column('end_pos', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['label_id'], ['labels.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_documents_labels_company_id'), 'documents_labels', ['company_id'], unique=False)
    op.create_index(op.f('ix_documents_labels_user_id'), 'documents_labels', ['user_id'], unique=False)
    op.create_table('landscape_profile_documents',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('status', sa.Enum('INACTIVE', 'ACTIVE', name='landscapeprofiledocumentstatus'), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('source', sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'PATENT_NUMBER', 'DOCUMENT_ID', 'NPL', name='searchtype'), nullable=False),
    sa.Column('landscape_profile_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['landscape_profile_id'], ['landscape_profiles.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('landscape_rni_results',
    sa.Column('landscape_profile_id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.String(length=100), nullable=False),
    sa.Column('status', sa.Enum('COMPUTING', 'FAILURE', 'SUCCESS', name='landscaperniresultstatus'), nullable=False),
    sa.Column('message', sa.String(length=255), nullable=True),
    sa.Column('data', sa.JSON(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['landscape_profile_id'], ['landscape_profiles.id'], ),
    sa.PrimaryKeyConstraint('landscape_profile_id')
    )
    op.create_table('legal_status_tracking',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.String(length=16), nullable=False),
    sa.Column('profile_id', sa.Integer(), nullable=False),
    sa.Column('family_id', sa.Integer(), nullable=False),
    sa.Column('publication_number', sa.String(length=50), nullable=False),
    sa.ForeignKeyConstraint(['profile_id'], ['monitor_profile.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_legal_status_tracking_user_id', 'legal_status_tracking', ['user_id'], unique=False)
    op.create_index('ix_legal_status_tracking_user_id_publication_number', 'legal_status_tracking', ['user_id', 'publication_number'], unique=False)
    op.create_table('machine_learning_dataset',
    sa.Column('profile_id', sa.Integer(), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('source', sa.Enum('PATENT_NUMBER', 'SEARCH', 'FEEDBACK', name='machinelearningdatasetentrysource'), nullable=False),
    sa.Column('match', sa.Enum('POSITIVE', 'NEGATIVE', name='machinelearningdatasetentrymatch'), nullable=True),
    sa.ForeignKeyConstraint(['profile_id'], ['monitor_profile.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('profile_id', 'document_id')
    )
    op.create_table('monitor_profile_shared_copies',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('version_id', sa.Integer(), nullable=False),
    sa.Column('owner_id', sa.String(length=16), nullable=False),
    sa.Column('owner_details', sa.JSON(), nullable=False),
    sa.Column('shared_with', sa.String(length=16), nullable=False),
    sa.Column('shared_with_details', sa.JSON(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'SHARED', name='sharedcopystatus'), nullable=False),
    sa.Column('original_id', sa.Integer(), nullable=False),
    sa.Column('copy_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['copy_id'], ['monitor_profile.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['original_id'], ['monitor_profile.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('owner_id', 'original_id', 'shared_with')
    )
    op.create_table('monitor_profile_user_settings',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('profile_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('send_emails', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['profile_id'], ['monitor_profile.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('profile_id', 'user_id')
    )
    op.create_index(op.f('ix_monitor_profile_user_settings_profile_id'), 'monitor_profile_user_settings', ['profile_id'], unique=False)
    op.create_table('monitor_run',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.String(length=16), nullable=False),
    sa.Column('profile_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=256), nullable=True),
    sa.Column('run_type', sa.Enum('Scheduled', 'Manual', 'Initial', 'Feedback', name='monitorruntype'), nullable=False),
    sa.Column('run_from', sa.DateTime(), nullable=True),
    sa.Column('run_to', sa.DateTime(), nullable=True),
    sa.Column('status', sa.Enum('Pending', 'Error', 'Processing', 'Finished', 'Skipped', name='monitorrunstatus'), nullable=False),
    sa.Column('boolean_status', sa.Enum('Pending', 'Error', 'Processing', 'Finished', 'Skipped', name='monitorrunstatus'), nullable=False),
    sa.Column('semantic_status', sa.Enum('Pending', 'Error', 'Processing', 'Finished', 'Skipped', name='monitorrunstatus'), nullable=False),
    sa.Column('citation_status', sa.Enum('Pending', 'Error', 'Processing', 'Finished', 'Skipped', name='monitorrunstatus'), nullable=False),
    sa.Column('ml_status', sa.Enum('Pending', 'Error', 'GettingData', 'LoadingModel', 'RunningPrediction', 'CreatingSnapshot', 'Finished', 'Skipped', name='monitorrunmlstatus'), nullable=False),
    sa.Column('legal_status_status', sa.Enum('Pending', 'Error', 'Processing', 'Finished', 'Skipped', name='monitorrunstatus'), nullable=False),
    sa.Column('email_sent', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['profile_id'], ['monitor_profile.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_monitor_run_user_id'), 'monitor_run', ['user_id'], unique=False)
    op.create_table('tagged_groups',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('document_comment_id', sa.Integer(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['document_comment_id'], ['documents_comments.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tagged_users',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('document_comment_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['document_comment_id'], ['documents_comments.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('task_assignment',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('task_id', sa.Integer(), nullable=False),
    sa.Column('assignee_id', sa.Integer(), nullable=True),
    sa.Column('answer', sa.String(length=1000), nullable=True),
    sa.Column('answered_at', sa.DateTime(), nullable=True),
    sa.Column('status', sa.Enum('OPEN', 'CLOSED', 'DONE', 'OVERDUE', name='assignedtaskstatusenum'), nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['task.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_assignment_assignee_id'), 'task_assignment', ['assignee_id'], unique=False)
    op.create_index(op.f('ix_task_assignment_status'), 'task_assignment', ['status'], unique=False)
    op.create_table('collection_results',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('document_id', sa.Integer(), nullable=False),
    sa.Column('collection_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['collection_id'], ['collections.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('legal_status_result_entries',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('run_id', sa.Integer(), nullable=False),
    sa.Column('family_id', sa.Integer(), nullable=False),
    sa.Column('publication_number', sa.String(length=50), nullable=False),
    sa.Column('previous_status', sa.JSON(), nullable=False),
    sa.Column('current_status', sa.JSON(), nullable=False),
    sa.ForeignKeyConstraint(['run_id'], ['monitor_run.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('monitor_results',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('family_id', sa.Integer(), nullable=False),
    sa.Column('run_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['run_id'], ['monitor_run.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('run_id', 'family_id')
    )
    op.create_index(op.f('ix_monitor_results_run_id'), 'monitor_results', ['run_id'], unique=False)
    op.create_table('boolean_result_entries',
    sa.Column('result_id', sa.Integer(), nullable=False),
    sa.Column('score', sa.Float(), nullable=True),
    sa.ForeignKeyConstraint(['result_id'], ['monitor_results.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('result_id')
    )
    op.create_table('machine_learning_result_entries',
    sa.Column('result_id', sa.Integer(), nullable=False),
    sa.Column('confidence_score', sa.Float(), nullable=True),
    sa.Column('word_scores', sa.JSON(), nullable=True),
    sa.Column('matching_document_ids', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['result_id'], ['monitor_results.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('result_id')
    )
    op.create_table('semantic_result_entries',
    sa.Column('result_id', sa.Integer(), nullable=False),
    sa.Column('similarity_index', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['result_id'], ['monitor_results.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('result_id')
    )
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('semantic_result_entries')
    op.drop_table('machine_learning_result_entries')
    op.drop_table('boolean_result_entries')
    op.drop_index(op.f('ix_monitor_results_run_id'), table_name='monitor_results')
    op.drop_table('monitor_results')
    op.drop_table('legal_status_result_entries')
    op.drop_table('collection_results')
    op.drop_index(op.f('ix_task_assignment_status'), table_name='task_assignment')
    op.drop_index(op.f('ix_task_assignment_assignee_id'), table_name='task_assignment')
    op.drop_table('task_assignment')
    op.drop_table('tagged_users')
    op.drop_table('tagged_groups')
    op.drop_index(op.f('ix_monitor_run_user_id'), table_name='monitor_run')
    op.drop_table('monitor_run')
    op.drop_index(op.f('ix_monitor_profile_user_settings_profile_id'), table_name='monitor_profile_user_settings')
    op.drop_table('monitor_profile_user_settings')
    op.drop_table('monitor_profile_shared_copies')
    op.drop_table('machine_learning_dataset')
    op.drop_index('ix_legal_status_tracking_user_id_publication_number', table_name='legal_status_tracking')
    op.drop_index('ix_legal_status_tracking_user_id', table_name='legal_status_tracking')
    op.drop_table('legal_status_tracking')
    op.drop_table('landscape_rni_results')
    op.drop_table('landscape_profile_documents')
    op.drop_index(op.f('ix_documents_labels_user_id'), table_name='documents_labels')
    op.drop_index(op.f('ix_documents_labels_company_id'), table_name='documents_labels')
    op.drop_table('documents_labels')
    op.drop_index(op.f('ix_collections_user_id'), table_name='collections')
    op.drop_table('collections')
    op.drop_index(op.f('ix_task_status'), table_name='task')
    op.drop_index(op.f('ix_task_resource_id'), table_name='task')
    op.drop_index(op.f('ix_task_author_id'), table_name='task')
    op.drop_table('task')
    op.drop_table('synonyms')
    op.drop_index(op.f('ix_search_history_user_id'), table_name='search_history')
    op.drop_table('search_history')
    op.drop_index(op.f('ix_read_document_user_id'), table_name='read_document')
    op.drop_table('read_document')
    op.drop_index(op.f('ix_notifications_to_user_id'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_from_user_id'), table_name='notifications')
    op.drop_table('notifications')
    op.drop_index(op.f('ix_monitor_profile_user_id'), table_name='monitor_profile')
    op.drop_table('monitor_profile')
    op.drop_index(op.f('ix_landscape_profiles_user_id'), table_name='landscape_profiles')
    op.drop_table('landscape_profiles')
    op.drop_index(op.f('ix_labels_user_id'), table_name='labels')
    op.drop_index(op.f('ix_labels_company_id'), table_name='labels')
    op.drop_table('labels')
    op.drop_index(op.f('ix_folders_user_id'), table_name='folders')
    op.drop_table('folders')
    op.drop_index(op.f('ix_documents_comments_user_id'), table_name='documents_comments')
    op.drop_index(op.f('ix_documents_comments_company_id'), table_name='documents_comments')
    op.drop_table('documents_comments')
    op.drop_index(op.f('ix_collaborations_owner_id'), table_name='collaborations')
    op.drop_index(op.f('ix_collaborations_collaborator_id'), table_name='collaborations')
    op.drop_table('collaborations')
    op.drop_index(op.f('ix_boolean_templates_user_id'), table_name='boolean_templates')
    op.drop_index(op.f('ix_boolean_templates_company_id'), table_name='boolean_templates')
    op.drop_table('boolean_templates')
    op.drop_table('applicant_aliases')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass


