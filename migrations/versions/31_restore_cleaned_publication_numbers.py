"""empty message

Revision ID: 70b1aa6db9b8
Revises: aca42f6452b3
Create Date: 2024-06-07 22:41:56.471450

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy import not_, select
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

from octimine_common.patent_number import PatentNumberParser

# revision identifiers, used by Alembic.
revision = '70b1aa6db9b8'
down_revision = 'aca42f6452b3'
branch_labels = None
depends_on = None

Base = declarative_base()


class CollectionResult(Base):
    __tablename__ = 'collection_results'

    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    publication_number = sa.Column(sa.String(50), nullable=True)


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    session = Session(bind=op.get_bind())

    results = session.execute(
        select(CollectionResult).
        where(
            CollectionResult.publication_number != None,
            not_(CollectionResult.publication_number.contains('-'))
        )
    ).scalars().all()

    for result in results:
        try:
            result.publication_number = PatentNumberParser.parse(result.publication_number).join()
        except ValueError as error:
            print(f"Could not parse publication number {result.publication_number}: {error}")

    session.commit()


def downgrade_():
    pass


def upgrade_auth():
    pass


def downgrade_auth():
    pass
