"""empty message

Revision ID: 3c68d8afcd0c
Revises: 402401bf0286
Create Date: 2024-02-20 16:32:49.746534

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '3c68d8afcd0c'
down_revision = '402401bf0286'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.drop_column('landscape_profiles', 'active_patents_count')
    op.drop_column('landscape_profiles', 'oldest_patent')
    op.drop_column('landscape_profiles', 'most_frequent_cpc_codes')
    op.drop_column('landscape_profiles', 'high_recency_patents_count')
    op.drop_column('landscape_profiles', 'newest_document')
    op.drop_column('landscape_profiles', 'top3_cpc_codes')
    op.drop_column('landscape_profiles', 'high_risk_patents_count')
    op.drop_column('landscape_profiles', 'highly_cited_patents_threshold')
    op.drop_column('landscape_profiles', 'environmental_patents_count')
    op.drop_column('landscape_profiles', 'mean_patent_age_in_years')
    op.drop_column('landscape_profiles', 'oldest_priority_date')
    op.drop_column('landscape_profiles', 'covered_authorities_count')
    op.drop_column('landscape_profiles', 'newest_patent')
    op.drop_column('landscape_profiles', 'inactive_patents_count')
    op.drop_column('landscape_profiles', 'highly_cited_patents_count')
    op.drop_column('landscape_profiles', 'newest_priority_date')
    op.drop_column('landscape_profiles', 'high_impact_patents_count')
    op.drop_column('landscape_profiles', 'oldest_document')
    op.drop_column('landscape_profiles', 'environmental_classification_codes')


def downgrade_():
    op.add_column('landscape_profiles', sa.Column('environmental_classification_codes', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=False))
    op.add_column('landscape_profiles', sa.Column('oldest_document', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('high_impact_patents_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('newest_priority_date', mysql.DATETIME(), nullable=True))
    op.add_column('landscape_profiles', sa.Column('highly_cited_patents_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('inactive_patents_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('newest_patent', mysql.VARCHAR(length=50), nullable=True))
    op.add_column('landscape_profiles', sa.Column('covered_authorities_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('oldest_priority_date', mysql.DATETIME(), nullable=True))
    op.add_column('landscape_profiles', sa.Column('mean_patent_age_in_years', mysql.FLOAT(), nullable=True))
    op.add_column('landscape_profiles', sa.Column('environmental_patents_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('highly_cited_patents_threshold', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('high_risk_patents_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('top3_cpc_codes', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=False))
    op.add_column('landscape_profiles', sa.Column('newest_document', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('high_recency_patents_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))
    op.add_column('landscape_profiles', sa.Column('most_frequent_cpc_codes', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=False))
    op.add_column('landscape_profiles', sa.Column('oldest_patent', mysql.VARCHAR(length=50), nullable=True))
    op.add_column('landscape_profiles', sa.Column('active_patents_count', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True))


def upgrade_auth():
    pass


def downgrade_auth():
    pass
