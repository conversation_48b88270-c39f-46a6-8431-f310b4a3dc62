"""folder name check on same level

Revision ID: 7cf23338bcf9
Revises: 7b4b0ae25009
Create Date: 2025-05-30 10:08:35.847494

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '7cf23338bcf9'
down_revision = '7b4b0ae25009'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('folders', schema=None) as batch_op:
        batch_op.drop_index('user_id')
        batch_op.create_unique_constraint('folders_unique_folder_name', ['user_id', 'name', 'parent_id'])
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('folders', schema=None) as batch_op:
        batch_op.drop_constraint('folders_unique_folder_name', type_='unique')
        batch_op.create_index('user_id', ['user_id', 'name'], unique=True)
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

