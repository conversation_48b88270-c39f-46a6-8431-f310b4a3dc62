"""Update Everyone group description to use team terminology

Revision ID: 18891546b0c2
Revises: 3619a2ff6ded
Create Date: 2025-06-25 06:22:50.932664

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '18891546b0c2'
down_revision = '3619a2ff6ded'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    from sqlalchemy import text
    op.execute(text("""
        UPDATE groups 
        SET description = 'System team containing all users in the company'
        WHERE name = 'Everyone' AND is_system = 1
    """))


def downgrade_auth():
    from sqlalchemy import text
    op.execute(text("""
        UPDATE groups 
        SET description = 'System group containing all users in the company'
        WHERE name = 'Everyone' AND is_system = 1
    """))

