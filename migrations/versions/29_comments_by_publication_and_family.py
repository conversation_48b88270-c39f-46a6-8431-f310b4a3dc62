"""Added general comment and publication number comment in comment feature

Revision ID: 4609369ce47f
Revises: 720df80126bd
Create Date: 2024-04-05 10:12:19.890300

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '4609369ce47f'
down_revision = '720df80126bd'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents_comments', sa.Column('updated_at', sa.DateTime(), nullable=False))
    op.execute(text('''
           update documents_comments dc 
           set dc.updated_at = dc.created_at
       '''))
    op.add_column('documents_comments', sa.Column('publication_number', sa.String(length=50), nullable=True))
    op.alter_column('documents_comments', 'source',
                    existing_type=sa.Enum('annotation', 'explainer'),
                    type_=sa.Enum('annotation', 'explainer', 'general'),
                    existing_nullable=True)
    op.alter_column('documents_comments', 'field',
                    existing_type=sa.Enum('abstract', 'claims', 'description'),
                    type_=sa.Enum('abstract', 'claims', 'description', 'general'),
                    existing_nullable=False)
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('documents_comments', 'publication_number')
    op.drop_column('documents_comments', 'updated_at')
    op.alter_column('documents_comments', 'source',
                    existing_type=sa.Enum('annotation', 'explainer', 'general'),
                    type_=sa.Enum('annotation', 'explainer'),
                    existing_nullable=True)
    op.alter_column('documents_comments', 'field',
                    existing_type=sa.Enum('abstract', 'claims', 'description', 'general'),
                    type_=sa.Enum('abstract', 'claims', 'description'),
                    existing_nullable=False)
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
