"""Add edited_at to task_assignment

Revision ID: 8b8b0155ef8b
Revises: 8c8d75532052
Create Date: 2024-12-03 06:06:38.971431

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = '8b8b0155ef8b'
down_revision = '1460d7d0f550'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('task_assignment', sa.Column('edited_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('task_assignment', 'edited_at')
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
