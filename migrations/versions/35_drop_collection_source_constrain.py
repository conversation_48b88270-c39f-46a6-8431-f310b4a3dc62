"""empty message

Revision ID: 86f2bd132caf
Revises: c26ce893b8d1
Create Date: 2024-07-19 01:39:33.769684

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = '86f2bd132caf'
down_revision = 'c26ce893b8d1'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('CONSTRAINT_1', 'collection_source', type_='check')
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_check_constraint('CONSTRAINT_1', 'collection_source',
                               'NOT(search_history_id IS NULL AND monitor_run_id IS NULL)')
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
