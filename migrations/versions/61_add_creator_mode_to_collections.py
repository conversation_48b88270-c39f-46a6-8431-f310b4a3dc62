""" Add creation_mode to collections

Revision ID: 3d97250b887f
Revises: 174857c3c57d
Create Date: 2025-02-20 13:59:29.398745

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = '3d97250b887f'
down_revision = '174857c3c57d'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('collections', sa.Column('creation_mode', sa.Enum('DEFAULT', 'MONITOR', name='creation_mode'), nullable=True))
    session = Session(bind=op.get_bind())
    session.execute(sa.text("""
        UPDATE collections c
        SET c.creation_mode = 'DEFAULT'
    """))
    # The following query will set the creation_mode to MONITOR for collections
    # that have only one source and that source is a monitor run and were shared with other users/groups
    # This has a bug when user creates a monitor profile that also shares with some users/groups.
    # Then he creates a new collection with that profile and share to other users/groups.
    session.execute(sa.text("""
        UPDATE collections c
        JOIN collaborations coll ON c.id = coll.resource_id AND coll.resource_type = 'COLLECTION'
        SET c.creation_mode = 'MONITOR'
        WHERE coll.id IS NOT NULL AND c.id IN ( 
            SELECT DISTINCT cs.collection_id 
            FROM collection_source cs
            LEFT JOIN monitor_run mr ON cs.monitor_run_id = mr.id
            LEFT JOIN monitor_profile mp ON mr.profile_id = mp.id
            WHERE (mp.shared_users IS NOT NULL AND LENGTH(mp.shared_users) > 0)
               OR (mp.shared_groups IS NOT NULL AND LENGTH(mp.shared_groups) > 0)
        ) AND c.id IN (
                SELECT DISTINCT cs.collection_id
                FROM collection_source cs
                GROUP BY cs.collection_id
                HAVING COUNT(cs.id) = 1
                )
    """))
    session.commit()
    op.alter_column('collections', 'creation_mode', existing_type=mysql.ENUM('DEFAULT', 'MONITOR'), nullable=False)
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('collections', 'creation_mode')
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

