"""collection results metadata

Revision ID: d1f6a81eb173
Revises: 511c5c081c6d
Create Date: 2023-05-22 14:10:47.655288

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text

# revision identifiers, used by Alembic.
revision = 'd1f6a81eb173'
down_revision = '511c5c081c6d'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.add_column('collection_results', sa.Column('created_at', sa.DateTime(), nullable=True))
    op.add_column('collection_results', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.add_column('collection_results', sa.Column('user_id', sa.Integer(), nullable=True))
    op.execute(text('''
        update collection_results cr join collections c on cr.collection_id = c.id
        set
            cr.user_id = c.user_id,
            cr.created_at = c.created_at,
            cr.updated_at = c.updated_at
    '''))
    op.alter_column('collection_results', column_name='created_at', existing_type=sa.DateTime(), nullable=False)
    op.alter_column('collection_results', column_name='updated_at', existing_type=sa.DateTime(), nullable=False)
    op.alter_column('collection_results', column_name='user_id', existing_type=sa.Integer(), nullable=False)


def downgrade_():
    op.drop_column('collection_results', 'user_id')
    op.drop_column('collection_results', 'updated_at')
    op.drop_column('collection_results', 'created_at')


def upgrade_auth():
    pass


def downgrade_auth():
    pass
