"""Adding search_type parameter to web API search request and 

Revision ID: c83bf3613653
Revises: 2df32bde7dfb
Create Date: 2023-09-28 12:32:15.604459

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'c83bf3613653'
down_revision = '2df32bde7dfb'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('search_history', sa.Column('search_document_type', sa.Enum('FAMILY', 'PUBLICATION', name='esdocumenttype'), nullable=True))
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('search_history', 'search_document_type')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
