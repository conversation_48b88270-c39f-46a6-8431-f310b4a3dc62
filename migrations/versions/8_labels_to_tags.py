"""migrate document-wise labels to tags

Revision ID: 5896442443d3
Revises: f333916bdd27
Create Date: 2023-06-02 12:20:28.508101

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '5896442443d3'
down_revision = 'f333916bdd27'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    labels = _get_labels_to_migrate()
    for label in labels:
        tag_id = _create_tag(label)
        collection_id = _create_collection(label, tag_id)
        _share_tag_collection_in_company(label, collection_id)


def _get_labels_to_migrate():
    return op.get_bind().execute(sa.text('''
        select l.id, l.name, l.color, coalesce(l.user_id, dl.user_id) user_id, 
        coalesce(l.company_id, dl.company_id) company_id, l.created_at, l.updated_at
        from labels l
        join documents_labels dl on dl.label_id = l.id
        where dl.start_pos = 1 and dl.end_pos = 1 and dl.field = "abstract"
        group by l.id
    '''))


def _create_tag(label):
    op.get_bind().execute(
        sa.text('insert into tags (name, color, user_id, company_id, created_at, updated_at) '
                'values (:name, :color, :user_id, :company_id, :created_at, :updated_at)'),
        {
            'name': label.name,
            'color': label.color,
            'user_id': label.user_id,
            'company_id': label.company_id,
            'created_at': label.created_at,
            'updated_at': label.updated_at,
        }
    )
    return op.get_bind().execute(sa.text('select last_insert_id()')).scalar()


def _create_collection(label, tag_id):
    doc_labels = _get_document_labels(label.id)
    op.get_bind().execute(
        sa.text('insert into collections (name, user_id, source_id, source_type, created_at, updated_at) '
                'values (:name, :user_id, :source_id, :source_type, :created_at, :updated_at)'),
        {
            'name': label.name,
            'user_id': label.user_id,
            'source_id': tag_id,
            'source_type': 'TAG',
            'created_at': label.created_at,
            'updated_at': label.updated_at
        }
    )
    collection_id = op.get_bind().execute(sa.text('select last_insert_id()')).scalar()
    op.get_bind().execute(
        sa.text('insert into collection_results (document_id, collection_id, user_id, created_at, updated_at) '
                'values (:doc_id, :collection_id, :user_id, :created_at, :updated_at)'),
        [
            {
                'doc_id': dl.document_id,
                'collection_id': collection_id,
                'user_id': dl.user_id,
                'created_at': dl.created_at,
                'updated_at': dl.created_at
            }
            for dl in doc_labels
        ]
    )
    op.get_bind().execute(
        sa.text('update tags set collection_id = :collection_id where id = :tag_id'),
        {'collection_id': collection_id, 'tag_id': tag_id}
    )
    return collection_id


def _share_tag_collection_in_company(label, collection_id):
    if not label.company_id:
        return
    op.get_bind().execute(
        sa.text('insert into collaborations '
                '(resource_id, owner_id, collaborator_id, permission,'
                ' status, shared_at, resource_type, updated_at, collaborator_type)'
                ' values '
                '(:resource_id, :owner_id, :collaborator_id, :permission, :status, :shared_at, :resource_type,'
                ' :updated_at, :collaborator_type)'),
        {
            'resource_id': collection_id,
            'owner_id': label.user_id,
            'collaborator_id': label.company_id,
            'permission': 'READ_WRITE',
            'status': 'READ',
            'shared_at': label.created_at,
            'resource_type': 'COLLECTION',
            'updated_at': label.created_at,
            'collaborator_type': 'COMPANY'
        }
    )


def _get_document_labels(label_id):
    res = op.get_bind().execute(sa.text('''
        select dl.document_id, dl.user_id, dl.company_id, dl.created_at
        from labels l
        join documents_labels dl on dl.label_id = l.id
        where dl.start_pos = 1 and dl.end_pos = 1 and dl.field = "abstract" and label_id = :label_id
    '''), {'label_id': label_id})
    return res


def downgrade_():
    pass


def upgrade_auth():
    pass


def downgrade_auth():
    pass
