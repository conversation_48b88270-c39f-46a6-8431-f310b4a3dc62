"""tags

Revision ID: e1718a1422f9
Revises: 78c613a0baa9
Create Date: 2023-04-13 08:16:13.883362

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e1718a1422f9'
down_revision = '8ae268d6af92'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        'tags',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('company_id', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('color', sa.String(length=6), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tags_company_id'), 'tags', ['company_id'], unique=False)
    op.create_index(op.f('ix_tags_user_id'), 'tags', ['user_id'], unique=False)
    op.create_table(
        'tag_documents',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('tag_id', sa.Integer(), nullable=True),
        sa.Column('document_id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('company_id', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tag_documents_company_id'), 'tag_documents', ['company_id'], unique=False)
    op.create_index(op.f('ix_tag_documents_user_id'), 'tag_documents', ['user_id'], unique=False)


def downgrade_():
    op.drop_index(op.f('ix_tag_documents_user_id'), table_name='tag_documents')
    op.drop_index(op.f('ix_tag_documents_company_id'), table_name='tag_documents')
    op.drop_table('tag_documents')
    op.drop_index(op.f('ix_tags_user_id'), table_name='tags')
    op.drop_index(op.f('ix_tags_company_id'), table_name='tags')
    op.drop_table('tags')


def upgrade_auth():
    pass


def downgrade_auth():
    pass


