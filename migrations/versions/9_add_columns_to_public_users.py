"""empty message

Revision ID: 7753df249f59
Revises: f333916bdd27
Create Date: 2023-06-16 20:40:59.387162

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '7753df249f59'
down_revision = '5896442443d3'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('public_users', sa.Column('first_name', sa.String(255), nullable=True))
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('public_users', 'first_name')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
