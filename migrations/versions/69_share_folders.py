"""share folders

Revision ID: be662a254675
Revises: 743e29608ce3
Create Date: 2025-03-20 11:44:24.750033

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'be662a254675'
down_revision = '743e29608ce3'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    op.alter_column(
        'collaborations',
        'resource_type',
        existing_type=sa.Enum('COLLECTION', 'PATENT', 'COPIED_MONITOR', 'LANDSCAPE', 'PATENT_COMMENT', 'PATENT_TAG', 'REPLY_ON_TAGGED_COMMENT', name='collaborationresourcetype'),
        type_=sa.Enum('COLLECTION', 'FOLDER', 'PATENT', 'COPIED_MONITOR', 'LANDSCAPE', 'PATENT_COMMENT', 'PATENT_TAG', 'REPLY_ON_TAGGED_COMMENT', name='collaborationresourcetype'),
        existing_nullable=False
    )
    op.alter_column(
        'notifications',
        'resource_type',
        existing_type=sa.Enum('MONITOR_PROFILE', 'LANDSCAPE_PROFILE', 'MONITOR_RUN', 'TASK', 'COLLECTION', 'PATENT', name='resourcetype'),
        type_=sa.Enum('MONITOR_PROFILE', 'LANDSCAPE_PROFILE', 'MONITOR_RUN', 'TASK', 'COLLECTION', 'FOLDER', 'PATENT', name='resourcetype'),
        existing_nullable=True,
    )


def downgrade_():
    op.alter_column(
        'collaborations',
        'resource_type',
        existing_type=sa.Enum('COLLECTION', 'FOLDER', 'PATENT', 'COPIED_MONITOR', 'LANDSCAPE', 'PATENT_COMMENT', 'PATENT_TAG', 'REPLY_ON_TAGGED_COMMENT', name='collaborationresourcetype'),
        type_=sa.Enum('COLLECTION', 'PATENT', 'COPIED_MONITOR', 'LANDSCAPE', 'PATENT_COMMENT', 'PATENT_TAG', 'REPLY_ON_TAGGED_COMMENT', name='collaborationresourcetype'),
        existing_nullable=False
    )
    op.alter_column(
        'notifications',
        'resource_type',
        existing_type=sa.Enum('MONITOR_PROFILE', 'LANDSCAPE_PROFILE', 'MONITOR_RUN', 'TASK', 'COLLECTION', 'FOLDER' 'PATENT', name='resourcetype'),
        type_=sa.Enum('MONITOR_PROFILE', 'LANDSCAPE_PROFILE', 'MONITOR_RUN', 'TASK', 'COLLECTION', 'PATENT', name='resourcetype'),
        existing_nullable=True,
    )

def upgrade_auth():
    pass


def downgrade_auth():
    pass

