"""empty message

Revision ID: cafd3d4ac8ac
Revises: 
Create Date: 2023-06-15 10:42:05.701890

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'f0d8df3f12f9'
down_revision = None
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('companies',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('active', sa.<PERSON>(), nullable=False),
    sa.Column('valid_to', sa.DateTime(), nullable=True),
    sa.Column('name', sa.String(length=128), nullable=False),
    sa.Column('seats', sa.Integer(), nullable=True),
    sa.Column('free_seats', sa.Integer(), nullable=True),
    sa.Column('domains', sa.String(length=256), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('default_subscription_type', sa.Enum('FREE', 'TRIAL', 'BASIC', 'PROFESSIONAL', 'ENTERPRISE', name='subscriptiontype'), nullable=True),
    sa.Column('default_subscription_valid_until', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('features',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('short_name', sa.String(length=2), nullable=False),
    sa.Column('name', sa.String(length=25), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    sa.UniqueConstraint('short_name')
    )
    op.execute("""INSERT INTO features (short_name, name) VALUES ('M', 'Monitor')""")
    op.execute("""INSERT INTO features (short_name, name) VALUES ('L', 'Landscape')""")
    op.create_table('groups',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('users',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.String(length=16), nullable=False),
    sa.Column('type', sa.String(length=16), nullable=True),
    sa.Column('activated_at', sa.DateTime(), nullable=True),
    sa.Column('valid_to', sa.DateTime(), nullable=True),
    sa.Column('status', sa.Enum('Active', 'Registered', 'Blocked', 'Delegated', name='userstatus'), nullable=True),
    sa.Column('company_id', sa.Integer(), nullable=True),
    sa.Column('delegation_source', sa.String(length=256), nullable=True),
    sa.Column('is_admin', sa.Boolean(), nullable=False),
    sa.Column('is_manager', sa.Boolean(), nullable=False),
    sa.Column('is_sales', sa.Boolean(), nullable=False),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('locale', sa.String(length=5), nullable=True),
    sa.Column('translation_api_key', sa.String(length=256), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['companies.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('api_users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=False),
    sa.Column('api_key', sa.String(length=64), nullable=False),
    sa.Column('public_key', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('api_key'),
    sa.UniqueConstraint('name')
    )
    op.create_table('app_users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=100), nullable=False),
    sa.Column('password', sa.String(length=128), nullable=False),
    sa.Column('first_name', sa.String(length=100), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('company_name', sa.String(length=100), nullable=True),
    sa.Column('department_name', sa.String(length=100), nullable=True),
    sa.Column('save_history', sa.Boolean(), nullable=False),
    sa.Column('phone1', sa.String(length=32), nullable=True),
    sa.Column('phone2', sa.String(length=32), nullable=True),
    sa.Column('mobile', sa.String(length=32), nullable=True),
    sa.Column('country', sa.String(length=64), nullable=True),
    sa.Column('website', sa.String(length=128), nullable=True),
    sa.Column('ui_settings', sa.JSON(), nullable=True),
    sa.Column('newsletter', sa.Boolean(), nullable=False),
    sa.Column('is_searchable', sa.Boolean(), nullable=False),
    sa.Column('terms_and_conditions', sa.Boolean(), nullable=False),
    sa.Column('gdpr', sa.Boolean(), nullable=True),
    sa.Column('must_change_password', sa.Boolean(), nullable=False),
    sa.Column('crm_id', sa.String(length=256), nullable=True),
    sa.Column('two_factor_secret', sa.String(length=128), nullable=True),
    sa.ForeignKeyConstraint(['id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email')
    )
    op.create_table('subscriptions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.Enum('FREE', 'TRIAL', 'BASIC', 'COLLABORATOR', 'PROFESSIONAL', 'ENTERPRISE', name='subscriptiontype'), nullable=False),
    sa.Column('current_term_started_at', sa.TIMESTAMP(), nullable=True),
    sa.Column('valid_until', sa.TIMESTAMP(), nullable=True),
    sa.Column('api_package', sa.Integer(), nullable=True),
    sa.Column('api_access_throttle', sa.Integer(), nullable=True),
    sa.Column('api_max_result', sa.Integer(), nullable=True),
    sa.Column('external_subscription_id', sa.Integer(), nullable=True),
    sa.Column('api_max_monitor_profile', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_table('trial_emails',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('send_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('params', sa.JSON(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_custom_activities',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_custom_activities_created_at'), 'user_custom_activities', ['created_at'], unique=False)
    op.create_index(op.f('ix_user_custom_activities_type'), 'user_custom_activities', ['type'], unique=False)
    op.create_index(op.f('ix_user_custom_activities_user_id'), 'user_custom_activities', ['user_id'], unique=False)
    op.create_table('user_downloads',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('source_type', sa.String(length=25), nullable=False),
    sa.Column('format', sa.String(length=10), nullable=False),
    sa.Column('size', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_downloads_created_at'), 'user_downloads', ['created_at'], unique=False)
    op.create_index(op.f('ix_user_downloads_source_type'), 'user_downloads', ['source_type'], unique=False)
    op.create_index(op.f('ix_user_downloads_user_id'), 'user_downloads', ['user_id'], unique=False)
    op.create_table('user_exports',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('export_format', sa.String(length=10), nullable=False),
    sa.Column('size', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_exports_created_at'), 'user_exports', ['created_at'], unique=False)
    op.create_index(op.f('ix_user_exports_user_id'), 'user_exports', ['user_id'], unique=False)
    op.create_table('user_features',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('feature_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['feature_id'], ['features.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'feature_id')
    )
    op.create_table('user_images',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('image', sa.BLOB(), nullable=False),
    sa.Column('content_type', sa.String(length=15), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id')
    )
    op.create_table('user_journal',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('modifier_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['modifier_id'], ['users.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_recurring_searches',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('search_type', sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'PATENT_NUMBER', 'DOCUMENT_ID', 'NPL', name='searchtype'), nullable=False),
    sa.Column('search_filters', sa.Text(), nullable=True),
    sa.Column('patent_numbers', sa.Text().with_variant(mysql.MEDIUMTEXT(), 'mysql'), nullable=True),
    sa.Column('search_hash', sa.String(length=65), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_recurring_searches_created_at'), 'user_recurring_searches', ['created_at'], unique=False)
    op.create_index(op.f('ix_user_recurring_searches_user_id'), 'user_recurring_searches', ['user_id'], unique=False)
    op.create_table('user_searches',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('search_type', sa.Enum('SEMANTIC', 'CITATION', 'BOOLEAN', 'PATENT_NUMBER', 'DOCUMENT_ID', 'NPL', name='searchtype'), nullable=False),
    sa.Column('query_length', sa.Integer(), nullable=False),
    sa.Column('search_filters', sa.Text(), nullable=True),
    sa.Column('patent_numbers', sa.Text().with_variant(mysql.MEDIUMTEXT(), 'mysql'), nullable=True),
    sa.Column('search_hash', sa.String(length=65), nullable=True),
    sa.Column('results', sa.Text().with_variant(mysql.MEDIUMTEXT(), 'mysql'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_searches_created_at'), 'user_searches', ['created_at'], unique=False)
    op.create_index(op.f('ix_user_searches_user_id'), 'user_searches', ['user_id'], unique=False)
    op.create_table('users_groups',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'group_id')
    )
    op.create_table('users_sales',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('sale_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['sale_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'sale_id')
    )
    op.create_table('user_journal_entries',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('journal_id', sa.Integer(), nullable=False),
    sa.Column('field_name', sa.String(length=255), nullable=False),
    sa.Column('previous_value', sa.String(length=255), nullable=True),
    sa.Column('new_value', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['journal_id'], ['user_journal.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    _create_initial_users()


def _create_initial_users():
    op.execute(text("""
       INSERT INTO companies (created_at,updated_at,active,name) 
       VALUES
        ('2015-08-14','2021-09-16',1,'Test Company')
    """))
    op.execute(text(f"""
       INSERT INTO users (created_at,updated_at,user_id,`type`,status,company_id,is_admin,is_manager,is_sales) 
       VALUES 
        ('2023-11-15','2023-11-15','B0KRX67JQP','app_user','Active',1,1,1,0),
        ('2023-11-15','2023-11-15','B0KRX67JQQ','app_user','Active',1,0,0,0),
        ('2023-11-15','2023-11-15','B0KRX67JQR','app_user','Active',1,0,0,0),
        ('2023-11-15','2023-11-15','B0KRX67JQS','app_user','Active',1,0,0,0),
        ('2023-11-15','2023-11-15','B0KRX67JQT','app_user','Active',1,0,0,0),
        ('2023-11-15','2023-11-15','B0KRX67JQU','app_user','Active',NULL,0,0,0),
        ('2023-11-15','2023-11-15','B0KRX67JQV','api_user','Active',NULL,0,0,0)
    """))
    pw = '$2b$12$vl/Qcotuq5DOd5n.3CMIBuufy96YCdxZPqJ/vmR156eWY09MzTd8K'
    ui_settings = '{"first_login": true, "charts": ["basic"], "dashboard_charts": []}'
    op.execute(text(f"""
       INSERT INTO app_users (
        id, email,password,first_name,last_name,save_history,country,ui_settings,newsletter,is_searchable,
        terms_and_conditions,gdpr,must_change_password
       ) 
       VALUES
         (1, '<EMAIL>','{pw}','Test','Admin',1,'Germany','{ui_settings}',0,0,1,1,0),
         (2, '<EMAIL>','{pw}','Test','Enterprise',1,'Germany','{ui_settings}',0,0,1,1,0),
         (3, '<EMAIL>','{pw}','Test','Professional',1,'Germany','{ui_settings}',0,0,1,1,0),
         (4, '<EMAIL>','{pw}','Test','Free',1,'Germany','{ui_settings}',0,0,1,1,0),
         (5, '<EMAIL>','{pw}','Test','Collaborator',1,'Germany','{ui_settings}',0,0,1,1,0),
         (6, '<EMAIL>','{pw}','Test','Single',1,'Germany','{ui_settings}',0,0,1,1,0)
    """))
    api_key = "354104DF855F4563B9CE437D6CF1CC59"
    public_key = """
-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEA3ttonBe620ID8WtRhxH2iuc9nn58K1zveWO5iJeCWdCgf/PnFoCw
CYsAvs2mfOAch3VcM6qoW3vQZVJQUsoAFBR288qHHR2N36jJ4aJ325uJpdnuh7ya
9YIJTphHW0DwH4vhgfcf0IAkkgtwDxT/IxQiEw0PrYO5E0A2bTxXmbSxJ4MBWIve
Nqt0z/zgYewiqkIq6gsrg/ak69AezMN1GYjlMY51qm7SWb4m1uLtzBghEOm1tvdE
i59zpJYEiuR5LsU/25p2VFgC5Tzadeixxdo2q3+kTTfdW8H8tWwrrErK1xmXq9SJ
LzEHxj5BApCrOgSQQbKI1sEviPjBcr7QMQIDAQAB
-----END RSA PUBLIC KEY-----
    """
    op.execute(text(f"""
       INSERT INTO api_users (id, name, api_key, public_key)
       VALUES
         (7, 'SMOKE_TEST', '{api_key}', '{public_key.strip()}')
    """))
    op.execute(text(f"""
       INSERT INTO subscriptions (user_id,`type`,created_at) 
       VALUES 
        (1,'ENTERPRISE','2015-02-20'),
        (2,'ENTERPRISE','2015-02-20'),
        (3,'PROFESSIONAL','2015-02-20'),
        (4,'FREE','2015-02-20'),
        (5,'COLLABORATOR','2015-02-20'),
        (6,'TRIAL','2015-02-20'),
        (7,'ENTERPRISE','2015-02-20')
    """))
    op.execute(text(f"""
       INSERT INTO user_features
       VALUES
        (1, 1),
        (1, 2),
        (2, 1),
        (2, 2),
        (4, 1),
        (7, 1),
        (7, 2)
    """))
    op.execute(text(f"""
       INSERT INTO groups(name, company_id, created_at, updated_at)
       VALUES
        ('Test group', 1, '2015-02-20', '2015-02-20')
    """))
    op.execute(text(f"""
       INSERT INTO users_groups
       VALUES
        (2, 1),
        (3, 1)
    """))


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_journal_entries')
    op.drop_table('users_sales')
    op.drop_table('users_groups')
    op.drop_index(op.f('ix_user_searches_user_id'), table_name='user_searches')
    op.drop_index(op.f('ix_user_searches_created_at'), table_name='user_searches')
    op.drop_table('user_searches')
    op.drop_index(op.f('ix_user_recurring_searches_user_id'), table_name='user_recurring_searches')
    op.drop_index(op.f('ix_user_recurring_searches_created_at'), table_name='user_recurring_searches')
    op.drop_table('user_recurring_searches')
    op.drop_table('user_journal')
    op.drop_table('user_images')
    op.drop_table('user_features')
    op.drop_index(op.f('ix_user_exports_user_id'), table_name='user_exports')
    op.drop_index(op.f('ix_user_exports_created_at'), table_name='user_exports')
    op.drop_table('user_exports')
    op.drop_index(op.f('ix_user_downloads_user_id'), table_name='user_downloads')
    op.drop_index(op.f('ix_user_downloads_source_type'), table_name='user_downloads')
    op.drop_index(op.f('ix_user_downloads_created_at'), table_name='user_downloads')
    op.drop_table('user_downloads')
    op.drop_index(op.f('ix_user_custom_activities_user_id'), table_name='user_custom_activities')
    op.drop_index(op.f('ix_user_custom_activities_type'), table_name='user_custom_activities')
    op.drop_index(op.f('ix_user_custom_activities_created_at'), table_name='user_custom_activities')
    op.drop_table('user_custom_activities')
    op.drop_table('trial_emails')
    op.drop_table('subscriptions')
    op.drop_table('app_users')
    op.drop_table('api_users')
    op.drop_table('users')
    op.drop_table('groups')
    op.drop_table('features')
    op.drop_table('companies')
    # ### end Alembic commands ###
