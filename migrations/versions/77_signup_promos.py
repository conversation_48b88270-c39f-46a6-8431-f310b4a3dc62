"""signup promotional codes

Revision ID: 3619a2ff6ded
Revises: 7cf23338bcf9
Create Date: 2025-06-12 12:50:32.140414

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3619a2ff6ded'
down_revision = '7cf23338bcf9'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()





def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    op.create_table(
        'promos',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('enabled', sa.<PERSON>(), nullable=False),
        sa.Column('code', sa.String(length=256), nullable=False),
        sa.Column('description', sa.String(length=256), nullable=True),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('rules', sa.JSON(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code')
    )
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.add_column(sa.Column('promo_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('subscriptions_ibfk_2', 'promos', ['promo_id'], ['id'], ondelete='SET NULL')


def downgrade_auth():
    with op.batch_alter_table('subscriptions', schema=None) as batch_op:
        batch_op.drop_constraint('subscriptions_ibfk_2', type_='foreignkey')
        batch_op.drop_column('promo_id')
    op.drop_table('promos')

