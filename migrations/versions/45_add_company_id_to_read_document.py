"""Add company_id to read_document

Revision ID: 8c8d75532052
Revises: fe89bfd4d499
Create Date: 2024-11-20 04:22:46.599573

"""
from typing import List

import sqlalchemy as sa
from alembic import op
from sqlalchemy import select
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = '8c8d75532052'
down_revision = 'fe89bfd4d499'
branch_labels = None
depends_on = None

Base = declarative_base()
session = Session(bind=op.get_bind())


class ReadDocument(Base):
    __tablename__ = 'read_document'

    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    user_id = sa.Column(sa.Integer, nullable=False)
    company_id = sa.Column(sa.Integer, nullable=True)


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('read_document', sa.Column('company_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_read_document_company_id'), 'read_document', ['company_id'], unique=False)
    _update_company_ids()
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_read_document_company_id'), table_name='read_document')
    op.drop_column('read_document', 'company_id')
    # ### end Alembic commands ###


def upgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade_auth():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def _update_company_ids():
    read_documents = session.execute(
        select(ReadDocument)
    ).scalars().all()

    user_ids = list(set([rd.user_id for rd in read_documents]))
    users = _get_users(user_ids)
    users_dict = {user_id: company_id for user_id, company_id in users}

    for rd in read_documents:
        rd.company_id = users_dict.get(rd.user_id, None)
    session.commit()


def _get_users(user_ids: List[int]) -> List[dict]:
    if not user_ids:
        return []
    return session.execute(
        sa.text("select id, company_id from octimine_auth.users where id in :user_ids"),
        {'user_ids': user_ids}
    ).all()
