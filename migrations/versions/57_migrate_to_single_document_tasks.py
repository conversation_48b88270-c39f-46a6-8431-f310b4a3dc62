"""Migrate to single document tasks

Revision ID: 1bd48db5435d
Revises: 0bd48db5435d
Create Date: 2025-02-18 10:24:50.843731

"""
from enum import Enum
from alembic import op
import sqlalchemy as sa
from sqlalchemy import select
from collections import defaultdict
from datetime import datetime
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.orm.session import Session

# revision identifiers, used by Alembic.
revision = '1bd48db5435d'
down_revision = '0bd48db5435d'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


Base = declarative_base()


class TaskStatusEnum(Enum):
    NEW = "NEW"
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    DONE = "DONE"


class AssignedTaskStatusEnum(Enum):
    NEW = "NEW"
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    DONE = "DONE"
    OVERDUE = "OVERDUE"


class TaskAnswerTypeEnum(Enum):
    STAR_RATING = "STAR_RATING"

tasks_topics = sa.Table(
    'tasks_topics',
    Base.metadata,
    sa.Column('id', sa.Integer, primary_key=True, autoincrement=True),
    sa.Column('task_id', sa.Integer, sa.ForeignKey('task.id', ondelete='CASCADE')),
    sa.Column('topic_id', sa.Integer, sa.ForeignKey('topics.id', ondelete='CASCADE'))
)


class TopicModel(Base):
    MAX_NAME_LENGTH = 255

    __tablename__ = 'topics'

    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)


class TaskAssignmentModel(Base):
    MAX_ANSWER_LENGTH = 1000

    __tablename__ = 'task_assignment'

    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    task_id = sa.Column(sa.Integer, sa.ForeignKey('task.id', ondelete='CASCADE'), nullable=False)
    task = relationship('TaskModel', lazy="select", back_populates='assignments')
    document_id = sa.Column(sa.Integer, nullable=True, index=True)
    status = sa.Column(sa.Enum(AssignedTaskStatusEnum), nullable=False)
    answer = sa.Column(sa.String(MAX_ANSWER_LENGTH), nullable=True)


class TaskModel(Base):
    SUBJECT_MAX_LENGTH = 255
    DESCRIPTION_MAX_LENGTH = 1000

    __tablename__ = 'task'

    id = sa.Column(sa.Integer, primary_key=True, autoincrement=True)
    assignments = relationship(TaskAssignmentModel, cascade='all, delete, delete-orphan', back_populates="task")
    author_id = sa.Column(sa.Integer, nullable=False)
    company_id = sa.Column(sa.Integer, nullable=True)
    document_id = sa.Column(sa.Integer, nullable=False)
    created_at = sa.Column(sa.DateTime(), default=datetime.now, nullable=False)
    updated_at = sa.Column(sa.DateTime(), default=datetime.now, onupdate=datetime.now, nullable=False)
    deadline = sa.Column(sa.DateTime(), nullable=True)
    subject = sa.Column(sa.String(SUBJECT_MAX_LENGTH), nullable=False)
    description = sa.Column(sa.String(DESCRIPTION_MAX_LENGTH), nullable=True)
    task_type = sa.Column(sa.Enum(TaskAnswerTypeEnum), nullable=False)
    status = sa.Column(sa.Enum(TaskStatusEnum), nullable=False, index=True)
    topics = relationship(TopicModel, secondary=tasks_topics, lazy='selectin')



def upgrade_():
    _migrate_to_single_doc_tasks()


def _migrate_to_single_doc_tasks():
    session = Session(bind=op.get_bind())
    tasks = session.execute(
        select(TaskModel).where(TaskModel.document_id.is_(None))
    ).scalars().all()
    for t in tasks:
        doc_assignments = _group_task_assignments_by_document_id(t)
        single_doc_tasks = _single_doc_tasks_from_assignments(t, doc_assignments)
        for sdt in single_doc_tasks:
            if not sdt.id:
                session.add(sdt)
            elif sdt.document_id == 0:  # Way to detect tasks with no assignments
                session.delete(sdt)
        session.commit()


def _group_task_assignments_by_document_id(task):
    res = defaultdict(lambda: [])
    for ta in task.assignments:
        res[ta.document_id].append(ta)
    return res

def _single_doc_tasks_from_assignments(task, doc_assignments):
    res = []
    task.document_id = 0
    for doc_id, assignments in doc_assignments.items():
        t = _copy_task(task) if res else task
        t.assignments = assignments
        t.document_id = doc_id
        _update_task_status(t)
        res.append(t)
    return res


def _copy_task(task):
    return TaskModel(
        author_id=task.author_id,
        company_id=task.company_id,
        created_at=task.created_at,
        updated_at=task.updated_at,
        deadline=task.deadline,
        subject=task.subject,
        description=task.description,
        task_type=task.task_type,
        status=task.status,
        topics=task.topics
    )


def _update_task_status(task):
    if task.status == TaskStatusEnum.CLOSED:
        for ta in task.assignments:
            ta.status = AssignedTaskStatusEnum.CLOSED
    else:
        for ta in task.assignments:
            if ta.status == AssignedTaskStatusEnum.CLOSED or ta.status == AssignedTaskStatusEnum.OVERDUE:
                ta.status = AssignedTaskStatusEnum.OPEN
            if ta.answer:
                ta.status = AssignedTaskStatusEnum.DONE
            if ta.status in [AssignedTaskStatusEnum.NEW, AssignedTaskStatusEnum.OPEN] \
                    and task.deadline and datetime.now() > task.deadline:
                ta.status = AssignedTaskStatusEnum.OVERDUE

        if all(ta.status == AssignedTaskStatusEnum.DONE for ta in task.assignments):
            task.status = TaskStatusEnum.DONE
        else:
            task.status = TaskStatusEnum.OPEN


def downgrade_():
    pass

def upgrade_auth():
    pass


def downgrade_auth():
    pass
