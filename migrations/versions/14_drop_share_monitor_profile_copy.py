"""empty message

Revision ID: 2df32bde7dfb
Revises: 1a9c8dd7d2e4
Create Date: 2023-09-19 07:39:04.157691

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '2df32bde7dfb'
down_revision = '1a9c8dd7d2e4'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('owner_id', table_name='monitor_profile_shared_copies')
    op.drop_table('monitor_profile_shared_copies')
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('monitor_profile_shared_copies',
                    sa.Column('id', mysql.INTEGER(display_width=11), autoincrement=True, nullable=False),
                    sa.Column('version_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
                    sa.Column('owner_id', mysql.VARCHAR(length=16), nullable=False),
                    sa.Column('owner_details', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=False),
                    sa.Column('shared_with', mysql.VARCHAR(length=16), nullable=False),
                    sa.Column('shared_with_details', mysql.LONGTEXT(charset='utf8mb4', collation='utf8mb4_bin'), nullable=False),
                    sa.Column('status', mysql.ENUM('PENDING', 'SHARED'), nullable=False),
                    sa.Column('original_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=False),
                    sa.Column('copy_id', mysql.INTEGER(display_width=11), autoincrement=False, nullable=True),
                    sa.ForeignKeyConstraint(['copy_id'], ['monitor_profile.id'], name='monitor_profile_shared_copies_ibfk_1', ondelete='CASCADE'),
                    sa.ForeignKeyConstraint(['original_id'], ['monitor_profile.id'], name='monitor_profile_shared_copies_ibfk_2', ondelete='CASCADE'),
                    sa.PrimaryKeyConstraint('id'),
                    mysql_default_charset='utf8mb4',
                    mysql_engine='InnoDB'
                    )
    op.create_index('owner_id', 'monitor_profile_shared_copies', ['owner_id', 'original_id', 'shared_with'], unique=False)
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
