"""empty message

Revision ID: 5cae59812ae4
Revises: 7753df249f59
Create Date: 2023-07-13 16:35:56.245392

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '5cae59812ae4'
down_revision = '7753df249f59'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents_comments', sa.Column('source', sa.Enum('annotation', 'explainer', name='source'),
                                                  nullable=True))
    op.execute(text('''
           update documents_comments dc 
           set dc.source = 'annotation'
       '''))
    # ### end Alembic commands ###


def downgrade_():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('documents_comments', 'source')
    # ### end Alembic commands ###


def upgrade_auth():
    pass


def downgrade_auth():
    pass
