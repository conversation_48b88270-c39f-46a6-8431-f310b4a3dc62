"""empty message

Revision ID: 3f0096181a02
Revises: c51627800ca8
Create Date: 2024-03-05 13:24:15.372548

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '3f0096181a02'
down_revision = 'c51627800ca8'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    pass


def downgrade_():
    pass


def upgrade_auth():
    op.drop_column('companies', 'default_subscription_type')
    op.drop_column('companies', 'default_subscription_valid_until')


def downgrade_auth():
    op.add_column('companies', sa.Column('default_subscription_type', sa.Enum('FREE', 'TRIAL', 'BASIC', 'PROFESSIONAL', 'ENTERPRISE', name='subscriptiontype'), nullable=True))
    op.add_column('companies', sa.Column('default_subscription_valid_until', sa.DateTime(), nullable=True))
