"""Add single document tasks columns

Revision ID: 0bd48db5435d
Revises: bcd385358fb3
Create Date: 2025-02-18 10:24:50.843731

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '0bd48db5435d'
down_revision = 'bcd385358fb3'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()


def upgrade_():
    with op.batch_alter_table('task', schema=None) as batch_op:
        batch_op.add_column(sa.Column('document_id', sa.Integer(), nullable=True))
        batch_op.create_index(batch_op.f('ix_task_document_id'), ['document_id'], unique=False)


def downgrade_():
    with op.batch_alter_table('task', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_task_document_id'))
        batch_op.drop_column('document_id')

def upgrade_auth():
    pass


def downgrade_auth():
    pass
