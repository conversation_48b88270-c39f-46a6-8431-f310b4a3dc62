"""Fix IPLounge shared landscape permissions

Revision ID: d6bba7b1c8d9
Revises: f93617a64081
Create Date: 2024-07-09 15:41:12.309913

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = 'd6bba7b1c8d9'
down_revision = 'f93617a64081'
branch_labels = None
depends_on = None


def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()

def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()

def upgrade_():
    if not _apply_migration():
        return
    print('applying IP lounge migration')
    ip_lounge_user_id = _get_ip_lounge_user_id()
    _downgrade_shared_landscapes_permissions(ip_lounge_user_id)

def downgrade_():
    pass

def upgrade_auth():
    pass

def downgrade_auth():
    pass


def _apply_migration():
    res = op.get_bind().execute(sa.text("SELECT * FROM information_schema.tables WHERE table_schema = 'octimine_auth' AND table_name = 'api_users'")).scalars().all()
    return bool(res)



def _get_ip_lounge_user_id():
    return op.get_bind().execute(sa.text("select au.id from octimine_auth.api_users au where au.name = 'IP_LOUNGE'")).scalar()


def _downgrade_shared_landscapes_permissions(user_id):
    op.get_bind().execute(
        sa.text("update collaborations set permission = 'READONLY' where owner_id = :user_id and resource_type = 'LANDSCAPE'"),
        {'user_id': user_id}
    )
