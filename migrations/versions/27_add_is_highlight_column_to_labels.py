"""empty message

Revision ID: e4627ddf1e16
Revises: c51627800ca8
Create Date: 2024-03-07 05:28:43.075333

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'e4627ddf1e16'
down_revision = '3f0096181a02'
branch_labels = None
depends_on = None

def upgrade(engine_name):
    globals()["upgrade_%s" % engine_name]()


def downgrade(engine_name):
    globals()["downgrade_%s" % engine_name]()

    
def upgrade_():
   # Add the new column
    op.add_column('labels', sa.Column('is_highlight', sa.<PERSON>an(), nullable=False, default=False))

    # Insert new records
    _insert_new_records()


def _insert_new_records():
    date = {'created_at': datetime.now(), 'updated_at': datetime.now()}
    records = [
        {**{'name': 'Highlight yellow', 'color': 'F8FFA8', 'is_highlight': 1}, **date}, 
        {**{'name': 'Highlight green', 'color': 'D5F6C6', 'is_highlight': 1}, **date},  
        {**{'name': 'Highlight red', 'color': 'FFD2C9', 'is_highlight': 1}, **date},    
        {**{'name': 'Highlight pink', 'color': 'FFD7EA', 'is_highlight': 1}, **date}, 
        {**{'name': 'Highlight blue', 'color': 'BDFEF2', 'is_highlight': 1}, **date},   
    ]

    for record in records:
        op.execute(
            "INSERT INTO labels (name, color, is_highlight, created_at, updated_at) VALUES ('{name}', '{color}', '{is_highlight}', '{created_at}', '{updated_at}')".format(**record)
        )


def downgrade_():
    op.drop_column('labels', 'is_highlight')


def upgrade_auth():
    pass


def downgrade_auth():
    pass