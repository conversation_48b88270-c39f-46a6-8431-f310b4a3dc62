# Standard lib imports
import logging
from http import HTTPStatus

# Local imports
from octimine_common.messages import ERROR_RESOURCE_NOT_FOUND, ERROR_FORBIDDEN, ERROR_BAD_REQUEST, ERROR_UNAUTHORIZED, \
    ERROR_CONFLICT, ERROR_INTERNAL_SERVER_ERROR


class APIException(Exception):
    def __init__(self, code: int, message: str = None):
        super().__init__(message)
        self.message = message
        self.code = code

    def get_additional_data(self):
        """
        Subclasses might override this to include additional data into error response
        :return: dictionary with additional data to be included into the JSON response
        """
        return {}

    @staticmethod
    def get_log_level():
        return logging.ERROR

    def __str__(self):
        return self.message or self.__class__.__name__


class NotFoundException(APIException):
    def __init__(self, message=ERROR_RESOURCE_NOT_FOUND, code=HTTPStatus.NOT_FOUND):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.WARNING


class BadRequestException(APIException):
    def __init__(self, message=ERROR_BAD_REQUEST, code=HTTPStatus.BAD_REQUEST):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.WARNING


class ForbiddenException(APIException):
    def __init__(self, message=ERROR_FORBIDDEN, code=HTTPStatus.FORBIDDEN):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.WARNING


class UnauthorizedException(APIException):
    def __init__(self, message=ERROR_UNAUTHORIZED, code=HTTPStatus.UNAUTHORIZED):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.WARNING


class ConflictException(APIException):
    def __init__(self, message=ERROR_CONFLICT, code=HTTPStatus.CONFLICT):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.WARNING


class InternalServerException(APIException):
    def __init__(self, message=ERROR_INTERNAL_SERVER_ERROR, code=HTTPStatus.INTERNAL_SERVER_ERROR):
        APIException.__init__(self, message=message, code=code)

    @staticmethod
    def get_log_level():
        return logging.ERROR
