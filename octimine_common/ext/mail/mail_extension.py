import os
from email.utils import make_msgid
from urllib.parse import urlparse, quote
from flask import render_template, current_app
from flask_mail import Message, Mail
from app import urls


class MailExtension:
    """
    Extension for sending emails
    """

    class MultipartRelatedMessage(Message):
        """
        Needed since Flask-Mail does not allow setting a content-type
        """

        def __init__(self, *args, **kwargs):
            super(MailExtension.MultipartRelatedMessage, self).__init__(*args, **kwargs)

        def _message(self):
            msg = super(MailExtension.MultipartRelatedMessage, self)._message()
            if msg['Content-Type'] == 'multipart/mixed':
                msg.replace_header('Content-Type', 'multipart/related')
            return msg

    def __init__(self, app=None):
        self.mail = Mail()
        self.mailing_enabled = False
        if app is not None:
            self.init_app(app)

    def init_app(self, app):
        self.mail.init_app(app)
        self.mailing_enabled = app is not None and app.config['MAIL_SERVER'] is not None

    def send(self, *, subject: str, from_email: str | None = None, to_emails: list[str],
             template: str, params: dict, attachments: list[dict] | None = None,
             cc: list[str] | None = None):
        if not from_email:
            from_email = f'Octimine <{current_app.config["MAIL_DEFAULT_SENDER"]}>'
        if params.get('url'):
            if not urlparse(params['url']).netloc:
                params['url'] = urls.get_full_url(params['url'])
            if params.get('to_delegation_source') == 'IP_LOUNGE':
                params['url'] = f'{current_app.config["IP_LOUNGE_URL"]}login' \
                                f'?redirect_uri={quote(params["url"], safe="")}'

        message = self._build_message(
            subject=subject,
            sender=from_email,
            recipients=to_emails,
            reply_to=current_app.config['MAIL_DEFAULT_REPLY_TO'],
            template=template,
            params=params,
            cc=cc
        )
        if attachments:
            for a in attachments:
                message.attach(**a)
        for img in params.get('images', []):
            content_id = img.get('content_id', self._generate_content_id())
            message.attach(
                filename=img['filename'],
                content_type=img['content_type'],
                data=img['data'],
                disposition='inline',
                headers={'Content-ID': content_id}
            )
        self._send(message)

    def _send(self, message: Message):
        current_app.logger.info(f"Sending email: [msgid='{message.msgId}', subject='{message.subject}', "
                                f"to={message.recipients}]")
        if self.mailing_enabled:
            self.mail.send(message)

    @staticmethod
    def _generate_content_id():
        return make_msgid(domain=current_app.config['MAIL_MESSAGE_ID_DOMAIN'])

    @staticmethod
    def _attach_logo(message: Message, content_id: str):
        with current_app.open_resource(os.path.join(current_app.template_folder, 'mail', 'logo.png')) as fp:
            message.attach("logo.png", "image/png", fp.read(), 'inline', headers={'Content-ID': content_id})

    def _build_message(self, subject: str, sender: str, recipients: list[str], reply_to: str,
                       template: str, params: dict[str, str],
                       cc: list[str] | None = None) -> Message:
        logo_content_id = self._generate_content_id()
        html = render_template(f'mail/{template}.html', parameters=params, logo_cdi=logo_content_id)
        message = MailExtension.MultipartRelatedMessage(
            subject=subject.format(**(params or {})),
            sender=sender,
            recipients=recipients,
            reply_to=reply_to,
            html=html,
            cc=cc or []
        )
        self._attach_logo(message, logo_content_id)
        return message
