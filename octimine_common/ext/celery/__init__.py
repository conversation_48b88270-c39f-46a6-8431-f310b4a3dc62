from celery import Celery
from octimine_common.request_ctx import RequestContext, optional_ctx, set_ctx
from flask import has_app_context
from celery.signals import setup_logging


@setup_logging.connect
def avoid_logging_reconfiguration(*args, **kwags):
    """ This registers an empty custom logging configuration function in order to prevent celery to re-configure logs.
    Logs are only reconfigured by celery if there are no custom logging configurators
    """
    pass


class CeleryExtension:

    def __init__(self, app=None):
        self._celery = Celery()
        if app:
            self.init_app(app)

    def init_app(self, app):
        self._celery.main = app.import_name
        self._celery.conf['result_backend'] = app.config['CELERY_RESULT_BACKEND']
        self._celery.conf['broker_url'] = app.config['CELERY_BROKER_URL']
        self._celery.conf.update(app.config['CELERY_ADDITIONAL_CONFIG'])

        class ContextTask(self._celery.Task):
            typing = False

            def apply_async(self, args=None, kwargs=None, **options):
                args = args or ()
                kwargs = kwargs or {}
                def exec():
                    if optional_ctx:
                        kwargs['__ctx__'] = optional_ctx.to_dict()
                    return super(ContextTask, self).apply_async(args=args, kwargs=kwargs, **options)

                if not has_app_context():
                    with app.app_context():
                        return exec()
                else:
                    return exec()

            def __call__(self, *args, **kwargs):
                def exec():
                    current_ctx = kwargs.pop('__ctx__', None)
                    if current_ctx:
                        set_ctx(RequestContext.from_dict(current_ctx))
                    return self.run(*args, **kwargs)

                if not has_app_context():
                    with app.app_context():
                        return exec()
                else:
                    return exec()


        self._celery.Task = ContextTask

        if not hasattr(app, "extensions"):
            app.extensions = {}
        app.extensions["celery"] = self

    def __getattr__(self, name):
        return getattr(self._celery, name)
