from typing import List, Callable


class MessageBus:

    def __init__(self):
        self._app = None
        self._event_handlers = {}

    def init_app(self, app):
        if not hasattr(app, 'extensions'):
            app.extensions = {}
        self._app = app
        app.extensions['message_bus'] = self

    def register_handlers(self, event_name: str, handlers: List[Callable]):
        self._event_handlers[event_name] = handlers

    def register_handler(self, event_name: str, handler: Callable):
        if event_name not in self._event_handlers:
            self._event_handlers[event_name] = []
        self._event_handlers[event_name].append(handler)

    def clean_handlers(self):
        self._event_handlers = {}

    def emit(self, event_name, *args, **kwargs):
        for handler in self._event_handlers.get(event_name, []):
            handler(*args, **kwargs)
