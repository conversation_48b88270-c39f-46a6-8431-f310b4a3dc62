# Third party imports
from flask import request
from flask_jwt_extended.exceptions import NoAuthorizationError, InvalidHeaderError
from flask_jwt_extended.config import config
from jwt import decode

# Local imports
from .data import access_token, access_jti, refresh_jti, manager_access_jti


def decode_jwt_from_headers():
    header_name = config.header_name
    header_type = config.header_type

    jwt_header = request.headers.get(header_name, None)
    if not jwt_header:
        raise NoAuthorizationError("Missing {} Header".format(header_name))

    parts = jwt_header.split()
    if not header_type:
        if len(parts) != 1:
            msg = "Bad {} header. Expected value '<JWT>'".format(header_name)
            raise InvalidHeaderError(msg)
        encoded_token = parts[0]
    else:
        if parts[0] != header_type or len(parts) != 2:
            msg = "Bad {} header. Expected value '{} <JWT>'".format(
                header_name,
                header_type
            )
            raise InvalidHeaderError(msg)
        encoded_token = parts[1]

    if encoded_token != access_token:
        NoAuthorizationError()
    return encoded_token, None


def token_in_blocklist(jwt_headers, jwt_payload):
    tokens = {
        'access': [access_jti, manager_access_jti],
        'refresh': [refresh_jti]
    }
    jti = jwt_payload['jti']
    type_ = jwt_payload['type']
    return jti not in tokens[type_]


def decode_jwt(algorithms, allow_expired, audience, csrf_value, encoded_token, identity_claim_key, issuer, leeway,
               secret, verify_aud, verify_sub):
    options = dict(verify_exp=False)
    data = decode(encoded_token, secret, algorithms=['HS256'], audience=audience,
                  leeway=leeway, options=options)
    return data
