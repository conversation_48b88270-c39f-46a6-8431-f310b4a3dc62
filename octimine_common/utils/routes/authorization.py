from functools import wraps
from http import HTTPStatus
from typing import List, Dict, Callable

from flask import abort
from octimine_common.jwt_utils import get_jwt_claims


class Authorization:

    @classmethod
    def roles_required(cls, any_roles=None):
        if any_roles is None:
            any_roles = []

        def decorator(fn):
            @wraps(fn)
            def wrapper(*args, **kwargs):
                claims = get_jwt_claims()
                if not claims or not any(all(claims.get(r) for r in role) if isinstance(role, list)
                                         else claims.get(role) for role in any_roles):
                    abort(HTTPStatus.FORBIDDEN)
                return fn(*args, **kwargs)

            return wrapper

        return decorator

    @classmethod
    def forbidden(cls, checks: List[Dict[str, List[any]]]) -> Callable:
        def decorator(fn: Callable) -> Callable:
            @wraps(fn)
            def wrapper(*args, **kwargs):
                claims = get_jwt_claims()
                if not claims or any(claims.get(check['field']) in check['values'] for check in checks):
                    abort(HTTPStatus.FORBIDDEN)
                return fn(*args, **kwargs)

            return wrapper

        return decorator
