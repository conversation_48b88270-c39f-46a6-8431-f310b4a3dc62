from typing import Optional

from marshmallow import ValidationError
from sqlalchemy import select, func

from octimine_common.util import Page, Sort


class ModelQuery:

    def __init__(self, db, model_class):
        self._db = db
        self._query = select(model_class)
        self._model_class = model_class

    def select(self, *columns):
        if columns:
            self._query = self._query.with_only_columns(*columns)
        return self

    def distinct(self):
        self._query = self._query.distinct()
        return self

    def filter(self, *args):
        if args:
            self._query = self._query.where(*args)
        return self

    def filter_by(self, **kwargs):
        if kwargs:
            self._query = self._query.filter_by(**kwargs)
        return self

    def paginate(self, page: Page):
        self._query = paginate(db=self._db, query=self._query, page=page)
        return self

    def sort(self, sort: Sort):
        self._query = sort_by(model_class=self._model_class, query=self._query, sort=sort)
        return self

    def all(self):
        return self._db.session.execute(self._query).scalars().all()

    def all_tuples(self):
        return self._db.session.execute(self._query).all()

    def first(self):
        return self._db.session.execute(self._query).scalars().first()

    def join(self, target_table, onclause=None, *, isouter: bool = False, full: bool = False):
        self._query = self._query.join(target_table, onclause=onclause, isouter=isouter, full=full)
        return self


def paginate(*, db, query, page: Optional[Page] = None):
    if page:
        page.total_hits = db.session.scalar(select(func.count()).select_from(query.subquery()))
        query = query.limit(page.size).offset(page.from_item)
    return query


def sort_by(*, query, sort: Optional[Sort] = None, model_class=None):
    if sort and sort.is_defined():
        try:
            if model_class:
                field = model_class.__table__.columns[sort.sort_column]
            else:
                field = getattr(query.selected_columns, sort.sort_column)
            sort_param = getattr(field, sort.sort_order)()
            query = query.order_by(sort_param)
        except AttributeError:
            raise ValidationError("Invalid sort parameters")
    return query
