from typing import Optional
from octimine_common.exceptions import NotFoundException, ConflictException, InternalServerException
from octimine_common.util import Page, Sort
from sqlalchemy import inspect
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from .filter import Filters
from .queries import sort_by, paginate


class BaseModel:
    __db__ = None

    def is_persisted(self):
        return inspect(self).persistent

    def merge(self, other):
        self.__db__.session.merge(other)

    def save(self):
        self._before_save()
        self._save()

    def _before_save(self):
        pass

    def _save(self):
        if not self.is_persisted():
            self.__db__.session.add(self)
        try:
            self.__db__.session.commit()
        except IntegrityError as e:
            self.__db__.session.rollback()
            raise ConflictException(e.orig.args[1])
        except SQLAlchemyError as sql_err:
            self.__db__.session.rollback()
            raise InternalServerException(str(sql_err))

    def delete(self):
        if self.is_persisted():
            self.__db__.session.delete(self)
            self.__db__.session.commit()

    @classmethod
    def get_one(cls, **kwargs):
        result = cls._get_one(**kwargs)
        cls._after_get_one(result)
        return result

    @classmethod
    def _get_one(cls, **kwargs):
        result = cls.__db__.session.scalars(cls.__db__.select(cls).filter_by(**kwargs)).first()
        if result is None:
            raise NotFoundException()
        return result

    @classmethod
    def _after_get_one(cls, result):
        pass

    @classmethod
    def get_all(cls, page: Optional[Page], sort: Optional[Sort], filters: Optional[Filters], *args, **kwargs):
        kwargs = kwargs or {}
        cls._before_get_all(page, sort, filters, kwargs)
        return cls._get_all(page, sort, filters, *args, **kwargs)

    @classmethod
    def _before_get_all(cls, page: Optional[Page], sort: Optional[Sort], filters: Optional[Filters], kwargs):
        pass

    @classmethod
    def _get_all(cls, page: Optional[Page], sort: Optional[Sort], filters: Optional[Filters], *args, **kwargs):
        statement = cls.__db__.select(cls)
        if args:
            statement = statement.where(*args)
        if kwargs:
            statement = statement.filter_by(**kwargs)
        if filters:
            for f in filters.values:
                statement = statement.where(f)
        statement = sort_by(query=statement, sort=sort)
        statement = paginate(db=cls.__db__, query=statement, page=page)
        return cls.__db__.session.scalars(statement).all()

    @classmethod
    def delete_by(cls, **kwargs):
        cls.__db__.session.query(cls).filter_by(**kwargs).delete(synchronize_session=False)
        cls.__db__.session.commit()
