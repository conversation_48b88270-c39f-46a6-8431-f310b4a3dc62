import re
from enum import Enum
from marshmallow import fields, post_load, ValidationError, Schema
from marshmallow.utils import resolve_field_instance
from flask import request
from marshmallow import EXCLUDE


class FilterOperand(Enum):

    EQ = "eq"
    NE = "ne"
    LT = "lt"
    LTE = "lte"
    GT = "gt"
    GTE = "gte"
    LIKE = "like"
    CASE_INSENSITIVE_LIKE = "ilike"
    IN = "in"
    IS = "is"

    @classmethod
    def all(cls):
        return [o for o in FilterOperand]

    @classmethod
    def values(cls):
        return [o.value for o in FilterOperand]

    @classmethod
    def from_str(cls, value: str):
        for operand in FilterOperand:
            if operand.value == value:
                return operand
        return FilterOperand.EQ


class _FilterOperandValueParser:

    # Pattern ends up like: ((op_1|...|op_i|...|op_n):).*
    _operand_value_pattern = re.compile("((" + "|".join(FilterOperand.values()) + "):)(.*)")

    @classmethod
    def parse(cls, value: str):
        """
        Returns the operand and value that result from a raw value

        :param value: The raw value with form operand:value
        :return: A tuple containing the operand and the value. Returns EQ if no match is found
        """
        match = cls._operand_value_pattern.match(value)
        if match:
            return FilterOperand.from_str(match.group(2)), match.group(3)
        return FilterOperand.EQ, value


class DefaultOperandDeserializer:

    @classmethod
    def deserialize(cls, field, value):
        return field.deserialize(value)


class InOperandDeserializer:

    @classmethod
    def deserialize(cls, field, value):
        return [field.deserialize(v) for v in value.split(',')]


class IsOperandDeserializer:

    @classmethod
    def deserialize(cls, _, value):
        if value != "null":
            raise ValidationError("is operand only supports the 'null' value")
        return None


class Filters:
    """
    Maps a series of request parameters into filters with the help of a FilterSchema subclass.
    To use it just create your own schema subclassing FilterSchema, and pass it to the constructor
    of Filters. You can access the different filters through the 'values' property

    Example:

        filters = Filters(MyFiltersSchema)
        filters.values # Contains the list of filters detected from the request

    """

    @staticmethod
    def from_request(schema_class, request):
        return Filters(schema_class, {k: request.args.getlist(k) for k in request.args})

    def __init__(self, schema_class, provided_args=None):
        self.schema = schema_class(unknown=EXCLUDE)
        # TODO: Let's get rid of request dependency in this constructor and use from_request instead
        # That way we can init filters from places outside request context (i.e. celery tasks, etc...)
        args = provided_args if provided_args is not None else {k: request.args.getlist(k) for k in self.schema.fields}
        self._values = self.schema.load(args)

    def add_filter(self, field, filter_value):
        self._values.extend(self.schema.load({field: filter_value}))

    @property
    def values(self):
        return self._values


class FilterField(fields.Field):
    """Field that represent a filter value. This field type should be used in all schemas
    that inherit the FilterSchema class.

    Filter values are composed by both operands and values.
    The following operands are supported:
    - eq: Equals
    - ne: Not equals
    - lt: Less than
    - lte: Less than or equal
    - gt: Greater than
    - gte: Greater than or equal
    - like: For partial matching in strings, use % wildcards
    """

    _supported_operands = {
        fields.DateTime: FilterOperand.all(),
        fields.Date: FilterOperand.all(),
        fields.Integer: FilterOperand.all(),
        fields.Float: FilterOperand.all(),
        fields.Decimal: FilterOperand.all(),
        fields.Number: FilterOperand.all(),
        fields.Boolean: [FilterOperand.EQ, FilterOperand.NE],
        fields.String: [FilterOperand.LIKE, FilterOperand.CASE_INSENSITIVE_LIKE,
                        FilterOperand.EQ, FilterOperand.NE, FilterOperand.IN],
        fields.Enum: FilterOperand.all()
    }

    _operand_deserializers = {
        FilterOperand.IN: InOperandDeserializer,
        FilterOperand.IS: IsOperandDeserializer
    }

    def __init__(self, cls_or_instance, *, model_field, default_value=None, operands_map=None, available_operands=None,
                 **kwargs):
        super().__init__(**kwargs)
        self.model_field = model_field
        self.default_value = default_value
        self.operands_map = operands_map
        self._inner = resolve_field_instance(cls_or_instance)
        self._available_operands = available_operands or FilterField._supported_operands.get(type(self._inner))

    def _deserialize(self, val, attr, data, **kwargs):
        if not isinstance(val, list):
            values = [val]
        else:
            values = val

        if not values and self.default_value:
            return [(FilterOperand.EQ, self.default_value() if callable(self.default_value) else self.default_value)]

        if not values and self.required:
            raise ValidationError("It must be specified as a filter")

        result = []
        for value in values:
            operand, value = _FilterOperandValueParser.parse(value)
            if operand not in self._available_operands:
                raise ValidationError(f"Operand {operand.value} not supported")
            deserializer = self._operand_deserializers.get(operand, DefaultOperandDeserializer)
            result.append((operand, deserializer.deserialize(self._inner, value)))
        return result


class FilterSchema(Schema):
    """ Base schema class for filtering.

    Validates and builds a set of ready-to-use filters for the model layer.
    Inherit from this class and specify filter fields with the FilterField class.

    Example:
        class MyFilterSchema(FilterSchema):
            created_at = FilterField(fields.DateTime, model_field=MyModel.created_at)
            name = FilterField(fields.String, model_field=MyModel.name)
    """

    @post_load
    def build(self, data, **kwargs):
        results = []
        for field in data:
            model_field = self.fields[field].model_field
            operands_map = self.fields[field].operands_map
            for operand, value in data[field]:
                if operands_map and operand in operands_map:
                    filter_ = operands_map[operand](model_field, value)
                elif operand == FilterOperand.LT:
                    filter_ = model_field < value
                elif operand == FilterOperand.LTE:
                    filter_ = model_field <= value
                elif operand == FilterOperand.GT:
                    filter_ = model_field > value
                elif operand == FilterOperand.GTE:
                    filter_ = model_field >= value
                elif operand == FilterOperand.NE:
                    filter_ = model_field != value
                elif operand == FilterOperand.LIKE:
                    filter_ = model_field.like(value)
                elif operand == FilterOperand.CASE_INSENSITIVE_LIKE:
                    filter_ = model_field.ilike(value)
                elif operand == FilterOperand.IN:
                    filter_ = model_field.in_(value)
                else:
                    filter_ = model_field == value

                if filter_ is not None:
                    results.append(filter_)
        return results
