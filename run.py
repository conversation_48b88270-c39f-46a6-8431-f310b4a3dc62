from os import getenv
from flask import request, g

from octimine_common.flask_utils import user_id_from_headers
from app.app import create_app

app = create_app(getenv("FLASK_ENV", "development"), getenv("FLASK_OVERWRITE_CONFIG", ""))


@app.before_request
def log_before_request():
    g.user_id = user_id_from_headers(request.headers.get('Authorization'))
    app.logger.info('>>> REQUEST: %s %s %s' % (request.remote_addr, request.method, request.full_path))


@app.after_request
def after_request(response):
    app.logger.info('<<< RESPONSE: %s %s %s %s' %
                    (response.status_code, response.content_type, response.content_length,
                     response.json if response.status_code == 401 else ''))
    return response


if __name__ == '__main__':
    app.run()
