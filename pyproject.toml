[project]
name = "octimine-backend"
version = "25.12.0"
description = "Octimine REST API"
authors = [
    {name = "Octimine Engineering",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<3.13"  # python 3.13 has issues with huggingface

[build-system]
requires = ["poetry-core>=1.0.0,<2.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
package-mode = false

[[tool.poetry.source]]
name = "pytorch"
url = "https://download.pytorch.org/whl/cpu"
priority = "supplemental"

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
flask = "==3.*"
flask-jwt-extended = "==4.*"
flask-marshmallow = "==1.*"
flask_redis = "==0.*"
flask-sqlalchemy = "==3.*"
flask-migrate = "==4.*"
flask-bcrypt = "==1.*"
flask-mail = "<0.11"
flask-cors = "==5.*"
sqlalchemy = "==2.*"
marshmallow = "==3.*"
marshmallow-sqlalchemy = "==1.4.2"
pika = "==1.*"
ply = "==3.*"
PyMySQL = "==1.*"
pycryptodome = "==3.*"
pyjwt = "==2.*"
redis = "==4.*"
requests = "==2.*"
elasticsearch = ">=8.12.0,<9"
pyyaml = "==6.*"
neo4j-driver = "==4.*"
celery = "==5.*"
numpy = "==1.*"
stemming = "==1.*"
python-dateutil = "==2.*"
pandas = "==1.*"
deepl = "==1.*"
lxml = "==4.*"
xmlsec = "<=1.3.13"
babel = "==2.*"
cryptography = "==42.*"
iso3166 = "==2.*"
pyotp = "==2.*"
pycountry = "*"
python3-saml = "==1.*"
openpyxl = "==3.*"
xlrd = "==2.*"
odfpy = "==1.*"
bs4 = "==0.*"
kneed = "==0.*"
boto3 = "==1.*"
openai = "==1.*"
async-timeout = "==5.*"
beautifulsoup4 = "==4.*"
xlsxwriter = "==3.*"
reportlab = "==4.*"
fasttext-langdetect = "==1.*"
tiktoken = "==0.*"
hubspot-api-client = "==8.*"
email-validator = "==2.*"
opencv-python-headless = "==4.*"

[tool.poetry.group.dev.dependencies]
flake8 = "*"
pytest = "*"
pytest-flask = "*"
pytest-mock = "*"
pytest-cov = "*"
requests = "*"
lorem = "*"
fakeredis = "*"
